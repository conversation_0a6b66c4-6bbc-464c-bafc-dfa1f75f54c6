# 工作时间追踪小程序

一个专业的微信小程序，用于工作时间追踪和收入管理。采用现代化的模块化架构，提供完整的工作履历管理、时间追踪、收入统计等功能。

## 项目特点

- 🏗️ **现代化架构** - 采用分层架构设计，模块化开发
- 📱 **微信小程序** - 原生微信小程序开发，性能优异
- ☁️ **云开发支持** - 基于微信云开发，提供完整的后端服务
- 💾 **智能存储** - 数据压缩存储，节省空间
- 🔧 **工具丰富** - 完整的工具类库，提高开发效率
- 📊 **数据可视化** - 多种图表展示，数据一目了然
- 🎨 **界面美观** - 现代化UI设计，用户体验优秀

## 功能特性

### 核心功能
- ✅ 工作履历管理（增删改查）
- ✅ 时间追踪记录
- ✅ 收入统计分析
- ✅ 多仪表盘展示
- ✅ 数据导入导出
- ✅ 用户签到系统
- ✅ 积分商店系统
- ✅ 摸鱼状态追踪
- ✅ 友情应用推荐
- ✅ 邀请好友功能
- ✅ 反馈意见系统

### 技术特性
- ✅ 模块化架构设计
- ✅ 数据压缩存储
- ✅ 缓存机制优化
- ✅ 防抖保存机制
- ✅ 完善的错误处理
- ✅ 丰富的工具类库
- ✅ API客户端封装
- ✅ 重试机制支持

## 项目结构

```
mp-md/
├── cloudfunctions/          # 云函数目录
│   └── cloud-functions/     # 主云函数
│       ├── api/             # API接口层
│       ├── db/              # 数据库操作层
│       ├── utils/           # 工具函数层
│       └── index.js         # 云函数入口
├── miniprogram/             # 小程序目录
│   ├── core/                # 核心模块
│   │   ├── api/             # API客户端
│   │   ├── managers/        # 数据管理器
│   │   └── services/        # 业务服务
│   ├── utils/               # 工具类
│   ├── pages/               # 页面
│   ├── components/          # 组件
│   └── docs/                # 小程序文档
└── docs/                    # 项目文档
```

## 快速开始

### 环境要求
- 微信开发者工具
- Node.js 14+
- 微信云开发环境

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd mp-md
   ```

2. **配置云函数**
   ```bash
   cd cloudfunctions/cloud-functions
   npm install
   ```

3. **配置小程序**
   - 使用微信开发者工具打开项目
   - 配置云开发环境ID
   - 上传并部署云函数

4. **启动项目**
   - 在微信开发者工具中编译运行

## 文档说明

### 云函数文档
- [云函数架构说明](./cloudfunctions/cloud-functions/README.md) - 详细的云函数架构和API说明

### 小程序文档
- [项目结构说明](./miniprogram/docs/project-structure.md) - 小程序项目结构详解
- [使用指南](./miniprogram/docs/usage-guide.md) - 数据管理使用指南
- [开发指南](./miniprogram/docs/development-guide.md) - 开发规范和最佳实践
- [快速参考](./miniprogram/docs/quick-reference.md) - 常用功能快速参考

## 技术栈

### 前端
- 微信小程序原生开发
- 模块化ES6+语法
- 组件化开发

### 后端
- 微信云开发
- 云函数 (Node.js)
- 云数据库 (MongoDB)
- 云存储

### 工具库
- Deflate数据压缩
- 缓存管理
- 重试机制
- 数据验证

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 参考文档

- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)

