/**
 * 数据归档相关API
 * 处理用户数据的归档和回滚功能
 */

const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
})

const db = cloud.database()

/**
 * 归档当前数据
 * @param {Object} event - 事件对象
 * @returns {Object} 归档结果
 */
async function archiveUserData(event) {
  try {
    // 获取用户身份信息
    const wxContext = cloud.getWXContext()
    const userId = wxContext.OPENID

    const { data, version, reason = 'auto_backup' } = event.data

    if (!userId || !data) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 获取当前时间
    const now = new Date()

    // 准备归档数据
    const archiveData = {
      userId: userId,
      originalData: data,
      version: version || 1,
      archiveReason: reason,
      archiveTime: now,
      createTime: now,
      // 保留7天
      expireTime: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    }

    // 保存到归档集合
    const result = await db.collection('userDataArchive').add({
      data: archiveData
    })

    console.log('数据归档成功:', result._id)

    return {
      success: true,
      archiveId: result._id,
      archiveTime: now,
      message: '数据归档成功'
    }

  } catch (error) {
    console.error('归档数据失败:', error)
    return {
      success: false,
      message: '归档数据失败: ' + error.message
    }
  }
}

/**
 * 获取用户的归档列表
 * @param {Object} event - 事件对象
 * @returns {Object} 归档列表
 */
async function getUserArchives(event) {
  try {
    // 获取用户身份信息
    const wxContext = cloud.getWXContext()
    const userId = wxContext.OPENID

    const { limit = 10 } = event.data

    if (!userId) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }

    // 查询用户的归档数据，按时间倒序
    const result = await db.collection('userDataArchive')
      .where({
        userId: userId,
        expireTime: db.command.gt(new Date()) // 未过期的
      })
      .orderBy('archiveTime', 'desc')
      .limit(limit)
      .get()

    // 格式化归档列表
    const archives = result.data.map(archive => ({
      archiveId: archive._id,
      archiveTime: archive.archiveTime,
      version: archive.version,
      reason: archive.archiveReason,
      expireTime: archive.expireTime,
      // 不返回完整数据，只返回摘要信息
      dataSummary: {
        workHistoryCount: archive.originalData.workHistory?.length || 0,
        timeTrackingCount: archive.originalData.timeTracking?.length || 0,
        settingsKeys: Object.keys(archive.originalData.settings || {})
      }
    }))

    return {
      success: true,
      archives: archives,
      total: archives.length
    }

  } catch (error) {
    console.error('获取归档列表失败:', error)
    return {
      success: false,
      message: '获取归档列表失败: ' + error.message
    }
  }
}

/**
 * 回滚到指定归档
 * @param {Object} event - 事件对象
 * @returns {Object} 回滚结果
 */
async function rollbackToArchive(event) {
  try {
    // 获取用户身份信息
    const wxContext = cloud.getWXContext()
    const userId = wxContext.OPENID

    const { archiveId } = event.data

    if (!userId || !archiveId) {
      return {
        success: false,
        message: '缺少必要参数'
      }
    }

    // 获取归档数据
    const archiveResult = await db.collection('userDataArchive')
      .doc(archiveId)
      .get()

    if (!archiveResult.data) {
      return {
        success: false,
        message: '归档数据不存在'
      }
    }

    const archiveData = archiveResult.data

    // 验证归档是否属于当前用户
    if (archiveData.userId !== userId) {
      return {
        success: false,
        message: '无权访问此归档'
      }
    }

    // 检查归档是否已过期
    if (new Date() > new Date(archiveData.expireTime)) {
      return {
        success: false,
        message: '归档已过期'
      }
    }

    // 先备份当前数据
    const currentDataResult = await db.collection('userData')
      .where({ userId: userId })
      .get()

    if (currentDataResult.data.length > 0) {
      // 归档当前数据
      await archiveUserData({
        data: {
          data: currentDataResult.data[0].data,
          version: currentDataResult.data[0].version || 1,
          reason: 'before_rollback'
        }
      })
    }

    // 准备回滚数据
    const rollbackData = {
      userId: userId,
      data: archiveData.originalData,
      version: (archiveData.version || 0) + 1,
      lastModified: new Date(),
      rollbackFrom: archiveId,
      rollbackTime: new Date()
    }

    // 更新或插入用户数据
    const updateResult = await db.collection('userData')
      .where({ userId: userId })
      .get()

    if (updateResult.data.length > 0) {
      // 更新现有数据
      await db.collection('userData')
        .doc(updateResult.data[0]._id)
        .update({
          data: rollbackData
        })
    } else {
      // 插入新数据
      await db.collection('userData').add({
        data: rollbackData
      })
    }

    console.log('数据回滚成功:', archiveId)

    return {
      success: true,
      rollbackTime: new Date(),
      version: rollbackData.version,
      message: '数据回滚成功'
    }

  } catch (error) {
    console.error('数据回滚失败:', error)
    return {
      success: false,
      message: '数据回滚失败: ' + error.message
    }
  }
}

/**
 * 清理过期归档
 * @returns {Object} 清理结果
 */
async function cleanExpiredArchives() {
  try {
    const now = new Date()

    // 删除过期的归档
    const result = await db.collection('userDataArchive')
      .where({
        expireTime: db.command.lt(now)
      })
      .remove()

    console.log('清理过期归档:', result.stats.removed)

    return {
      success: true,
      removedCount: result.stats.removed,
      message: '清理完成'
    }

  } catch (error) {
    console.error('清理过期归档失败:', error)
    return {
      success: false,
      message: '清理失败: ' + error.message
    }
  }
}

module.exports = {
  archiveUserData,
  getUserArchives,
  rollbackToArchive,
  cleanExpiredArchives
}
