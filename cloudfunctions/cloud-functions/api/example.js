/**
 * 示例API - 演示如何使用新的API计数方法
 */

const { incrementApiCallCount } = require('../utils/auth')
const { success, error, wrapAsync } = require('../utils/response')

/**
 * 示例API方法
 * 演示如何在API中使用incrementApiCallCount
 */
exports.exampleApi = wrapAsync(async (params = {}) => {
  try {
    // 在API开始时增加调用计数并检查限制
    await incrementApiCallCount()
    
    // 执行实际的API逻辑
    const result = {
      message: '这是一个示例API',
      timestamp: new Date().toISOString(),
      params: params
    }
    
    return success(result, 'API调用成功')
  } catch (error) {
    // 如果是API限制错误，直接返回错误信息
    if (error.message.includes('已达上限')) {
      return {
        success: false,
        message: error.message,
        code: 'API_LIMIT_EXCEEDED'
      }
    }
    
    // 其他错误
    throw error
  }
})

/**
 * 另一个示例API方法
 * 演示不同的错误处理方式
 */
exports.anotherExampleApi = wrapAsync(async (params = {}) => {
  try {
    // 增加API调用计数
    const countResult = await incrementApiCallCount()
    
    // 可以使用计数结果中的信息
    console.log('当前用户VIP状态:', countResult.isVip)
    console.log('API调用限制:', countResult.limits)
    console.log('当前调用次数:', countResult.current)
    
    // 执行API逻辑
    const result = {
      message: '另一个示例API',
      userIsVip: countResult.isVip,
      apiLimits: countResult.limits,
      currentUsage: countResult.current
    }
    
    return success(result, 'API调用成功')
  } catch (error) {
    // 统一的错误处理
    return error(error.message || 'API调用失败')
  }
})
