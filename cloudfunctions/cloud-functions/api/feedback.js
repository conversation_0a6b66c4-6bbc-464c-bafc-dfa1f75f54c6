/**
 * 反馈意见相关API
 */

const feedbackDB = require('../db/feedback')
const { getCurrentUser } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')

/**
 * 提交反馈
 */
exports.submitFeedback = wrapAsync(async (params = {}) => {
  validateRequired(params, ['category', 'content'])

  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data
  const { category, email, content } = params

  // 验证反馈内容
  if (!content || content.trim().length === 0) {
    return error('反馈内容不能为空')
  }

  if (content.length > 1000) {
    return error('反馈内容不能超过1000字')
  }

  // 验证分类（虽然不做严格校验，但确保不为空）
  if (!category || category.trim().length === 0) {
    return error('请选择反馈分类')
  }

  // 创建反馈记录
  const feedbackData = {
    userId: user._id,
    category: category.trim(),
    email: email ? email.trim() : '',
    content: content.trim()
  }

  const result = await feedbackDB.createFeedback(feedbackData)

  if (!result.success) {
    return error(result.message || '提交反馈失败')
  }

  return success(result.data, '反馈提交成功，我们会尽快处理')
})

/**
 * 获取用户反馈列表
 */
exports.getUserFeedbacks = wrapAsync(async (params = {}) => {
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data
  const { page = 1, limit = 20 } = params
  const skip = (page - 1) * limit

  // 获取反馈列表
  const feedbacksResult = await feedbackDB.getFeedbacksByUserId(user._id, {
    limit: parseInt(limit),
    skip: parseInt(skip)
  })

  if (!feedbacksResult.success) {
    return error(feedbacksResult.message || '获取反馈列表失败')
  }

  // 格式化返回数据
  const feedbacks = feedbacksResult.data.map(feedback => ({
    _id: feedback._id,
    category: feedback.category,
    email: feedback.email,
    content: feedback.content,
    status: feedback.status,
    reply: feedback.reply,
    replyTime: feedback.replyTime,
    createTime: feedback.createTime,
    updateTime: feedback.updateTime,
    // 添加格式化的时间文本
    createTimeText: formatDateTime(feedback.createTime),
    replyTimeText: feedback.replyTime ? formatDateTime(feedback.replyTime) : null,
    // 添加状态文本
    statusText: getStatusText(feedback.status),
    // 添加内容摘要
    contentSummary: getContentSummary(feedback.content)
  }))

  // 检查是否还有更多数据
  const hasMore = feedbacks.length === parseInt(limit)

  return success({
    feedbacks,
    hasMore,
    total: feedbacks.length
  }, '获取反馈列表成功')
})

/**
 * 删除反馈
 */
exports.deleteFeedback = wrapAsync(async (params = {}) => {
  validateRequired(params, ['feedbackId'])

  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data
  const { feedbackId } = params

  // 执行软删除
  const result = await feedbackDB.softDeleteFeedback(feedbackId, user._id)

  if (!result.success) {
    return error(result.message || '删除反馈失败')
  }

  return success(null, '反馈删除成功')
})

/**
 * 获取反馈统计
 */
exports.getFeedbackStats = wrapAsync(async (params = {}) => {
  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 获取统计数据
  const statsResult = await feedbackDB.getFeedbackStats(user._id)

  if (!statsResult.success) {
    return error(statsResult.message || '获取统计数据失败')
  }

  return success(statsResult.data, '获取统计数据成功')
})

/**
 * 格式化日期时间
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
function formatDateTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}`
}

/**
 * 获取状态文本
 * @param {string} status - 状态
 * @returns {string} 状态文本
 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'replied': '已回复',
    'closed': '已关闭'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 获取内容摘要
 * @param {string} content - 完整内容
 * @returns {string} 内容摘要
 */
function getContentSummary(content) {
  if (!content) return ''
  
  // 如果内容长度小于50字符，直接返回
  if (content.length <= 50) {
    return content
  }
  
  // 截取前50个字符并添加省略号
  return content.substring(0, 50) + '...'
}
