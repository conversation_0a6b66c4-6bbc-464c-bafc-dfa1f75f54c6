/**
 * 用户相关API
 */

const usersDB = require('../db/users')
const vipRecordsDB = require('../db/vip-records')
const redemptionCodesDB = require('../db/redemption-codes')
const { getCurrentUser, ensureUserExists, incrementApiCallCount } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')

/**
 * 获取用户信息
 * 根据 openid 获取用户信息，如果不存在则自动创建
 * 同时刷新用户 VIP 状态
 */
exports.getUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event

  // 目前getUserInfo不需要处理任何用户提交的数据
  // 只传递版本信息用于用户创建和版本更新
  const userCreationData = {
    version: version
  }

  const result = await ensureUserExists(userCreationData)

  if (!result.success) {
    return result
  }

  // 如果是新用户，添加VIP福利
  if (result.isNewUser) {
    try {
      console.log('新用户注册，添加VIP福利:', result.data._id)

      const vipResult = await vipRecordsDB.addNewUserVipBenefit(result.data._id, 14)

      if (vipResult.success) {
        console.log('新用户VIP福利添加成功')

        // 更新用户VIP状态
        const vipExpiryResult = await vipRecordsDB.calculateUserVipExpiry(result.data._id)
        if (vipExpiryResult.success && vipExpiryResult.data.isVip) {
          await usersDB.updateByOpenid(result.data.openid, {
            'vip.status': true,
            'vip.expiredAt': vipExpiryResult.data.expireDate
          })

          // 更新返回的用户数据
          result.data.vip = {
            status: true,
            expiredAt: vipExpiryResult.data.expireDate
          }
        }
      } else {
        console.error('新用户VIP福利添加失败:', vipResult.message)
      }
    } catch (error) {
      console.error('处理新用户VIP福利时出错:', error)
    }
  } else {
    // 对于现有用户，刷新VIP状态
    try {
      const vipExpiryResult = await vipRecordsDB.calculateUserVipExpiry(result.data._id)
      if (vipExpiryResult.success) {
        const isVip = vipExpiryResult.data.isVip

        // 检查VIP状态是否有变化
        const currentVipStatus = result.data.vip?.status || false

        if (isVip !== currentVipStatus) {
          await usersDB.updateByOpenid(result.data.openid, {
            'vip.status': isVip,
            'vip.expiredAt': isVip ? vipExpiryResult.data.expireDate : null
          })

          // 更新返回的用户数据
          result.data.vip = {
            status: isVip,
            expiredAt: isVip ? vipExpiryResult.data.expireDate : null
          }
        }
      }
    } catch (error) {
      console.error('刷新用户VIP状态时出错:', error)
    }

    // 更新当前使用版本
    try {
      const currentVersion = version
      if (currentVersion) {
        const userVersion = result.data.version || {}

        // 如果版本有变化，则更新
        if (userVersion.currentVersion !== currentVersion) {
          const versionUpdateData = {
            'version.currentVersion': currentVersion,
            'version.lastVersionUpdateAt': new Date()
          }

          await usersDB.updateByOpenid(result.data.openid, versionUpdateData)

          // 更新返回的用户数据
          result.data.version = {
            ...userVersion,
            currentVersion: currentVersion,
            lastVersionUpdateAt: new Date()
          }

          console.log(`[用户版本] 更新用户 ${result.data.openid} 当前版本从 ${userVersion.currentVersion} 到 ${currentVersion}`)
        }
      }
    } catch (error) {
      console.error('更新用户版本信息时出错:', error)
    }
  }

  // 获取额外的统计数据
  try {
    // 获取兑换码数量
    const codesCountResult = await redemptionCodesDB.getUserCodesCount(result.data._id)
    if (codesCountResult.success) {
      result.data.redemptionCodesCount = codesCountResult.data.count
    } else {
      result.data.redemptionCodesCount = 0
    }
  } catch (error) {
    console.error('获取兑换码数量失败:', error)
    result.data.redemptionCodesCount = 0
  }

  return success(result.data, result.isNewUser ? '用户创建成功，已赠送14天VIP会员' : '获取用户信息成功')
})

/**
 * 更新用户信息
 */
exports.updateUserInfo = wrapAsync(async (event = {}) => {
  const { data = {}, version } = event
  validateRequired(data, [])

  // 记录版本信息用于统计分析
  if (version) {
    console.log(`[用户更新] 版本 ${version} 的用户更新信息`)
  }

  const userResult = await getCurrentUser()
  if (!userResult.success) {
    return userResult
  }

  const user = userResult.data

  // 更新的数据
  const updateData = {}

  // 更新：昵称
  if (data.nickname !== undefined) {
    if (typeof data.nickname === 'string' && data.nickname.length <= 50) {
      updateData.nickname = data.nickname.trim()
    } else {
      return error('昵称格式不正确或长度超过限制')
    }
  }

  // 更新：头像
  if (data.avatar !== undefined) {
    if (typeof data.avatar === 'string' && data.avatar.length <= 500) {
      // 简单验证URL格式
      if (data.avatar === '' || /^https?:\/\/.+/.test(data.avatar)) {
        updateData.avatar = data.avatar
      } else {
        return error('头像URL格式不正确')
      }
    } else {
      return error('头像URL长度超过限制')
    }
  }

  // 更新：设置
  if (data.settings !== undefined) {
    if (typeof data.settings === 'object' && data.settings !== null && !Array.isArray(data.settings)) {
      updateData.settings = data.settings
    } else {
      return error('设置数据格式不正确')
    }
  }

  if (Object.keys(updateData).length === 0) {
    return error('没有可更新的字段')
  }

  const result = await usersDB.updateByOpenid(user.openid, updateData)

  if (!result.success) {
    return result
  }

  return success(null, '用户信息更新成功')
})

/**
 * 获取用户VIP记录
 */
exports.getVipRecords = wrapAsync(async (params = {}) => {
  const currentUser = await getCurrentUser()

  if (!currentUser.success) {
    return currentUser
  }

  const { limit = 20, skip = 0 } = params

  const result = await vipRecordsDB.getVipRecordsByUserId(currentUser.data._id, {
    limit,
    skip
  })

  if (!result.success) {
    return error(result.message || '获取VIP记录失败')
  }

  return success(result.data, '获取VIP记录成功')
})



/**
 * 获取VIP记录统计
 */
exports.getVipRecordsStats = wrapAsync(async (params = {}) => {
  const currentUser = await getCurrentUser()

  if (!currentUser.success) {
    return currentUser
  }

  const result = await vipRecordsDB.getVipRecordsStats(currentUser.data._id)

  if (!result.success) {
    return error(result.message || '获取VIP记录统计失败')
  }

  return success(result.data, '获取VIP记录统计成功')
})

/**
 * 获取用户兑换码
 */
exports.getUserRedemptionCodes = wrapAsync(async (params = {}) => {
  const currentUser = await getCurrentUser()

  if (!currentUser.success) {
    return currentUser
  }

  const { page = 1, limit = 20 } = params
  const skip = (page - 1) * limit

  try {
    // 获取用户创建的兑换码
    const codesResult = await redemptionCodesDB.getUserCreatedCodes(currentUser.data._id, {
      limit,
      skip
    })

    if (!codesResult.success) {
      return error(codesResult.message || '获取兑换码失败')
    }

    // 获取统计信息
    const statsResult = await redemptionCodesDB.getCodeStats(currentUser.data._id)
    const stats = statsResult.success ? statsResult.data : {
      totalCodes: 0,
      usedCodes: 0,
      activeCodes: 0
    }

    // 检查是否还有更多数据
    const hasMore = codesResult.data.length === limit

    return success({
      codes: codesResult.data,
      stats: {
        totalCodes: stats.total,
        usedCodes: stats.used,
        activeCodes: stats.active
      },
      hasMore
    }, '获取兑换码成功')

  } catch (error) {
    console.error('获取用户兑换码失败:', error)
    return error(error.message || '获取兑换码失败')
  }
})

/**
 * 使用兑换码
 */
exports.useRedemptionCode = wrapAsync(async (params = {}) => {
  validateRequired(params, ['code'])

  const currentUser = await getCurrentUser()
  if (!currentUser.success) {
    return currentUser
  }

  const { code } = params

  try {
    // 验证并使用兑换码
    const useResult = await redemptionCodesDB.useCode(code, currentUser.data._id)
    if (!useResult.success) {
      return error(useResult.message || '兑换码使用失败')
    }

    const codeData = useResult.data

    // 根据兑换码类型执行相应操作
    if (codeData.type === 'vip_days') {
      // 添加VIP天数
      const vipResult = await vipRecordsDB.addVipDays(currentUser.data._id, codeData.value, `使用兑换码: ${code}`)
      if (!vipResult.success) {
        console.error('添加VIP天数失败:', vipResult.message)
        return error('兑换失败，请联系客服')
      }

      // 更新用户VIP状态
      const vipExpiryResult = await vipRecordsDB.calculateUserVipExpiry(currentUser.data._id)
      if (vipExpiryResult.success && vipExpiryResult.data.isVip) {
        await usersDB.updateByOpenid(currentUser.data.openid, {
          'vip.status': true,
          'vip.expiredAt': vipExpiryResult.data.expireDate
        })
      }

      return success({
        code: codeData,
        vipDays: codeData.value
      }, `成功兑换${codeData.value}天VIP会员！`)
    }

    return success({
      code: codeData
    }, '兑换成功！')

  } catch (error) {
    console.error('使用兑换码失败:', error)
    return error(error.message || '兑换失败')
  }
})