/**
 * 积分记录数据库操作
 */

const BaseDB = require('./base')

class PointsRecordsDB extends BaseDB {
  constructor() {
    super('points-records')
  }

  // 获取数据库实例
  getDB() {
    if (!this._db) {
      const cloud = require('wx-server-sdk')
      this._db = cloud.database()
    }
    return this._db
  }

  /**
   * 创建积分记录
   * @param {Object} recordData - 积分记录数据
   * @returns {Promise<Object>} 操作结果
   */
  async createRecord(recordData) {
    const defaultData = {
      userId: recordData.userId,
      userNumber: recordData.userNumber,
      type: recordData.type, // 'earn' | 'spend'
      amount: recordData.amount, // 正数为获得，负数为消费
      source: recordData.source, // 'check_in', 'purchase', 'admin', 'redeem'
      description: recordData.description,
      relatedId: recordData.relatedId || null,
      timestamp: new Date().toISOString(),
      ...recordData
    }

    return await this.create(defaultData)
  }

  /**
   * 获取用户积分记录
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 积分记录
   */
  async getUserRecords(userId, options = {}) {
    try {
      const {
        type = null, // 'earn' | 'spend' | null(全部)
        limit = 20,
        skip = 0,
        startDate = null,
        endDate = null
      } = options

      let query = { userId }

      // 类型筛选
      if (type) {
        query.type = type
      }

      const db = this.getDB()

      // 日期筛选
      if (startDate || endDate) {
        query.timestamp = {}
        if (startDate) {
          query.timestamp[db.command.gte] = startDate
        }
        if (endDate) {
          query.timestamp[db.command.lte] = endDate
        }
      }

      const result = await db.collection(this.collectionName)
        .where(query)
        .orderBy('timestamp', 'desc')
        .skip(skip)
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取用户积分记录失败:', error)
      return {
        success: false,
        message: error.message || '获取积分记录失败'
      }
    }
  }

  /**
   * 获取用户积分统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 积分统计
   */
  async getUserPointsStats(userId) {
    try {
      const db = this.getDB()

      // 获取总收入
      const earnResult = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'earn'
        })
        .get()

      // 获取总支出
      const spendResult = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'spend'
        })
        .get()

      const totalEarned = earnResult.data.reduce((sum, record) => sum + record.amount, 0)
      const totalSpent = spendResult.data.reduce((sum, record) => sum + Math.abs(record.amount), 0)
      const currentBalance = totalEarned - totalSpent

      return {
        success: true,
        data: {
          totalEarned,
          totalSpent,
          currentBalance,
          totalRecords: earnResult.data.length + spendResult.data.length
        }
      }
    } catch (error) {
      console.error('获取用户积分统计失败:', error)
      return {
        success: false,
        message: error.message || '获取积分统计失败'
      }
    }
  }

  /**
   * 获取积分来源统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 来源统计
   */
  async getPointsSourceStats(userId) {
    try {
      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where({
          userId,
          type: 'earn'
        })
        .get()

      const sourceStats = {}
      result.data.forEach(record => {
        const source = record.source
        if (!sourceStats[source]) {
          sourceStats[source] = {
            count: 0,
            totalPoints: 0
          }
        }
        sourceStats[source].count++
        sourceStats[source].totalPoints += record.amount
      })

      return {
        success: true,
        data: sourceStats
      }
    } catch (error) {
      console.error('获取积分来源统计失败:', error)
      return {
        success: false,
        message: error.message || '获取来源统计失败'
      }
    }
  }

  /**
   * 获取最近的积分记录
   * @param {string} userId - 用户ID
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 最近记录
   */
  async getRecentRecords(userId, limit = 5) {
    try {
      const db = this.getDB()
      const result = await db.collection(this.collectionName)
        .where({ userId })
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .get()

      return {
        success: true,
        data: result.data || []
      }
    } catch (error) {
      console.error('获取最近积分记录失败:', error)
      return {
        success: false,
        message: error.message || '获取最近记录失败'
      }
    }
  }

  /**
   * 按日期统计积分变化
   * @param {string} userId - 用户ID
   * @param {number} days - 天数
   * @returns {Promise<Object>} 日期统计
   */
  async getDailyPointsStats(userId, days = 30) {
    try {
      const db = this.getDB()
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - (days - 1) * 24 * 60 * 60 * 1000)

      const result = await db.collection(this.collectionName)
        .where({
          userId,
          timestamp: db.command.gte(startDate.toISOString()).and(db.command.lte(endDate.toISOString()))
        })
        .orderBy('timestamp', 'asc')
        .get()

      // 按日期分组统计
      const dailyStats = {}
      result.data.forEach(record => {
        const date = record.timestamp.split('T')[0]
        if (!dailyStats[date]) {
          dailyStats[date] = {
            earned: 0,
            spent: 0,
            net: 0
          }
        }
        
        if (record.type === 'earn') {
          dailyStats[date].earned += record.amount
        } else {
          dailyStats[date].spent += Math.abs(record.amount)
        }
        dailyStats[date].net = dailyStats[date].earned - dailyStats[date].spent
      })

      return {
        success: true,
        data: dailyStats
      }
    } catch (error) {
      console.error('获取日期积分统计失败:', error)
      return {
        success: false,
        message: error.message || '获取日期统计失败'
      }
    }
  }
}

module.exports = new PointsRecordsDB()
