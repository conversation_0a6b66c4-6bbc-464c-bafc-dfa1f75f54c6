/**
 * 用户数据存储操作
 */

const BaseDB = require('./base')
const { formatDate } = require('../utils/date.js')
const { normalizeTime } = require('../utils/datetime.js')

class UserDataDB extends BaseDB {
  constructor() {
    super('user-data')
  }

  /**
   * 根据用户ID查找数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async findByUserId(userId) {
    return await this.findOne({ userId })
  }

  /**
   * 保存用户数据
   * @param {string} userId - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveUserData(userId, userData) {
    try {
      // 使用 userData 中的 lastModified 作为时间来源
      const userLastModified = userData.lastModified || new Date().toISOString()
      const saveTime = new Date(userLastModified)

      // 获取当前日期（用于同一天判断）
      const currentDate = formatDate(saveTime, 'YYYY-MM-DD') // YYYY-MM-DD 格式

      const dataToSave = {
        userId,
        data: userData,
        lastModified: normalizeTime(saveTime),
        recordDate: currentDate
      }

      // 查找同一天的数据
      const todayDataResult = await this.findOne({
        userId,
        recordDate: currentDate
      })

      if (todayDataResult.success && todayDataResult.data) {
        // 同一天内有数据，更新（覆盖）现有数据
        console.log(`更新用户 ${userId} 在 ${currentDate} 的数据`)
        return await this.updateById(todayDataResult.data._id, dataToSave)
      } else {
        // 同一天内没有数据，创建新记录
        console.log(`创建用户 ${userId} 在 ${currentDate} 的新数据`)
        return await this.create(dataToSave)
      }
    } catch (error) {
      console.error('保存用户数据失败:', error)
      return {
        success: false,
        message: error.message || '保存用户数据失败'
      }
    }
  }

  /**
   * 获取用户数据（获取最新的数据）
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async getUserData(userId) {
    try {
      // 获取用户的最新数据（按 lastModified 降序排列，取第一条）
      const result = await this.find({ userId }, {
        orderBy: { field: 'lastModified', order: 'desc' },
        limit: 1
      })

      if (!result.success) {
        return result
      }

      if (!result.data || result.data.length === 0) {
        return {
          success: true,
          data: null,
          hasData: false
        }
      }

      const latestRecord = result.data[0]

      // 使用 userData 中的 lastModified 作为时间来源
      const userData = latestRecord.data
      const userLastModified = userData.lastModified || latestRecord.lastModified
      const normalizedTimestamp = Math.floor(new Date(userLastModified).getTime() / 1000) * 1000

      return {
        success: true,
        data: userData,
        hasData: true,
        lastModified: userLastModified,
        timestamp: normalizedTimestamp
      }
    } catch (error) {
      console.error('获取用户数据失败:', error)
      return {
        success: false,
        message: error.message || '获取用户数据失败'
      }
    }
  }

  /**
   * 删除用户数据
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteUserData(userId) {
    return await this.delete({ userId })
  }

  /**
   * 获取用户历史数据列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getUserDataHistory(userId, options = {}) {
    try {
      const queryOptions = {
        orderBy: { field: 'lastModified', order: 'desc' },
        limit: options.limit || 20,
        skip: options.skip || 0
      }

      const result = await this.find({ userId }, queryOptions)

      if (!result.success) {
        return result
      }

      // 格式化历史数据
      const historyList = result.data.map(item => ({
        id: item._id,
        timestamp: new Date(item.lastModified).getTime(),
        timeText: item.lastModified,
        recordDate: item.recordDate,
        hasData: !!item.data,
        workHistoriesCount: item.data?.workHistories?.length || 0,
        hasSettings: item.data?.settings && Object.keys(item.data.settings).length > 0
      }))

      return {
        success: true,
        data: historyList,
        total: result.total
      }
    } catch (error) {
      console.error('获取用户历史数据失败:', error)
      return {
        success: false,
        message: error.message || '获取用户历史数据失败'
      }
    }
  }

  /**
   * 根据ID获取历史数据详情
   * @param {string} dataId - 数据ID
   * @param {string} userId - 用户ID（用于权限验证）
   * @returns {Promise<Object>} 查询结果
   */
  async getHistoryDataById(dataId, userId) {
    try {
      const result = await this.findById(dataId)

      if (!result.success) {
        return result
      }

      if (!result.data) {
        return {
          success: false,
          message: '数据不存在'
        }
      }

      // 验证数据所有权
      if (result.data.userId !== userId) {
        return {
          success: false,
          message: '无权访问此数据'
        }
      }

      return {
        success: true,
        data: result.data.data,
        lastModified: result.data.lastModified,
        timestamp: new Date(result.data.lastModified).getTime()
      }
    } catch (error) {
      console.error('获取历史数据详情失败:', error)
      return {
        success: false,
        message: error.message || '获取历史数据详情失败'
      }
    }
  }

  /**
   * 清理过期的历史数据
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpiredData(retentionDays = 90) {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      const result = await this.delete({
        createTime: {
          $lt: cutoffDate
        }
      })

      return {
        success: true,
        data: {
          deleted: result.data.deleted,
          cutoffDate: cutoffDate
        }
      }
    } catch (error) {
      console.error('清理过期数据失败:', error)
      return {
        success: false,
        message: error.message || '清理过期数据失败'
      }
    }
  }
}

module.exports = new UserDataDB()
