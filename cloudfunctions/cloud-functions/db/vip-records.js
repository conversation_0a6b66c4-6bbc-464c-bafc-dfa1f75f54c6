/**
 * VIP记录数据库操作
 */

const BaseDB = require('./base')

class VipRecordsDB extends BaseDB {
  constructor() {
    super('vip-records')
  }

  /**
   * 创建VIP记录
   * @param {Object} recordData - VIP记录数据
   * @returns {Promise<Object>} 创建结果
   */
  async createVipRecord(recordData) {
    const defaultRecordData = {
      userId: recordData.userId,
      type: recordData.type, // 'new_user', 'activation_code', 'invite_friend', 'watch_ad'
      days: recordData.days || 0,
      description: recordData.description || '',
      source: recordData.source || '', // 激活码、邀请人ID等
      status: 'active', // 'active', 'expired', 'used'
      effectiveDate: recordData.effectiveDate || new Date(),
      expireDate: recordData.expireDate || this.calculateExpireDate(recordData.days),
      ...recordData
    }

    return await this.create(defaultRecordData)
  }

  /**
   * 根据用户ID获取VIP记录
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getVipRecordsByUserId(userId, options = {}) {
    const queryOptions = {
      orderBy: { field: 'createTime', order: 'desc' },
      limit: options.limit || 50,
      skip: options.skip || 0
    }

    return await this.find({ userId }, queryOptions)
  }

  /**
   * 获取用户有效的VIP记录
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async getActiveVipRecords(userId) {
    const now = new Date()
    
    return await this.find({
      userId,
      status: 'active',
      expireDate: { $gt: now }
    }, {
      orderBy: { field: 'expireDate', order: 'desc' }
    })
  }

  /**
   * 计算用户VIP到期时间
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 计算结果
   */
  async calculateUserVipExpiry(userId) {
    try {
      const activeRecordsResult = await this.getActiveVipRecords(userId)
      
      if (!activeRecordsResult.success || activeRecordsResult.data.length === 0) {
        return {
          success: true,
          data: {
            isVip: false,
            expireDate: null,
            daysRemaining: 0
          }
        }
      }

      // 找到最晚的到期时间
      const latestExpireDate = activeRecordsResult.data.reduce((latest, record) => {
        const recordExpire = new Date(record.expireDate)
        return recordExpire > latest ? recordExpire : latest
      }, new Date(0))

      const now = new Date()
      const daysRemaining = Math.ceil((latestExpireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

      return {
        success: true,
        data: {
          isVip: daysRemaining > 0,
          expireDate: latestExpireDate,
          daysRemaining: Math.max(0, daysRemaining)
        }
      }
    } catch (error) {
      console.error('计算用户VIP到期时间失败:', error)
      return {
        success: false,
        message: error.message || '计算VIP到期时间失败'
      }
    }
  }

  /**
   * 添加新用户VIP福利
   * @param {string} userId - 用户ID
   * @param {number} days - 赠送天数，默认14天
   * @returns {Promise<Object>} 创建结果
   */
  async addNewUserVipBenefit(userId, days = 14) {
    try {
      // 检查是否已经领取过新用户福利
      const existingResult = await this.find({
        userId,
        type: 'new_user'
      })

      if (existingResult.success && existingResult.data.length > 0) {
        return {
          success: false,
          message: '新用户福利已领取过'
        }
      }

      // 创建新用户VIP记录
      const recordData = {
        userId,
        type: 'new_user',
        days,
        description: `新用户福利赠送${days}天VIP会员`,
        source: 'system',
        effectiveDate: new Date()
      }

      return await this.createVipRecord(recordData)
    } catch (error) {
      console.error('添加新用户VIP福利失败:', error)
      return {
        success: false,
        message: error.message || '添加新用户VIP福利失败'
      }
    }
  }

  /**
   * 添加激活码VIP
   * @param {string} userId - 用户ID
   * @param {string} activationCode - 激活码
   * @param {number} days - VIP天数
   * @returns {Promise<Object>} 创建结果
   */
  async addActivationCodeVip(userId, activationCode, days) {
    const recordData = {
      userId,
      type: 'activation_code',
      days,
      description: `激活码兑换${days}天VIP会员`,
      source: activationCode,
      effectiveDate: new Date()
    }

    return await this.createVipRecord(recordData)
  }

  /**
   * 添加观看广告VIP
   * @param {string} userId - 用户ID
   * @param {number} days - VIP天数，默认1天
   * @returns {Promise<Object>} 创建结果
   */
  async addWatchAdVip(userId, days = 1) {
    const recordData = {
      userId,
      type: 'watch_ad',
      days,
      description: `观看广告获得${days}天VIP会员`,
      source: 'ad_reward',
      effectiveDate: new Date()
    }

    return await this.createVipRecord(recordData)
  }

  /**
   * 添加邀请好友VIP
   * @param {string} userId - 用户ID
   * @param {string} inviteeId - 被邀请人ID
   * @param {number} days - VIP天数
   * @returns {Promise<Object>} 创建结果
   */
  async addInviteFriendVip(userId, inviteeId, days) {
    const recordData = {
      userId,
      type: 'invite_friend',
      days,
      description: `邀请好友获得${days}天VIP会员`,
      source: inviteeId,
      effectiveDate: new Date()
    }

    return await this.createVipRecord(recordData)
  }

  /**
   * 计算到期日期
   * @param {number} days - 天数
   * @param {Date} startDate - 开始日期，默认为当前时间
   * @returns {Date} 到期日期
   */
  calculateExpireDate(days, startDate = new Date()) {
    const expireDate = new Date(startDate)
    expireDate.setDate(expireDate.getDate() + days)
    return expireDate
  }

  /**
   * 获取VIP记录统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 统计结果
   */
  async getVipRecordsStats(userId) {
    try {
      const allRecordsResult = await this.getVipRecordsByUserId(userId)
      
      if (!allRecordsResult.success) {
        return allRecordsResult
      }

      const records = allRecordsResult.data
      const stats = {
        totalRecords: records.length,
        totalDays: 0,
        typeStats: {
          new_user: { count: 0, days: 0 },
          activation_code: { count: 0, days: 0 },
          watch_ad: { count: 0, days: 0 },
          invite_friend: { count: 0, days: 0 }
        }
      }

      records.forEach(record => {
        stats.totalDays += record.days || 0
        
        if (stats.typeStats[record.type]) {
          stats.typeStats[record.type].count++
          stats.typeStats[record.type].days += record.days || 0
        }
      })

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('获取VIP记录统计失败:', error)
      return {
        success: false,
        message: error.message || '获取VIP记录统计失败'
      }
    }
  }
}

module.exports = new VipRecordsDB()
