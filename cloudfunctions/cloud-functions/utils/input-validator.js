/**
 * 输入验证工具
 * 防止恶意参数注入和数据污染
 */

/**
 * 用户信息安全字段白名单
 */
const USER_SAFE_FIELDS = {
  // 基本信息字段
  nickname: {
    type: 'string',
    maxLength: 50,
    required: false,
    sanitize: true
  },
  avatar: {
    type: 'string',
    maxLength: 500,
    required: false,
    pattern: /^https?:\/\/.+/  // 必须是有效的URL
  },
  // 版本信息
  version: {
    type: 'string',
    maxLength: 20,
    required: false,
    pattern: /^\d+\.\d+\.\d+$/  // 版本号格式：x.y.z
  }
}

/**
 * 用户更新信息安全字段白名单
 */
const USER_UPDATE_SAFE_FIELDS = {
  nickname: USER_SAFE_FIELDS.nickname,
  avatar: USER_SAFE_FIELDS.avatar,
  settings: {
    type: 'object',
    required: false,
    maxDepth: 3  // 限制嵌套深度
  }
}

/**
 * 验证并清理用户输入
 * @param {Object} input - 输入数据
 * @param {Object} fieldConfig - 字段配置
 * @returns {Object} 清理后的安全数据
 */
function validateAndSanitizeInput(input, fieldConfig) {
  const result = {}
  const errors = []

  for (const [fieldName, config] of Object.entries(fieldConfig)) {
    const value = input[fieldName]

    // 检查必填字段
    if (config.required && (value === undefined || value === null || value === '')) {
      errors.push(`字段 ${fieldName} 是必填的`)
      continue
    }

    // 如果字段不存在且非必填，跳过
    if (value === undefined || value === null) {
      continue
    }

    // 类型验证
    if (!validateType(value, config.type)) {
      errors.push(`字段 ${fieldName} 类型错误，期望 ${config.type}`)
      continue
    }

    // 长度验证
    if (config.maxLength && typeof value === 'string' && value.length > config.maxLength) {
      errors.push(`字段 ${fieldName} 长度超过限制 ${config.maxLength}`)
      continue
    }

    // 正则验证
    if (config.pattern && typeof value === 'string' && !config.pattern.test(value)) {
      errors.push(`字段 ${fieldName} 格式不正确`)
      continue
    }

    // 对象深度验证
    if (config.type === 'object' && config.maxDepth) {
      if (getObjectDepth(value) > config.maxDepth) {
        errors.push(`字段 ${fieldName} 嵌套层级过深`)
        continue
      }
    }

    // 数据清理
    let cleanValue = value
    if (config.sanitize && typeof value === 'string') {
      cleanValue = sanitizeString(value)
    }

    result[fieldName] = cleanValue
  }

  if (errors.length > 0) {
    throw new Error(`输入验证失败: ${errors.join(', ')}`)
  }

  return result
}

/**
 * 验证数据类型
 * @param {*} value - 值
 * @param {string} expectedType - 期望类型
 * @returns {boolean} 是否匹配
 */
function validateType(value, expectedType) {
  switch (expectedType) {
    case 'string':
      return typeof value === 'string'
    case 'number':
      return typeof value === 'number' && !isNaN(value)
    case 'boolean':
      return typeof value === 'boolean'
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value)
    case 'array':
      return Array.isArray(value)
    default:
      return false
  }
}

/**
 * 获取对象嵌套深度
 * @param {Object} obj - 对象
 * @returns {number} 嵌套深度
 */
function getObjectDepth(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return 0
  }

  let maxDepth = 0
  for (const value of Object.values(obj)) {
    if (typeof value === 'object' && value !== null) {
      maxDepth = Math.max(maxDepth, getObjectDepth(value))
    }
  }

  return maxDepth + 1
}

/**
 * 清理字符串，移除潜在的恶意内容
 * @param {string} str - 输入字符串
 * @returns {string} 清理后的字符串
 */
function sanitizeString(str) {
  if (typeof str !== 'string') {
    return str
  }

  return str
    .trim()  // 移除首尾空格
    .replace(/[<>]/g, '')  // 移除尖括号，防止XSS
    .replace(/[\x00-\x1f\x7f]/g, '')  // 移除控制字符
    .substring(0, 1000)  // 限制最大长度
}

/**
 * 验证用户注册信息
 * @param {Object} userInfo - 用户信息
 * @returns {Object} 验证后的安全用户信息
 */
function validateUserRegistration(userInfo) {
  try {
    return validateAndSanitizeInput(userInfo, USER_SAFE_FIELDS)
  } catch (error) {
    console.error('用户注册信息验证失败:', error.message)
    throw new Error('用户信息格式不正确')
  }
}

/**
 * 验证用户更新信息
 * @param {Object} updateInfo - 更新信息
 * @returns {Object} 验证后的安全更新信息
 */
function validateUserUpdate(updateInfo) {
  try {
    return validateAndSanitizeInput(updateInfo, USER_UPDATE_SAFE_FIELDS)
  } catch (error) {
    console.error('用户更新信息验证失败:', error.message)
    throw new Error('更新信息格式不正确')
  }
}

/**
 * 检查是否包含敏感字段
 * @param {Object} input - 输入数据
 * @returns {Array} 发现的敏感字段列表
 */
function checkSensitiveFields(input) {
  const sensitiveFields = [
    'openid', '_id', 'id', 'no',
    'isAdmin', 'isTestUser',
    'vip', 'points', 'apiCallCount',
    'checkInStats', 'invitation',
    'createdAt', 'updatedAt'
  ]

  const foundSensitive = []
  for (const field of sensitiveFields) {
    if (input.hasOwnProperty(field)) {
      foundSensitive.push(field)
    }
  }

  return foundSensitive
}

module.exports = {
  validateUserRegistration,
  validateUserUpdate,
  validateAndSanitizeInput,
  checkSensitiveFields,
  USER_SAFE_FIELDS,
  USER_UPDATE_SAFE_FIELDS
}
