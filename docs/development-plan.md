# 时间跟踪器小程序开发计划

## 项目概述
将HTML版本的时间跟踪器复刻为微信小程序，使用纯原生技术实现（WXML、WXSS、JS、JSON）。

## 原功能分析
1. **日历视图** - 月历显示，日期选择，显示收入信息
2. **收入统计** - 今日/本周/本月收入统计  
3. **时间可视化** - 一日时间段的进度条可视化
4. **时间段管理** - 增删改查时间段，支持工作/休息/加班类型
5. **数据导入** - 从其他日期复制时间段
6. **数据存储** - 本地数据持久化

## 技术迁移方案

### 页面结构
- `pages/time-tracker/index` - 主页面（时间跟踪器）
- `pages/calendar/index` - 日历页面  
- `pages/statistics/index` - 统计页面

### 组件设计
- `components/calendar-view` - 日历组件
- `components/time-visualization` - 时间可视化组件
- `components/time-segment-list` - 时间段列表组件
- `components/time-input-modal` - 时间输入模态框
- `components/date-selector` - 日期选择器

### 数据存储
- 使用 `wx.setStorageSync`/`wx.getStorageSync` 替代 localStorage
- 数据结构保持与原HTML版本一致

### 样式处理
- 手动实现TailwindCSS样式（转换为WXSS）
- 使用微信小程序内置图标替代FontAwesome
- 采用Flexbox布局适配不同屏幕

### WXS使用注意事项
- 时间处理使用 `getDate()` 替代 `new Date()`
- 正则表达式使用 `getRegExp()` 替代 `new RegExp()`
- 类型判断使用构造器和typeof

## 开发阶段

### 第一阶段：项目重构
- [x] 清理多余文件
- [x] 重新配置app.json
- [x] 创建主页面结构

### 第二阶段：核心组件开发
- [ ] 日历组件
- [ ] 时间可视化组件
- [ ] 时间段列表组件

### 第三阶段：数据管理
- [ ] 数据存储服务
- [ ] 时间段CRUD操作
- [ ] 收入计算逻辑

### 第四阶段：交互优化
- [ ] 模态框组件
- [ ] 手势操作
- [ ] 用户体验优化

## 文件清理列表
- 删除: pages/example, pages/home, pages/work-history, pages/settings
- 保留: pages/index（重构为时间跟踪器主页）
- 保留: pages/calendar（重构为日历页面）

## 注意事项
1. 不使用第三方UI库
2. 不使用skyline渲染模式
3. 保持代码的可扩展性
4. 添加详细的中文注释
5. 遵循微信小程序开发规范 