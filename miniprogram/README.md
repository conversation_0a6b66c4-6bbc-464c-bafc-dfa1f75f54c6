# 工作时间追踪小程序

一个专业的微信小程序，用于工作时间追踪和收入管理。采用现代化的模块化架构，提供完整的工作履历管理、时间追踪、收入统计等功能。

## 项目特点

- 🏗️ **现代化架构** - 采用分层架构设计，模块化开发
- 📱 **微信小程序** - 原生微信小程序开发，性能优异
- 💾 **智能存储** - 数据压缩存储，节省空间
- 🔧 **工具丰富** - 完整的工具类库，提高开发效率
- 📊 **数据可视化** - 多种图表展示，数据一目了然
- 🎨 **界面美观** - 现代化UI设计，用户体验优秀

## 功能特性

### 核心功能
- ✅ 工作履历管理（增删改查）
- ✅ 时间追踪记录
- ✅ 收入统计分析
- ✅ 多仪表盘展示
- ✅ 数据导入导出
- ✅ 用户签到系统
- ✅ 积分商店系统
- ✅ 摸鱼状态追踪
- ✅ 友情应用推荐
- ✅ 邀请好友功能
- ✅ 反馈意见系统
- ✅ 数据云端同步

### 技术特性
- ✅ 模块化架构设计
- ✅ 数据压缩存储
- ✅ 缓存机制优化
- ✅ 防抖保存机制
- ✅ 完善的错误处理
- ✅ 丰富的工具类库
- ✅ API客户端封装
- ✅ 重试机制支持
- ✅ 用户管理系统
- ✅ 云端数据同步

## 项目架构

### 分层架构
```
Pages/Components (页面/组件层)
    ↓
Services (业务服务层)
    ↓
Managers (数据管理层)
    ↓
Utils (工具类层)
```

### 目录结构
```
miniprogram/
├── core/                    # 核心业务模块
│   ├── api/                # API客户端模块
│   │   ├── base.js         # 基础API客户端
│   │   ├── enhanced.js     # 增强API客户端
│   │   ├── modules/        # API模块（用户、签到、积分、商店等）
│   │   └── utils/          # API工具（缓存、重试）
│   ├── managers/           # 数据管理器
│   │   ├── data-manager.js        # 主数据管理器
│   │   ├── storage-manager.js     # 存储管理器
│   │   ├── work-manager.js        # 工作履历管理器
│   │   ├── settings-manager.js    # 设置管理器
│   │   ├── time-tracking-manager.js # 时间追踪管理器
│   │   ├── user-manager.js        # 用户管理器
│   │   ├── sync-manager.js        # 同步管理器
│   │   ├── fishing-manager.js     # 摸鱼管理器
│   │   └── holiday-manager.js     # 节假日管理器
│   └── services/           # 业务服务
│       ├── dashboard-service.js   # 仪表盘服务
│       ├── work-history-service.js # 工作履历服务
│       ├── time-segment-service.js # 时间段服务
│       ├── statistics-service.js  # 统计分析服务
│       └── data-import-export-service.js # 数据导入导出服务
├── utils/                   # 工具类模块
│   ├── validators/         # 验证器
│   │   └── data-validator.js      # 数据验证工具
│   ├── formatters/         # 格式化器
│   │   └── data-formatter.js      # 数据格式化工具
│   └── helpers/            # 辅助工具
│       ├── data-calculator.js     # 数据计算工具
│       ├── data-security.js       # 数据安全工具
│       └── time-utils.js          # 时间工具
├── pages/                   # 页面
├── components/              # 组件
│   ├── dashboard-switcher/ # 仪表盘切换器（共用组件）
│   ├── dashboard1/         # 经典仪表盘（自主导航栏）
│   ├── dashboard2/         # 现代仪表盘（自主导航栏）
│   ├── fishing-control/    # 摸鱼控制组件
│   └── fishing-remark-editor/ # 摸鱼备注编辑器组件
├── tests/                   # 测试
└── docs/                    # 文档
```

## 快速开始

### 环境要求
- 微信开发者工具
- Node.js (可选，用于开发工具)

### 安装步骤
1. 克隆项目到本地
2. 使用微信开发者工具打开项目
3. 配置小程序AppID
4. 编译运行

### 基础使用
```javascript
// 导入核心数据管理器
import dataManager from './core/managers/data-manager.js'

// 确保数据已加载
await dataManager.ensureLoaded()

// 添加工作履历
const workId = dataManager.addWork({
  company: '公司名称',
  position: '职位',
  startDate: new Date(),
  formalSalary: 10000
})

// 获取当前工作
const currentWork = dataManager.getCurrentWork()
```

## 核心模块介绍

### 数据管理器 (Managers)

#### DataManager - 主数据管理器
- 作为协调器整合各个专门管理器
- 提供统一的数据访问接口
- 管理数据变化通知和缓存

#### StorageManager - 存储管理器
- 负责数据的存储和加载
- 数据压缩和解压
- 数据序列化和反序列化

#### WorkManager - 工作履历管理器
- 工作履历的CRUD操作
- 工作履历数据验证
- 业务逻辑封装

#### SettingsManager - 设置管理器
- 用户设置管理
- 仪表盘配置管理
- 隐私设置管理

#### TimeTrackingManager - 时间追踪管理器
- 时间段的增删改查
- 日期数据管理
- 收入计算和统计

### 工具类 (Utils)

#### DataValidator - 数据验证器
```javascript
// 验证邮箱
DataValidator.isValidEmail('<EMAIL>')

// 验证手机号
DataValidator.isValidPhone('13812345678')

// 验证密码强度
DataValidator.validatePassword('password123')
```

#### DataFormatter - 数据格式化器
```javascript
// 格式化货币
DataFormatter.formatCurrency(1234.56) // ¥1,234.56

// 格式化时间
DataFormatter.formatDuration(125) // 2小时5分钟

// 格式化日期
DataFormatter.formatDate(new Date(), 'YYYY-MM-DD')
```

#### DataCalculator - 数据计算器
```javascript
// 计算天数差
DataCalculator.daysBetween('2024-01-01', '2024-01-15')

// 计算平均值
DataCalculator.average([10, 20, 30])

// 计算百分比
DataCalculator.percentage(25, 100)
```

#### DataSecurity - 数据安全工具
```javascript
// 手机号脱敏
DataSecurity.maskPhone('13812345678') // 138****5678

// 邮箱脱敏
DataSecurity.maskEmail('<EMAIL>') // t**<EMAIL>
```

## 开发指南

### 新功能开发流程
1. **需求分析** - 确定功能属于哪个业务领域
2. **数据层设计** - 设计数据结构和管理器
3. **业务服务层** - 实现业务逻辑
4. **页面组件层** - 实现用户界面
5. **测试验证** - 编写测试确保功能正常

### 代码规范
- 使用ES6模块语法
- 遵循单一职责原则
- 统一的命名规范
- 完善的错误处理
- 适当的注释说明

### 性能优化
- 使用缓存机制减少重复计算
- 防抖保存避免频繁写入
- 数据压缩节省存储空间
- 按需加载减少初始化时间

## 文档

- 📖 [开发指南](./docs/development-guide.md) - 详细的开发规范和流程
- 🚀 [快速参考](./docs/quick-reference.md) - 常用API和代码模板
- 🏗️ [项目结构](./docs/project-structure.md) - 详细的项目结构说明
- 📝 [使用指南](./docs/usage-guide.md) - 功能使用说明
- 📊 [重构报告](./docs/refactor-report.md) - 项目重构详情

## 测试

### 运行测试
```javascript
// 导入测试文件
const { runAllTests } = require('./tests/test-refactor.js')

// 运行所有测试
await runAllTests()
```

### 测试覆盖
- ✅ 数据管理器功能测试
- ✅ 工作履历管理测试
- ✅ 设置管理测试
- ✅ 时间追踪管理测试
- ✅ 存储管理测试

## 版本历史

### v0.3.0 (当前版本)
- 🎉 完成项目架构重构
- ✨ 新增模块化数据管理器
- 🔧 新增丰富的工具类库
- 📚 完善项目文档
- 🚀 优化性能和用户体验

### 特性说明
- **新项目**: 这是一个全新的项目，没有历史用户数据
- **无兼容性负担**: 开发新功能时无需考虑数据兼容性
- **现代化架构**: 采用最新的开发理念和最佳实践

## 贡献指南

### 开发环境设置
1. 安装微信开发者工具
2. 克隆项目代码
3. 阅读开发指南文档
4. 遵循代码规范

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 项目讨论区

---

**注意**: 这是一个新项目，专注于提供最佳的开发体验和用户体验。我们采用现代化的架构设计，确保项目的长期可维护性和扩展性。
