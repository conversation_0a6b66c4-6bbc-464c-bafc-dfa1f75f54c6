<!-- 批量操作模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content batch-modal-content" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">
        <text wx:if="{{operation === 'import'}}">导入工作安排</text>
        <text wx:elif="{{operation === 'copy' && batchStep === 1}}">第1步：选择源日期</text>
        <text wx:elif="{{operation === 'copy' && batchStep === 2}}">第2步：选择目标日期</text>
      </view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>

    <view class="modal-body" bind:scroll="onBatchModalScroll">
      <!-- 导入模式 -->
      <view wx:if="{{operation === 'import'}}">
        <view class="modal-subtitle">选择要导入的日期</view>
        <view class="template-list">
          <view class="template-item" wx:for="{{templateDates}}" wx:key="dateKey" bind:tap="onSelectTemplate" data-index="{{index}}">
            <view class="template-date">
              <text class="date-text">{{item.dateText}}</text>
              <view class="status-badge" style="background-color: {{item.statusConfig.backgroundColor}}; color: {{item.statusConfig.color}};">
                <text class="status-icon">{{item.statusConfig.icon}}</text>
                <text class="status-text">{{item.statusConfig.name}}</text>
              </view>
            </view>
            <view class="template-info">
              <text class="segment-count">{{item.segmentCount}}个时间段</text>
              <text class="income-info">收入: ¥{{item.dailyIncome.toFixed(2)}}</text>
            </view>
          </view>
        </view>

        <view wx:if="{{templateDates.length === 0}}" class="empty-templates">
          <view class="empty-icon">📅</view>
          <view class="empty-text">暂无可导入的日期</view>
          <view class="empty-tip">请先设置其他日期的工作安排</view>
        </view>
      </view>

      <!-- 批量复制模式 -->
      <view wx:else>
        <!-- 步骤1：选择源日期 -->
        <view wx:if="{{batchStep === 1}}">
          <!-- 批量复制日历 -->
          <view class="batch-calendar">
            <view class="batch-calendar-header">
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                <text class="nav-arrow">‹‹</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                <text class="nav-arrow">‹</text>
              </view>
              <view class="batch-calendar-title">
                <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                <text class="nav-arrow">›</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                <text class="nav-arrow">››</text>
              </view>
            </view>

            <view class="batch-calendar-weekdays">
              <view class="weekday">日</view>
              <view class="weekday">一</view>
              <view class="weekday">二</view>
              <view class="weekday">三</view>
              <view class="weekday">四</view>
              <view class="weekday">五</view>
              <view class="weekday">六</view>
            </view>

            <view class="batch-calendar-days">
              <block wx:for="{{batchCalendarDays}}" wx:key="index">
                <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedSource ? 'selected-source' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}} {{item && item.status ? 'day-status-' + item.status : ''}}"
                      bind:tap="onBatchDateTap"
                      data-index="{{index}}"
                      wx:if="{{item}}"
                      style="{{item && item.statusConfig ? '--status-color: ' + item.statusConfig.color + '; --status-bg-color: ' + item.statusConfig.backgroundColor + '; --status-border-color: ' + item.statusConfig.borderColor + ';' : ''}}">
                  <!-- 状态图标 -->
                  <view wx:if="{{item.status && item.statusConfig}}" class="status-indicator">
                    <text class="status-icon">{{item.statusConfig.icon}}</text>
                  </view>

                  <text class="day-number">{{item.day}}</text>
                  <text wx:if="{{item.holidayName}}" class="holiday-name">{{item.holidayName}}</text>
                </view>
                <view class="batch-calendar-day empty" wx:else></view>
              </block>
            </view>
          </view>

          <!-- 滚动提示箭头 -->
          <view wx:if="{{showScrollIndicator}}" class="scroll-indicator fade-in-out">
            <view class="scroll-arrow">
              <text class="arrow-icon">↓</text>
              <text class="arrow-text">往下滚动查看当天安排</text>
            </view>
          </view>

          <!-- 源日期工作安排预览 -->
          <view wx:if="{{sourceSchedulePreview}}" class="schedule-preview">
            <view class="preview-header">
              <view class="preview-title-section">
                <text class="preview-title">{{sourceSchedulePreview.date}}</text>
                <view wx:if="{{sourceSchedulePreview.statusConfig}}" class="preview-status">
                  <text class="status-icon">{{sourceSchedulePreview.statusConfig.icon}}</text>
                  <text class="status-text">{{sourceSchedulePreview.statusConfig.name}}</text>
                </view>
              </view>
            </view>

            <view wx:if="{{sourceSchedulePreview.hasSegments}}" class="preview-segments">
              <view class="preview-segment" wx:for="{{sourceSchedulePreview.segments}}" wx:key="index">
                <view class="segment-left">
                  <view class="segment-type-badge type-{{item.type}}">
                    <text class="type-text">{{item.typeText}}</text>
                  </view>
                  <view class="segment-time-info">
                    <text class="segment-time">{{item.startTime}} - {{item.endTime}}</text>
                    <text class="segment-duration">{{item.duration}}</text>
                  </view>
                </view>
                <view wx:if="{{item.income > 0}}" class="segment-right">
                  <view class="segment-income-info">
                    <text class="income-amount">¥{{item.incomeText}}</text>
                    <text wx:if="{{item.hourlyRateText}}" class="hourly-rate">¥{{item.hourlyRateText}}/h</text>
                  </view>
                </view>
              </view>
              <view class="preview-income-section">
                <text class="preview-income">日收入: ¥{{sourceSchedulePreview.dailyIncome || '0.00'}}</text>
              </view>
            </view>

            <view wx:if="{{sourceSchedulePreview.statusOnly}}" class="status-only-preview">
              <view class="status-only-info">
                <text class="status-only-text">该日期仅设置了状态，没有具体的时间安排</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 步骤2：选择目标日期 -->
        <view wx:elif="{{batchStep === 2}}">
          <!-- 快速添加工作日按钮 -->
          <view class="quick-add-workdays">
            <view class="quick-add-btn" bind:tap="onAddCurrentMonthWorkdays">
              <text class="btn-icon">➕</text>
              <text class="btn-text">加选此月工作日</text>
            </view>
            <view class="quick-add-btn clear-btn {{selectedTargetDates.length === 0 ? 'disabled' : ''}}" bind:tap="onClearSelection">
              <text class="btn-icon">🗑️</text>
              <text class="btn-text">清空选择</text>
            </view>
          </view>

          <!-- 批量复制日历 -->
          <view class="batch-calendar">
            <view class="batch-calendar-header">
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                <text class="nav-arrow">‹‹</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                <text class="nav-arrow">‹</text>
              </view>
              <view class="batch-calendar-title">
                <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                <text class="nav-arrow">›</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                <text class="nav-arrow">››</text>
              </view>
            </view>

            <view class="batch-calendar-weekdays">
              <view class="weekday">日</view>
              <view class="weekday">一</view>
              <view class="weekday">二</view>
              <view class="weekday">三</view>
              <view class="weekday">四</view>
              <view class="weekday">五</view>
              <view class="weekday">六</view>
            </view>

            <view class="batch-calendar-days">
              <block wx:for="{{batchCalendarDays}}" wx:key="index">
                <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedTarget ? 'selected-target' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}} {{item && item.isToday ? 'is-today' : ''}} {{item && item.status ? 'day-status-' + item.status : ''}}"
                      bind:tap="onBatchDateTap"
                      data-index="{{index}}"
                      wx:if="{{item}}"
                      style="{{item && item.statusConfig ? '--status-color: ' + item.statusConfig.color + '; --status-bg-color: ' + item.statusConfig.backgroundColor + '; --status-border-color: ' + item.statusConfig.borderColor + ';' : ''}}">
                  <!-- 状态图标 -->
                  <view wx:if="{{item.status && item.statusConfig}}" class="status-indicator">
                    <text class="status-icon">{{item.statusConfig.icon}}</text>
                  </view>

                  <text class="day-number">{{item.day}}</text>
                  <text wx:if="{{item.holidayName}}" class="holiday-name">{{item.holidayName}}</text>
                </view>
                <view class="batch-calendar-day empty" wx:else></view>
              </block>
            </view>
          </view>


          <!-- 复制选项面板 -->
          <view wx:if="{{selectedTargetDates.length > 0}}" class="copy-options-panel">
            <view class="panel-header">
              <text class="panel-title">复制选项</text>
            </view>
            <view class="copy-options">
              <view wx:if="{{sourceDataTypes && sourceDataTypes.hasStatus}}" class="copy-option">
                <switch checked="{{copyStatus}}" bind:change="onCopyStatusChange" />
                <text class="option-label">复制日期状态</text>
                <text wx:if="{{sourceDataTypes.statusText}}" class="option-desc">（{{sourceDataTypes.statusText}}）</text>
              </view>
              <view wx:if="{{sourceDataTypes && sourceDataTypes.hasSegments}}" class="copy-option">
                <switch checked="{{copySegments}}" bind:change="onCopySegmentsChange" />
                <text class="option-label">复制时间段安排</text>
                <text class="option-desc">（{{sourceDataTypes.segmentCount}}个时间段）</text>
              </view>
            </view>
            <view class="copy-note">
              <text class="note-text">💡 将清空目标日期的摸鱼记录和收入调整数据</text>
            </view>
          </view>

          <!-- 覆盖提示 -->
          <view wx:if="{{hasDataConflict}}" class="data-conflict-warning">
            <view class="warning-header">
              <text class="warning-icon">⚠️</text>
              <text class="warning-title">数据覆盖提示</text>
            </view>
            <text class="warning-text">选中的日期中有 {{conflictDatesCount}} 个日期已有数据，复制操作将覆盖这些日期的状态和时间段数据，摸鱼记录、收入调整将被清空。</text>
          </view>

        </view>
      </view>
    </view>

    <!-- 选择状态显示（固定在底部） -->
    <view wx:if="{{operation === 'copy' && batchStep === 2}}" class="batch-selection-status">
      <view class="selection-summary">
        <view class="summary-item">
          <text class="summary-label">已选择：</text>
          <text class="summary-value">{{selectedTargetDates.length}} 个日期</text>
        </view>
      </view>

    </view>

    <view class="modal-footer">
      <view wx:if="{{operation === 'copy' && batchStep === 2}}" class="btn-secondary modal-btn" bind:tap="onBackToSourceSelection">
        <text>上一步</text>
      </view>
      <view class="btn-primary modal-btn {{(operation === 'copy' && batchStep === 1 && !selectedSourceDate) || (operation === 'copy' && batchStep === 2 && (selectedTargetDates.length === 0 || batchCopyInProgress || batchCopyCompleted)) ? 'disabled' : ''}}" bind:tap="{{operation === 'import' ? 'onConfirmBatchOperation' : (batchStep === 1 ? 'onConfirmSourceDate' : 'onConfirmBatchOperation')}}">
        <text wx:if="{{operation === 'import'}}">导入</text>
        <text wx:elif="{{batchStep === 1}}">下一步</text>
        <text wx:elif="{{batchCopyCompleted}}">已完成复制</text>
        <text wx:elif="{{batchCopyInProgress}}">复制中...</text>
        <text wx:else>开始复制</text>
      </view>
    </view>
  </view>
</view>
