// 圆形进度条组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 进度值 (0-100)
    progress: {
      type: Number,
      value: 0
    },
    
    // 圆形大小 (rpx)
    size: {
      type: Number,
      value: 200
    },
    
    // 线条宽度 (rpx)
    strokeWidth: {
      type: Number,
      value: 8
    },
    
    // 进度条颜色
    strokeColor: {
      type: String,
      value: '#667eea'
    },
    
    // 背景色
    backgroundColor: {
      type: String,
      value: '#f0f0f0'
    },
    
    // 中心内容
    centerText: {
      type: String,
      value: ''
    },
    
    // 中心副标题
    centerSubtext: {
      type: String,
      value: ''
    },
    
    // 是否显示百分比
    showPercent: {
      type: Boolean,
      value: false
    },
    
    // 动画持续时间 (ms)
    animationDuration: {
      type: Number,
      value: 1000
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // SVG相关计算值
    radius: 0,
    circumference: 0,
    strokeDasharray: '',
    strokeDashoffset: 0,

    // 动画相关
    currentProgress: 0,
    animationTimer: null,

    // 样式相关
    sizeClass: ''
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      this.calculateCircle()
      this.updateProgress()
      this.updateSizeClass()
    },

    detached: function() {
      this.clearAnimation()
    }
  },

  /**
   * 属性变化监听
   */
  observers: {
    'progress': function(newProgress) {
      this.updateProgress(newProgress)
    },

    'size, strokeWidth': function() {
      this.calculateCircle()
      this.updateProgress()
      this.updateSizeClass()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算圆形相关参数
     */
    calculateCircle: function() {
      const size = this.data.size
      const strokeWidth = this.data.strokeWidth
      
      // 计算半径（考虑线条宽度）
      const radius = (size - strokeWidth) / 2
      
      // 计算周长
      const circumference = 2 * Math.PI * radius
      
      this.setData({
        radius: radius,
        circumference: circumference,
        strokeDasharray: circumference
      })
      
      console.log('圆形参数计算完成:', { size, strokeWidth, radius, circumference })
    },

    /**
     * 更新进度
     */
    updateProgress: function(targetProgress) {
      if (targetProgress === undefined) {
        targetProgress = this.data.progress
      }
      
      // 限制进度值范围
      targetProgress = Math.max(0, Math.min(100, targetProgress))
      
      // 清除之前的动画
      this.clearAnimation()
      
      // 如果没有动画时间，直接设置
      if (this.data.animationDuration <= 0) {
        this.setProgressValue(targetProgress)
        return
      }
      
      // 执行动画
      this.animateProgress(targetProgress)
    },

    /**
     * 执行进度动画
     */
    animateProgress: function(targetProgress) {
      const startProgress = this.data.currentProgress
      const progressDiff = targetProgress - startProgress
      const duration = this.data.animationDuration
      const startTime = Date.now()
      
      const animate = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        
        // 使用缓动函数
        const easeProgress = this.easeOutCubic(progress)
        const currentProgress = startProgress + (progressDiff * easeProgress)
        
        this.setProgressValue(currentProgress)
        
        if (progress < 1) {
          this.data.animationTimer = setTimeout(animate, 16) // 约60fps
        } else {
          this.setProgressValue(targetProgress)
        }
      }
      
      animate()
    },

    /**
     * 设置进度值
     */
    setProgressValue: function(progress) {
      const circumference = this.data.circumference
      const offset = circumference - (progress / 100) * circumference
      
      this.setData({
        currentProgress: progress,
        strokeDashoffset: offset
      })
    },

    /**
     * 缓动函数 - 三次方缓出
     */
    easeOutCubic: function(t) {
      return 1 - Math.pow(1 - t, 3)
    },

    /**
     * 清除动画
     */
    clearAnimation: function() {
      if (this.data.animationTimer) {
        clearTimeout(this.data.animationTimer)
        this.setData({
          animationTimer: null
        })
      }
    },

    /**
     * 获取显示文本
     */
    getDisplayText: function() {
      if (this.data.centerText) {
        return this.data.centerText
      }

      if (this.data.showPercent) {
        return Math.round(this.data.currentProgress) + '%'
      }

      return ''
    },

    /**
     * 更新尺寸相关的CSS类
     */
    updateSizeClass: function() {
      const size = this.data.size
      let sizeClass = ''

      if (size <= 120) {
        sizeClass = 'size-small'
      } else if (size >= 300) {
        sizeClass = 'size-large'
      }

      this.setData({
        sizeClass: sizeClass
      })
    }
  }
})
