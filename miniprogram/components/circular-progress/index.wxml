<!-- 圆形进度条组件 -->
<view class="circular-progress {{sizeClass}}" style="width: {{size}}rpx; height: {{size}}rpx;">
  <!-- SVG圆形进度条 -->
  <svg 
    class="progress-svg" 
    width="{{size}}rpx" 
    height="{{size}}rpx"
    viewBox="0 0 {{size}} {{size}}">
    
    <!-- 背景圆环 -->
    <circle
      class="progress-background"
      cx="{{size / 2}}"
      cy="{{size / 2}}"
      r="{{radius}}"
      fill="none"
      stroke="{{backgroundColor}}"
      stroke-width="{{strokeWidth}}"
    />
    
    <!-- 进度圆环 -->
    <circle
      class="progress-foreground"
      cx="{{size / 2}}"
      cy="{{size / 2}}"
      r="{{radius}}"
      fill="none"
      stroke="{{strokeColor}}"
      stroke-width="{{strokeWidth}}"
      stroke-dasharray="{{strokeDasharray}}"
      stroke-dashoffset="{{strokeDashoffset}}"
      stroke-linecap="round"
      transform="rotate(-90 {{size / 2}} {{size / 2}})"
    />
  </svg>
  
  <!-- 中心内容 -->
  <view class="progress-center">
    <!-- 主要文本 -->
    <view wx:if="{{centerText || showPercent}}" class="center-text">
      {{centerText || (Math.round(currentProgress) + '%')}}
    </view>
    
    <!-- 副标题 -->
    <view wx:if="{{centerSubtext}}" class="center-subtext">
      {{centerSubtext}}
    </view>
    
    <!-- 插槽内容 -->
    <slot></slot>
  </view>
</view>
