/* 圆形进度条组件样式 */
.circular-progress {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* SVG样式 */
.progress-svg {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(0deg);
}

/* 背景圆环 */
.progress-background {
  opacity: 0.3;
}

/* 进度圆环 */
.progress-foreground {
  transition: stroke-dashoffset 0.3s ease;
}

/* 中心内容容器 */
.progress-center {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 主要文本 */
.center-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  margin-bottom: 8rpx;
}

/* 副标题 */
.center-subtext {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  line-height: 1.3;
}

/* 响应式调整 */
.circular-progress.size-small .center-text {
  font-size: 32rpx;
}

.circular-progress.size-small .center-subtext {
  font-size: 20rpx;
}

.circular-progress.size-large .center-text {
  font-size: 64rpx;
}

.circular-progress.size-large .center-subtext {
  font-size: 28rpx;
}
