// 日收入计算器组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 默认月收入
    defaultMonthlyIncome: {
      type: Number,
      value: 10000
    },
    // 默认工作天数
    defaultWorkDays: {
      type: Number,
      value: 21.75
    },
    // 目标模式：'total' 或 'overtime'
    targetMode: {
      type: String,
      value: 'total',
      optionalTypes: [null]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 月收入输入值
    monthlyIncome: '',
    // 工作天数输入值
    workDays: '',
    // 计算结果
    result: '0.00',
    // 模态框动画状态
    modalVisible: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      if (this.data.visible) {
        this.setData({
          monthlyIncome: this.properties.defaultMonthlyIncome.toString(),
          workDays: this.properties.defaultWorkDays.toString()
        }, () => {
          this.calculateResult()
          // 延迟显示动画
          setTimeout(() => {
            this.setData({ modalVisible: true })
          }, 50)
        })
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 月收入输入变化
     */
    onMonthlyIncomeChange(e) {
      let value = this.limitDecimalPlaces(e.detail.value, 2)
      const numValue = parseFloat(value) || 0

      if (numValue > 1000000000) {
        wx.showToast({
          title: '月收入不能超过1000,000,000元',
          icon: 'none'
        })
        return
      }

      this.setData({
        monthlyIncome: value
      }, () => {
        this.calculateResult()
      })
    },

    /**
     * 工作天数输入变化
     */
    onWorkDaysChange(e) {
      let value = this.limitDecimalPlaces(e.detail.value, 2)
      const numValue = parseFloat(value) || 0

      if (numValue > 31) {
        wx.showToast({
          title: '工作天数不能超过31天',
          icon: 'none'
        })
        return
      }

      this.setData({
        workDays: value
      }, () => {
        this.calculateResult()
      })
    },

    /**
     * 计算日收入结果
     */
    calculateResult() {
      const workDays = parseFloat(this.data.workDays) || 0
      const monthlyIncome = parseFloat(this.data.monthlyIncome) || 0

      let dailyIncome = 0
      if (workDays > 0) {
        dailyIncome = monthlyIncome / workDays
      }

      this.setData({
        result: dailyIncome.toFixed(2)
      })
    },

    /**
     * 确认使用计算结果
     */
    onConfirm() {
      const result = parseFloat(this.data.result) || 0
      const targetMode = this.properties.targetMode || 'total'

      this.triggerEvent('confirm', {
        result: result,
        targetMode: targetMode
      })

      this.onClose()
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 限制小数位数
     */
    limitDecimalPlaces(value, maxDecimalPlaces) {
      if (!value || value === '') return ''

      // 移除非数字和小数点的字符
      value = value.replace(/[^\d.]/g, '')

      // 处理以小数点开头的情况：.123 -> 0.123
      if (value.startsWith('.')) {
        value = '0' + value
      }

      // 确保只有一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        // 保留第一个小数点，移除后续的小数点
        value = parts[0] + '.' + parts.slice(1).join('').replace(/\./g, '')
      }

      // 重新分割以获取正确的部分
      const finalParts = value.split('.')

      // 处理前导零的情况
      if (finalParts[0].length > 1 && finalParts[0].startsWith('0')) {
        // 移除前导零，但保留单独的0（如0.123）
        finalParts[0] = finalParts[0].replace(/^0+/, '') || '0'
        value = finalParts.join('.')
      }

      // 限制小数位数
      if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
        const integerPart = finalParts[0]
        const decimalPart = finalParts[1].substring(0, maxDecimalPlaces)
        value = integerPart + '.' + decimalPart
      }

      return value
    }
  },

  /**
   * 组件生命周期
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  }
})
