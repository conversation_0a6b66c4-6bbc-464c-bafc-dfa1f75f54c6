<!-- 现代仪表盘组件 -->
<view class="dashboard2-container" style="padding-top: {{navbarHeight}}px;">

  <!-- Dashboard2 导航栏 -->
  <view class="dashboard2-navbar">
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
    <view class="navbar-content">
      <!-- 左侧按钮组 -->
      <view class="navbar-left">
        <view class="navbar-button" bindtap="onShowDashboardSwitcher">
          <text class="button-icon">🔄</text>
        </view>
        <view class="navbar-button" bindtap="onShowSettings">
          <text class="button-icon">⚙️</text>
        </view>
      </view>

      <!-- 中间标题和时间 -->
      <view class="navbar-center">
        <text class="navbar-title">时间跟踪器</text>
        <view class="datetime-display">
          <text class="current-date">{{currentDateString}}</text>
          <text class="current-time">{{currentTime}}</text>
        </view>
      </view>

      <!-- 右侧留空，避免被微信菜单覆盖 -->
      <view class="navbar-right">
      </view>
    </view>
  </view>
  <!-- 没有工作履历时的引导 -->
  <view wx:if="{{!hasWorkHistory}}" class="no-work-guide">
    <view class="guide-content">
      <view class="guide-icon">💼</view>
      <view class="guide-title">欢迎使用现代仪表盘</view>
      <view class="guide-text">
        <text>请先添加您的工作履历以使用完整功能</text>
      </view>
      <button class="guide-btn" bindtap="goToWorkHistory">
        <text class="btn-icon">✨</text>
        <text>添加工作履历</text>
      </button>
    </view>
  </view>

  <!-- 有工作履历时显示的内容 -->
  <view wx:else class="main-content" style="min-height: calc(100vh - {{navbarHeight}}px);">
    <!-- 当前工作显示 -->
    <view class="current-work-section" wx:if="{{showCurrentWork}}">
      <view class="work-header">
        <view class="work-icon">💼</view>
        <view class="work-title">当前工作</view>
      </view>
      <view class="work-content">
        <view class="work-company" bindtap="toggleCompanyMask">
          <text class="work-label">公司</text>
          <text class="work-value">{{companyMasked ? '***' : currentCompany}}</text>
          <text class="mask-toggle">{{companyMasked ? '👁️' : '🙈'}}</text>
        </view>
        <view class="work-position" bindtap="togglePositionMask">
          <text class="work-label">职位</text>
          <text class="work-value">{{positionMasked ? '***' : currentPosition}}</text>
          <text class="mask-toggle">{{positionMasked ? '👁️' : '🙈'}}</text>
        </view>
      </view>
    </view>



    <!-- 收入统计卡片组 -->
    <view class="income-stats">
      <!-- 工作收入卡片 -->
      <view class="income-card work-income" bindtap="togglePrivacyMask" data-field="workIncome">
        <view class="income-icon">💼</view>
        <view class="income-info">
          <text class="income-value">{{currencySymbol}}{{privacyMask.workIncome ? workIncomeMasked : workIncome}}</text>
          <text class="income-label">工作收入</text>
        </view>
      </view>

      <!-- 摸鱼收入卡片 -->
      <view class="income-card fishing-income" bindtap="togglePrivacyMask" data-field="fishingIncome">
        <view class="income-icon">🐟</view>
        <view class="income-info">
          <text class="income-value">{{currencySymbol}}{{privacyMask.fishingIncome ? fishingIncomeMasked : fishingIncome}}</text>
          <text class="income-label">摸鱼收入</text>
        </view>
      </view>

      <!-- 额外收入卡片 -->
      <view class="income-card extra-income" wx:if="{{todayExtraIncomeValue > 0}}" bindtap="togglePrivacyMask" data-field="extraIncome">
        <view class="income-icon">💎</view>
        <view class="income-info">
          <text class="income-value">+{{currencySymbol}}{{privacyMask.extraIncome ? todayExtraIncomeMasked : todayExtraIncome}}</text>
          <text class="income-label">额外收入</text>
        </view>
      </view>

      <!-- 扣款卡片 -->
      <view class="income-card deduction-income" wx:if="{{todayDeductionsValue > 0}}" bindtap="togglePrivacyMask" data-field="deductions">
        <view class="income-icon">💸</view>
        <view class="income-info">
          <text class="income-value">-{{currencySymbol}}{{privacyMask.deductions ? todayDeductionsMasked : todayDeductions}}</text>
          <text class="income-label">扣款</text>
        </view>
      </view>
    </view>

    <!-- 中央圆形进度条 -->
    <view class="center-progress">
      <circular-progress
        progress="{{countdownProgress}}"
        size="{{400}}"
        stroke-width="{{12}}"
        stroke-color="#ff6b6b"
        background-color="#f0f0f0"
        animation-duration="{{500}}">

        <view class="countdown-display">
          <view class="countdown-time">
            <text class="time-part">{{countdown.hours}}</text>
            <text class="time-separator">:</text>
            <text class="time-part">{{countdown.minutes}}</text>
            <text class="time-separator">:</text>
            <text class="time-part">{{countdown.seconds}}</text>
          </view>
          <view class="countdown-text">{{countdown.text}}</view>
          <view class="progress-text">进度 {{Math.round(countdownProgress)}}%</view>
        </view>
      </circular-progress>
    </view>

    <!-- 摸鱼控制 -->
    <view class="fishing-control-section">
      <fishing-control
        type="default"
        bindfishingstart="onFishingStart"
        bindfishingend="onFishingEnd"
        bindfishingtimeadjusted="onFishingTimeAdjusted">
      </fishing-control>
    </view>

    <!-- 底部统计网格 -->
    <view class="bottom-stats">
      <!-- 第一行 -->
      <view class="stats-row">
        <view class="stat-item" bindtap="togglePrivacyMask" data-field="currentHourlyRate">
          <view class="stat-icon">💵</view>
          <view class="stat-content">
            <text class="stat-value">{{currencySymbol}}{{privacyMask.currentHourlyRate ? currentHourlyRateMasked : currentHourlyRate}}/h</text>
            <text class="stat-label">当前时薪</text>
          </view>
          <view class="stat-mascot">🐱</view>
        </view>

        <view class="stat-item" wx:if="{{showFishingCount}}">
          <view class="stat-icon">🐟</view>
          <view class="stat-content">
            <text class="stat-value" wx:if="{{!fishingCountLoading}}">{{fishingCount}}人</text>
            <text class="stat-value" wx:else>...</text>
            <text class="stat-label">当前摸鱼</text>
          </view>
          <view class="stat-mascot">🐸</view>
        </view>
      </view>

      <!-- 第二行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">📅</view>
          <view class="stat-content">
            <text class="stat-value">还有{{nonWorkDays}}天</text>
            <text class="stat-label">距离非工作日</text>
          </view>
          <view class="stat-mascot">🐼</view>
        </view>

        <view class="stat-item">
          <view class="stat-icon">🏖️</view>
          <view class="stat-content">
            <text class="stat-value">离国庆节{{nextHoliday}}天</text>
            <text class="stat-label">最近的假期</text>
          </view>
          <view class="stat-mascot">🐨</view>
        </view>
      </view>

      <!-- 第三行 -->
      <view class="stats-row">
        <view class="stat-item">
          <view class="stat-icon">💳</view>
          <view class="stat-content">
            <text class="stat-value" wx:if="{{payDayName === '未设置'}}">未设置</text>
            <text class="stat-value" wx:elif="{{payDay == '0' || payDay == 0}}">今天</text>
            <text class="stat-value" wx:else>{{payDay}}天</text>
            <text class="stat-label" wx:if="{{payDayName === '未设置'}}">发薪日期</text>
            <text class="stat-label" wx:elif="{{payDay == '0' || payDay == 0}}">{{payDayName}}</text>
            <text class="stat-label" wx:else>距离{{payDayName}}</text>
          </view>
          <view class="stat-mascot">🦊</view>
        </view>
        
        <view class="stat-item">
          <view class="stat-icon">👔</view>
          <view class="stat-content">
            <text class="stat-value">{{workDays}}天</text>
            <text class="stat-label">你已入职</text>
          </view>
          <view class="stat-mascot">🐯</view>
        </view>
      </view>
    </view>
  </view>

  <!-- Dashboard2 设置模态框 -->
  <view wx:if="{{showSettings}}" class="settings-overlay {{settingsVisible ? 'show' : ''}}" bindtap="onHideSettings">
    <view class="settings-content" catchtap="onModalContentTap">
      <view class="settings-header">
        <text class="settings-title">Dashboard2 设置</text>
        <view class="settings-close" bindtap="onHideSettings">✕</view>
      </view>

      <view class="settings-body">
        <view class="setting-item">
          <text class="setting-label">显示当前工作</text>
          <switch
            checked="{{dashboardConfig.showCurrentWork}}"
            bindchange="onConfigChange"
            data-key="showCurrentWork" />
        </view>

        <view class="setting-item">
          <text class="setting-label">收入小数位数</text>
          <picker
            mode="selector"
            range="{{decimalOptions}}"
            range-key="label"
            value="{{selectedDecimalIndex}}"
            bindchange="onDecimalChange">
            <view class="picker-value">{{decimalOptions[selectedDecimalIndex].label}}</view>
          </picker>
        </view>
      </view>

      <view class="settings-actions">
        <button class="action-btn cancel" bindtap="onHideSettings">取消</button>
        <button class="action-btn confirm" bindtap="onSaveSettings">保存</button>
      </view>
    </view>
  </view>
</view>
