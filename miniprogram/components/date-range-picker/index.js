Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    defaultStartDate: {
      type: String,
      value: ''
    },
    defaultEndDate: {
      type: String,
      value: ''
    }
  },

  data: {
    startDate: '',
    endDate: '',
    dayCount: 0,
    quickOptions: [
      { key: 'last7days', text: '最近7天', active: false },
      { key: 'last30days', text: '最近30天', active: false },
      { key: 'last90days', text: '最近90天', active: false },
      { key: 'thisMonth', text: '本月', active: false },
      { key: 'lastMonth', text: '上月', active: false },
      { key: 'thisYear', text: '今年', active: false }
    ]
  },

  observers: {
    'visible': function(visible) {
      if (visible) {
        this.setData({
          startDate: this.properties.defaultStartDate,
          endDate: this.properties.defaultEndDate
        })
        this.calculateDayCount()
      }
    },
    'startDate, endDate': function() {
      this.calculateDayCount()
    }
  },

  methods: {
    /**
     * 计算天数
     */
    calculateDayCount: function() {
      const { startDate, endDate } = this.data
      if (startDate && endDate) {
        const start = new Date(startDate)
        const end = new Date(endDate)
        const dayCount = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1
        this.setData({ dayCount })
      } else {
        this.setData({ dayCount: 0 })
      }
    },

    /**
     * 快捷选择
     */
    onQuickSelect: function(event) {
      const { key } = event.currentTarget.dataset
      const today = new Date()
      let startDate, endDate

      switch (key) {
        case 'last7days':
          endDate = this.formatDate(today)
          startDate = this.formatDate(new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000))
          break
        case 'last30days':
          endDate = this.formatDate(today)
          startDate = this.formatDate(new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000))
          break
        case 'last90days':
          endDate = this.formatDate(today)
          startDate = this.formatDate(new Date(today.getTime() - 89 * 24 * 60 * 60 * 1000))
          break
        case 'thisMonth':
          startDate = this.formatDate(new Date(today.getFullYear(), today.getMonth(), 1))
          endDate = this.formatDate(today)
          break
        case 'lastMonth':
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
          startDate = this.formatDate(lastMonth)
          endDate = this.formatDate(new Date(today.getFullYear(), today.getMonth(), 0))
          break
        case 'thisYear':
          startDate = this.formatDate(new Date(today.getFullYear(), 0, 1))
          endDate = this.formatDate(today)
          break
      }

      // 更新快捷选项状态
      const quickOptions = this.data.quickOptions.map(option => ({
        ...option,
        active: option.key === key
      }))

      this.setData({
        startDate,
        endDate,
        quickOptions
      })
    },

    /**
     * 开始日期变更
     */
    onStartDateChange: function(event) {
      this.setData({
        startDate: event.detail.value,
        quickOptions: this.data.quickOptions.map(option => ({ ...option, active: false }))
      })
    },

    /**
     * 结束日期变更
     */
    onEndDateChange: function(event) {
      this.setData({
        endDate: event.detail.value,
        quickOptions: this.data.quickOptions.map(option => ({ ...option, active: false }))
      })
    },

    /**
     * 确认选择
     */
    onConfirm: function() {
      const { startDate, endDate } = this.data
      
      if (!startDate || !endDate) {
        wx.showToast({
          title: '请选择完整的时间范围',
          icon: 'none'
        })
        return
      }

      if (new Date(startDate) > new Date(endDate)) {
        wx.showToast({
          title: '开始日期不能晚于结束日期',
          icon: 'none'
        })
        return
      }

      this.triggerEvent('confirm', {
        startDate,
        endDate,
        dayCount: this.data.dayCount
      })
    },

    /**
     * 取消选择
     */
    onCancel: function() {
      this.triggerEvent('cancel')
    },

    /**
     * 格式化日期
     */
    formatDate: function(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
})
