<!--自定义日期范围选择器-->
<view class="date-range-picker" wx:if="{{visible}}">
  <view class="picker-mask" bindtap="onCancel"></view>
  <view class="picker-container">
    <view class="picker-header">
      <view class="header-title">选择时间范围</view>
      <view class="header-actions">
        <view class="action-btn cancel" bindtap="onCancel">取消</view>
        <view class="action-btn confirm" bindtap="onConfirm">确定</view>
      </view>
    </view>
    
    <view class="picker-content">
      <!-- 快捷选择 -->
      <view class="quick-select">
        <view class="quick-title">快捷选择</view>
        <view class="quick-options">
          <view class="quick-option {{item.active ? 'active' : ''}}" 
                wx:for="{{quickOptions}}" 
                wx:key="key"
                data-key="{{item.key}}"
                bindtap="onQuickSelect">
            {{item.text}}
          </view>
        </view>
      </view>
      
      <!-- 自定义日期选择 -->
      <view class="custom-select">
        <view class="date-section">
          <view class="date-label">开始日期</view>
          <picker mode="date" 
                  value="{{startDate}}" 
                  bindchange="onStartDateChange"
                  class="date-picker">
            <view class="picker-text">{{startDate || '请选择开始日期'}}</view>
          </picker>
        </view>
        
        <view class="date-section">
          <view class="date-label">结束日期</view>
          <picker mode="date" 
                  value="{{endDate}}" 
                  bindchange="onEndDateChange"
                  class="date-picker">
            <view class="picker-text">{{endDate || '请选择结束日期'}}</view>
          </picker>
        </view>
      </view>
      
      <!-- 选择结果预览 -->
      <view class="result-preview" wx:if="{{startDate && endDate}}">
        <view class="preview-title">已选择时间范围</view>
        <view class="preview-text">{{startDate}} 至 {{endDate}}</view>
        <view class="preview-days">共 {{dayCount}} 天</view>
      </view>
    </view>
  </view>
</view>
