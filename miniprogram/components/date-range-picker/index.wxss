/* 自定义日期范围选择器样式 */
.date-range-picker {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.picker-container {
  width: 100%;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1001;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #e2e8f0;
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.header-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.action-btn.cancel {
  background: #f1f5f9;
  color: #64748b;
}

.action-btn.confirm {
  background: #3b82f6;
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.95);
}

.picker-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 快捷选择 */
.quick-select {
  margin-bottom: 40rpx;
}

.quick-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20rpx;
}

.quick-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-option {
  padding: 12rpx 24rpx;
  background: #f1f5f9;
  border: 1rpx solid #e2e8f0;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #64748b;
  transition: all 0.3s ease;
}

.quick-option.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.quick-option:active {
  transform: scale(0.95);
}

/* 自定义日期选择 */
.custom-select {
  margin-bottom: 40rpx;
}

.date-section {
  margin-bottom: 24rpx;
}

.date-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12rpx;
}

.date-picker {
  width: 100%;
}

.picker-text {
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1e293b;
}

/* 结果预览 */
.result-preview {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.preview-title {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}

.preview-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.preview-days {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
}
