<!-- 日期类型选择器 -->
<view class="date-type-selector {{visible ? 'show' : ''}}" wx:if="{{show}}" bind:tap="onClose">
  <view class="selector-content" catch:tap="onStopPropagation">

    <!-- 模态框头部 -->
    <view class="selector-header">
      <view class="header-title">{{title}}</view>
      <view class="header-close" bind:tap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 分类列表 -->
    <view class="categories-container">
      <view class="category-section" wx:for="{{categories}}" wx:key="id" wx:for-item="category">
        
        <!-- 分类标题 -->
        <view class="category-header">
          <view class="category-icon">{{category.icon}}</view>
          <view class="category-info">
            <view class="category-name">{{category.name}}</view>
            <view class="category-description">{{category.description}}</view>
          </view>
        </view>

        <!-- 类型列表 -->
        <view class="types-grid">
          <view
            class="type-item {{item.enabled ? 'type-enabled' : 'type-disabled'}} {{selectedValue === item.value ? 'type-selected' : ''}}"
            wx:for="{{category.types}}"
            wx:key="value"
            wx:for-item="item"
            bind:tap="onSelectType"
            data-value="{{item.value}}"
            data-enabled="{{item.enabled}}"
            style="{{item.enabled && selectedValue === item.value ? '--type-color: ' + item.color + '; --type-bg-color: ' + item.backgroundColor + '; --type-border-color: ' + item.borderColor + ';' : ''}}">
            
            <view class="type-icon">{{item.icon}}</view>
            <view class="type-text">{{item.text}}</view>
            
            <!-- 禁用遮罩 -->
            <view class="type-disabled-mask" wx:if="{{!item.enabled}}">
              <text class="disabled-text">敬请期待</text>
            </view>
            
            <!-- 选中指示器 -->
            <view class="type-selected-indicator" wx:if="{{selectedValue === item.value && item.enabled}}">
              <text class="selected-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="selector-actions">
      <view class="action-btn cancel-btn" bind:tap="onCancel">
        <text>取消</text>
      </view>
      <view class="action-btn confirm-btn" bind:tap="onConfirm">
        <text>确定</text>
      </view>
    </view>
  </view>
</view>
