/**
 * 摸鱼控制组件
 * 提供开启/关闭摸鱼的界面和功能
 */

const { formatMillisecondsDuration } = require('../../utils/helpers/time-utils.js')

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示组件
    show: {
      type: Boolean,
      value: true
    },
    // 组件样式类型
    type: {
      type: String,
      value: 'default' // default, compact
    }
  },

  data: {
    // 摸鱼状态
    isFishing: false,
    fishingState: null,

    // 摸鱼时长显示
    fishingDuration: '00:00:00',

    // 界面状态
    loading: false,

    // 备注编辑器
    showRemarkEditor: false,

    // 定时器
    timer: null,

    // 时间段状态
    timeSegmentStatus: 'no_segment', // working, resting, no_segment
    canStartFishing: false, // 是否可以开始摸鱼
    statusMessage: '', // 状态提示信息

    // 时间调整按钮状态
    canIncrease: false, // 是否可以增加摸鱼时间
    canDecrease: false  // 是否可以减少摸鱼时间
  },

  lifetimes: {
    attached() {
      this.initComponent()
    },

    detached() {
      this.cleanup()
    }
  },

  methods: {
    /**
     * 初始化组件
     */
    initComponent() {
      this.checkFishingState()
      this.startTimer()
      this.updateTimeSegmentStatus()
    },

    /**
     * 清理资源
     */
    cleanup() {
      if (this.data.timer) {
        clearInterval(this.data.timer)
        this.setData({ timer: null })
      }
    },

    /**
     * 检查摸鱼状态
     */
    checkFishingState() {
      try {
        const dataManager = getApp().getDataManager()

        // 检查并处理过期的摸鱼状态（仅在应用启动时需要）
        const expiredResult = dataManager.checkAndHandleExpiredFishing()

        if (expiredResult.autoEnded) {
          // 显示自动结束的提示
          wx.showToast({
            title: expiredResult.message,
            icon: 'none',
            duration: 3000
          })
        }

        // 获取当前摸鱼状态
        const fishingState = dataManager.getCurrentFishingState()
        const isFishing = !!(fishingState && fishingState.isActive)

        // 检查状态是否发生变化
        const stateChanged = this.data.isFishing !== isFishing

        this.setData({
          isFishing: isFishing,
          fishingState: fishingState
        })

        if (fishingState && fishingState.isActive) {
          this.updateFishingDuration()
          this.updateTimeAdjustButtons()
        }

      } catch (error) {
        console.error('检查摸鱼状态失败:', error)
      }
    },

    /**
     * 启动定时器
     */
    startTimer() {
      if (this.data.timer) {
        clearInterval(this.data.timer)
      }

      const timer = setInterval(() => {
        if (this.data.isFishing) {
          this.updateFishingDuration()
          this.updateTimeAdjustButtons()
        }
      }, 1000)

      this.setData({ timer })
    },

    /**
     * 更新摸鱼时长显示
     */
    updateFishingDuration() {
      const fishingState = this.data.fishingState
      if (!fishingState || !fishingState.isActive) {
        return
      }

      try {
        // 使用统一的摸鱼时长计算
        const dataManager = getApp().getDataManager()
        const formattedDuration = dataManager.getCurrentFishingDurationString()

        this.setData({
          fishingDuration: formattedDuration
        })
      } catch (error) {
        console.error('更新摸鱼时长失败:', error)
      }
    },

    /**
     * 开始摸鱼按钮点击
     */
    onStartFishing() {
      if (this.data.isFishing) {
        return
      }

      // 检查是否可以开始摸鱼
      if (!this.data.canStartFishing) {
        wx.showToast({
          title: this.data.statusMessage || '当前不在工作时间内',
          icon: 'none',
          duration: 1000
        })
        return
      }

      // 直接开始摸鱼，不弹出备注输入框
      this.doStartFishing('')
    },

    /**
     * 执行开始摸鱼
     * @param {string} remark - 备注
     */
    doStartFishing(remark = '') {
      if (this.data.loading) {
        return
      }

      this.setData({ loading: true })

      try {
        const dataManager = getApp().getDataManager()
        const result = dataManager.startFishing(remark)

        if (result.success) {
          this.setData({
            isFishing: true,
            fishingState: result.fishingState,
            loading: false
          })

          // 触发开始摸鱼事件
          this.triggerEvent('fishingstart', {
            fishingState: result.fishingState
          })

          wx.showToast({
            title: '开始摸鱼',
            icon: 'success'
          })
        } else {
          throw new Error(result.message || '开始摸鱼失败')
        }
      } catch (error) {
        console.error('开始摸鱼失败:', error)
        wx.showToast({
          title: error.message || '开始摸鱼失败',
          icon: 'error'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 结束摸鱼按钮点击
     */
    onEndFishing() {
      if (!this.data.isFishing || this.data.loading) {
        return
      }

      wx.showModal({
        title: '确认结束摸鱼',
        content: '确定要结束当前的摸鱼吗？',
        success: (res) => {
          if (res.confirm) {
            this.performEndFishing()
          }
        }
      })
    },

    /**
     * 执行结束摸鱼
     */
    performEndFishing() {
      this.setData({ loading: true })

      try {
        const dataManager = getApp().getDataManager()
        const result = dataManager.endFishing()

        if (result.success) {
          this.setData({
            isFishing: false,
            fishingState: null,
            fishingDuration: '00:00:00'
          })

          wx.showToast({
            title: `摸鱼结束，时长：${result.duration}`,
            icon: 'success',
            duration: 3000
          })

          // 触发结束摸鱼事件
          this.triggerEvent('fishingend', {
            fishingRecord: result.fishingRecord,
            duration: result.duration
          })
        } else {
          wx.showToast({
            title: result.message,
            icon: 'error'
          })
        }
      } catch (error) {
        console.error('结束摸鱼失败:', error)
        wx.showToast({
          title: '结束摸鱼失败',
          icon: 'error'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 备注输入
     */
    onRemarkInput(e) {
      this.setData({
        remark: e.detail.value
      })
    },

    /**
     * 编辑备注
     */
    onEditRemark() {
      if (!this.data.isFishing) {
        return
      }

      this.setData({
        showRemarkEditor: true
      })
    },

    /**
     * 备注编辑器保存事件
     */
    onRemarkEditorSave(e) {
      const { remark } = e.detail

      // 更新本地状态
      if (this.data.fishingState) {
        this.setData({
          'fishingState.remark': remark,
          showRemarkEditor: false
        })
      }
    },

    /**
     * 备注编辑器取消/关闭事件
     */
    onRemarkEditorClose() {
      this.setData({
        showRemarkEditor: false
      })
    },

    /**
     * 刷新状态
     */
    refresh() {
      this.checkFishingState()
    },

    /**
     * 外部调用的时间调整方法
     * @param {number} adjustMinutes - 调整分钟数，正数增加时长，负数减少时长
     */
    adjustTime(adjustMinutes) {
      this.adjustFishingTime(adjustMinutes)
    },

    /**
     * 更新时间段状态
     */
    updateTimeSegmentStatus() {
      const dataManager = getApp().getDataManager()
      const statusInfo = dataManager.getCurrentTimeSegmentStatus()

      const canStartFishing = statusInfo.isWorking
      let statusMessage = ''

      if (statusInfo.status === 'no_segment') {
        statusMessage = '当前不在任何时间段内'
      } else if (statusInfo.status === 'resting') {
        statusMessage = '当前在休息时间内'
      } else if (statusInfo.status === 'working') {
        statusMessage = '可以开始摸鱼'
      }

      this.setData({
        timeSegmentStatus: statusInfo.status,
        canStartFishing: canStartFishing,
        statusMessage: statusMessage
      })
    },

    /**
     * 更新时间调整按钮状态
     */
    updateTimeAdjustButtons() {
      if (!this.data.isFishing) {
        this.setData({
          canIncrease: false,
          canDecrease: false
        })
        return
      }

      const dataManager = getApp().getDataManager()
      const checkResult = dataManager.checkFishingTimeAdjustment()

      this.setData({
        canIncrease: checkResult.canIncrease || false,
        canDecrease: checkResult.canDecrease || false
      })
    },

    /**
     * 增加摸鱼时间（开始时间提前1分钟）
     */
    onIncreaseFishingTime(e) {
      // 阻止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation()
      }

      if (!this.data.canIncrease) {
        return
      }

      this.adjustFishingTime(1)
    },

    /**
     * 减少摸鱼时间（开始时间延后1分钟）
     */
    onDecreaseFishingTime(e) {
      // 阻止事件冒泡
      if (e && e.stopPropagation) {
        e.stopPropagation()
      }

      if (!this.data.canDecrease) {
        return
      }

      this.adjustFishingTime(-1)
    },

    /**
     * 调整摸鱼时间
     * @param {number} adjustMinutes - 调整分钟数，正数增加时长，负数减少时长
     */
    adjustFishingTime(adjustMinutes) {
      const dataManager = getApp().getDataManager()
      const result = dataManager.adjustFishingTime(adjustMinutes)

      if (result.success) {
        // 更新摸鱼状态
        this.setData({
          fishingState: result.fishingState
        })

        // 立即更新显示
        this.updateFishingDuration()
        this.updateTimeAdjustButtons()

        // 触发时间调整事件，通知父组件更新
        this.triggerEvent('fishingtimeadjusted', {
          fishingState: result.fishingState,
          adjustedMinutes: result.adjustedMinutes
        })

      } else {
        // 显示错误提示
        wx.showToast({
          title: result.message,
          icon: 'none',
          duration: 2000
        })
      }
    }
  }
})
