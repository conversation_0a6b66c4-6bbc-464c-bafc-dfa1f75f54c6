<!--摸鱼备注编辑器-->
<view class="fishing-remark-editor {{visible ? 'show' : ''}}" bindtap="onClose">
  <!-- 编辑器内容 -->
  <view class="editor-content" catchtap="onStopPropagation">
    <!-- 标题栏 -->
    <view class="editor-header">
      <text class="editor-title">编辑摸鱼备注</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <!-- 编辑器主体 -->
    <view class="editor-body">
      <!-- 常用备注 -->
      <view class="quick-remarks-section" wx:if="{{quickRemarks.length > 0}}">
        <view class="section-title">
          <text class="title-icon">⚡</text>
          <text class="title-text">常用备注</text>
        </view>
        
        <view class="quick-remarks-grid">
          <view 
            class="quick-remark-item"
            wx:for="{{quickRemarks}}"
            wx:key="remark"
            data-remark="{{item.remark}}"
            bindtap="onSelectQuickRemark">
            <text class="remark-text">{{item.remark}}</text>
            <text class="remark-count">{{item.count}}次</text>
          </view>
        </view>
      </view>

      <!-- 无常用备注提示 -->
      <view class="no-quick-remarks" wx:if="{{quickRemarks.length === 0}}">
        <view class="section-title">
          <text class="title-icon">⚡</text>
          <text class="title-text">常用备注</text>
        </view>
        <view class="no-remarks-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">暂无常用备注，同一备注使用3次及以上后会在这里显示</text>
        </view>
      </view>

      <!-- 自定义备注输入 -->
      <view class="custom-remark-section">
        <view class="section-title">
          <text class="title-icon">✏️</text>
          <text class="title-text">输入备注</text>
        </view>
        
        <input
          class="remark-input"
          placeholder="输入摸鱼备注..."
          value="{{remarkInput}}"
          maxlength="20"
          bindinput="onRemarkInput"
          auto-focus="{{false}}"
        />
        
        <view class="char-count">{{remarkInput.length}}/20</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="editor-footer">
      <button class="editor-btn cancel-btn" bindtap="onCancel">取消</button>
      <button class="editor-btn save-btn" disabled="{{loading}}" bindtap="onSaveRemark">{{loading ? '保存中...' : '保存'}}</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
    </view>
  </view>
</view>
