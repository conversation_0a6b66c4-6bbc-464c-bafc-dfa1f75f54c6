/* 工作安排导入模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  opacity: 1;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 85%;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.import-modal-content {
  max-height: 90vh;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #f5f5f5;
  color: #666;
}

/* 模态框主体 */
.modal-body {
  padding: 20px;
  max-height: 65vh;
  overflow-y: auto;
}

/* 使用提示 */
.import-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007aff;
}

.tip-icon {
  font-size: 16px;
}

.tip-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 日历样式 */
.import-calendar {
  margin-bottom: 20px;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 8px;
}

.calendar-nav {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-nav:hover {
  background-color: #e9ecef;
}

.nav-arrow {
  font-size: 18px;
  color: #666;
  font-weight: bold;
}

.calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  padding: 8px 0;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.calendar-day.empty {
  cursor: default;
}

.calendar-day.current-month {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.calendar-day.current-month:hover {
  background-color: #e9ecef;
}

.calendar-day.has-data {
  background-color: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.calendar-day.has-data:hover {
  background-color: #bbdefb;
}

.calendar-day.selected {
  background-color: #007aff !important;
  border-color: #007aff !important;
  color: white;
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.calendar-day.day-outside-employment {
  opacity: 0.5;
  cursor: not-allowed;
}

.day-number {
  font-size: 14px;
  font-weight: 500;
  z-index: 1;
}

.calendar-day.selected .day-number {
  color: white;
}

.day-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: bold;
}

.indicator-text {
  font-size: 10px;
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-tip {
  font-size: 14px;
  color: #999;
}

/* 日期详情 */
.date-detail {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.detail-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-emoji {
  font-size: 14px;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-income {
  display: flex;
  align-items: center;
  gap: 8px;
}

.income-label {
  font-size: 14px;
  color: #666;
}

.income-value {
  font-size: 16px;
  font-weight: 600;
  color: #10b981;
}

.detail-segments {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segments-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.segment-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

.segment-time {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.segment-income {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.no-segments-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  border: 1px dashed #e9ecef;
  color: #999;
}

.placeholder-icon {
  font-size: 16px;
}

.placeholder-text {
  font-size: 14px;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.modal-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: #333;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border: 1px solid #007aff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary.disabled {
  background-color: #e9ecef;
  color: #999;
  border-color: #e9ecef;
  cursor: not-allowed;
}

.btn-primary.disabled:hover {
  background-color: #e9ecef;
  color: #999;
  border-color: #e9ecef;
}
