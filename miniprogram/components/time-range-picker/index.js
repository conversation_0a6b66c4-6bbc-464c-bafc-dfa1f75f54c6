/**
 * 时间段选择器组件
 * 提供更好的时间段编辑体验
 */

Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 开始时间 (HH:mm格式)
    startTime: {
      type: String,
      value: '09:00'
    },
    // 结束时间 (HH:mm格式)
    endTime: {
      type: String,
      value: '18:00'
    },
    // 开始时间是否次日
    isStartNextDay: {
      type: Boolean,
      value: false
    },
    // 结束时间是否次日
    isEndNextDay: {
      type: Boolean,
      value: false
    },
    // 标题
    title: {
      type: String,
      value: '设置时间段'
    }
  },

  data: {
    // 小时选项 (0-23)
    hours: [],
    // 分钟选项 (0-59)
    minutes: [],
    
    // 当前选择的开始时间
    selectedStartHour: 9,
    selectedStartMinute: 0,
    startHourIndex: 9,
    startMinuteIndex: 0,
    
    // 当前选择的结束时间
    selectedEndHour: 18,
    selectedEndMinute: 0,
    endHourIndex: 18,
    endMinuteIndex: 0,
    
    // 是否次日
    selectedIsStartNextDay: false,
    selectedIsEndNextDay: false,

    // 结束时间次日选项是否禁用
    isEndNextDayDisabled: false,
    // 开始时间次日选项是否禁用
    isStartNextDayDisabled: false,
    
    // 计算的时长
    duration: ''
  },

  lifetimes: {
    attached() {
      console.log('时间段选择器组件已加载')
      this.initHours()

      // 测试事件绑定
      console.log('组件方法检查:')
      console.log('onStartNextDayTap:', typeof this.onStartNextDayTap)
      console.log('onStartNextDayChange:', typeof this.onStartNextDayChange)
      console.log('onEndNextDayTap:', typeof this.onEndNextDayTap)
      console.log('onEndNextDayChange:', typeof this.onEndNextDayChange)
    }
  },

  observers: {
    'startTime, endTime, isStartNextDay, isEndNextDay': function(startTime, endTime, isStartNextDay, isEndNextDay) {
      console.log('时间段选择器 - 监听到时间变化:', { startTime, endTime, isStartNextDay, isEndNextDay })
      this.parseTimeProps()
    }
  },

  methods: {
    /**
     * 初始化小时和分钟选项
     */
    initHours() {
      const hours = []
      for (let i = 0; i < 24; i++) {
        hours.push(i.toString().padStart(2, '0'))
      }

      const minutes = []
      for (let i = 0; i < 60; i++) {
        minutes.push(i.toString().padStart(2, '0'))
      }

      this.setData({ hours, minutes })
    },

    /**
     * 解析传入的时间属性
     */
    parseTimeProps() {
      const { startTime, endTime, isStartNextDay, isEndNextDay } = this.properties

      // 解析开始时间
      const [startHour, startMinute] = startTime.split(':').map(Number)
      const startMinuteIndex = parseInt(startMinute) // 确保是数字类型

      // 解析结束时间
      const [endHour, endMinute] = endTime.split(':').map(Number)
      const endMinuteIndex = parseInt(endMinute) // 确保是数字类型

      this.setData({
        selectedStartHour: parseInt(startHour),
        selectedStartMinute: parseInt(startMinute),
        startHourIndex: parseInt(startHour),
        startMinuteIndex: parseInt(startMinuteIndex),

        selectedEndHour: parseInt(endHour),
        selectedEndMinute: parseInt(endMinute),
        endHourIndex: parseInt(endHour),
        endMinuteIndex: parseInt(endMinuteIndex),

        selectedIsStartNextDay: isStartNextDay,
        selectedIsEndNextDay: isEndNextDay
      })

      // 初始化时也需要检查次日逻辑
      setTimeout(() => {
        this.checkAndUpdateNextDayLogic()
        this.calculateDuration()
      }, 0)
    },

    /**
     * 开始时间小时变化
     */
    onStartHourChange(e) {
      const hourIndex = e.detail.value
      const hour = parseInt(this.data.hours[hourIndex])

      this.setData({
        startHourIndex: hourIndex,
        selectedStartHour: hour
      })

      console.log('开始时间小时变化:', hour)

      // 手动触发检查，因为observer可能不会立即执行
      setTimeout(() => {
        this.checkAndUpdateNextDayLogic()
        this.calculateDuration()
      }, 0)
    },

    /**
     * 开始时间分钟变化
     */
    onStartMinuteChange(e) {
      const minuteIndex = e.detail.value
      const minute = parseInt(minuteIndex) // 确保是数字类型

      this.setData({
        startMinuteIndex: minuteIndex,
        selectedStartMinute: minute
      })

      console.log('开始时间分钟变化:', minute)

      // 手动触发检查
      setTimeout(() => {
        this.checkAndUpdateNextDayLogic()
        this.calculateDuration()
      }, 0)
    },

    /**
     * 结束时间小时变化
     */
    onEndHourChange(e) {
      const hourIndex = e.detail.value
      const hour = parseInt(this.data.hours[hourIndex])

      this.setData({
        endHourIndex: hourIndex,
        selectedEndHour: hour
      })

      console.log('结束时间小时变化:', hour)

      // 手动触发检查
      setTimeout(() => {
        this.checkAndUpdateNextDayLogic()
        this.calculateDuration()
      }, 0)
    },

    /**
     * 结束时间分钟变化
     */
    onEndMinuteChange(e) {
      const minuteIndex = e.detail.value
      const minute = parseInt(minuteIndex) // 确保是数字类型

      this.setData({
        endMinuteIndex: minuteIndex,
        selectedEndMinute: minute
      })

      console.log('结束时间分钟变化:', minute)

      // 手动触发检查
      setTimeout(() => {
        this.checkAndUpdateNextDayLogic()
        this.calculateDuration()
      }, 0)
    },

    /**
     * 开始时间次日选项点击（label点击）
     */
    onStartNextDayTap(e) {
      console.log('开始时间次日选项label被点击')

      // 如果被禁用，不处理
      if (this.data.isStartNextDayDisabled) {
        console.log('开始时间次日选项被禁用，忽略点击')
        return
      }

      // 切换状态
      const newValue = !this.data.selectedIsStartNextDay
      this.handleStartNextDayChange(newValue)
    },

    /**
     * 开始时间次日选项变化（checkbox change事件）
     */
    onStartNextDayChange(e) {
      console.log('开始时间次日选项checkbox change事件触发:', e.detail)

      // 如果被禁用，不处理
      if (this.data.isStartNextDayDisabled) {
        console.log('开始时间次日选项被禁用，忽略变化')
        return
      }

      // checkbox change事件中，e.detail.value是选中的checkbox的value数组
      const isChecked = e.detail.value.includes('start-next-day')
      console.log('checkbox状态:', isChecked)

      this.handleStartNextDayChange(isChecked)
    },

    /**
     * 处理开始时间次日状态变化
     */
    handleStartNextDayChange(isStartNextDay) {
      console.log('处理开始时间次日状态变化:', isStartNextDay, '当前状态:', this.data.selectedIsStartNextDay)

      // 如果状态没有变化，不处理
      if (isStartNextDay === this.data.selectedIsStartNextDay) {
        console.log('状态没有变化，不处理')
        return
      }

      // 如果开始时间勾选了次日，结束时间也必须是次日
      const updateData = {
        selectedIsStartNextDay: isStartNextDay
      }

      if (isStartNextDay) {
        // 开始时间是次日，结束时间也必须是次日并禁用
        updateData.selectedIsEndNextDay = true
        updateData.isEndNextDayDisabled = true
        console.log('开始时间设为次日，自动设置结束时间为次日并禁用')
      } else {
        // 开始时间不是次日，恢复结束时间次日选项
        updateData.isEndNextDayDisabled = false
        console.log('开始时间取消次日，恢复结束时间次日选项')
      }

      this.setData(updateData, () => {
        console.log('开始时间次日状态已更新为:', this.data.selectedIsStartNextDay)
        console.log('结束时间次日状态已更新为:', this.data.selectedIsEndNextDay)
        // 立即计算时长
        this.calculateDuration()
      })
    },

    /**
     * 结束时间次日选项点击（label点击）
     */
    onEndNextDayTap(e) {
      console.log('结束时间次日选项label被点击')

      // 如果被禁用，不处理
      if (this.data.isEndNextDayDisabled) {
        console.log('结束时间次日选项被禁用，忽略点击')
        return
      }

      // 切换状态
      const newValue = !this.data.selectedIsEndNextDay
      this.handleEndNextDayChange(newValue)
    },

    /**
     * 结束时间次日选项变化（checkbox change事件）
     */
    onEndNextDayChange(e) {
      console.log('结束时间次日选项checkbox change事件触发:', e.detail)

      // 如果被禁用，不处理
      if (this.data.isEndNextDayDisabled) {
        console.log('结束时间次日选项被禁用，忽略变化')
        return
      }

      // checkbox change事件中，e.detail.value是选中的checkbox的value数组
      const isChecked = e.detail.value.includes('end-next-day')
      console.log('checkbox状态:', isChecked)

      this.handleEndNextDayChange(isChecked)
    },

    /**
     * 处理结束时间次日状态变化
     */
    handleEndNextDayChange(isEndNextDay) {
      console.log('处理结束时间次日状态变化:', isEndNextDay, '当前状态:', this.data.selectedIsEndNextDay)

      // 如果状态没有变化，不处理
      if (isEndNextDay === this.data.selectedIsEndNextDay) {
        console.log('状态没有变化，不处理')
        return
      }

      this.setData({
        selectedIsEndNextDay: isEndNextDay
      }, () => {
        console.log('结束时间次日状态已更新为:', this.data.selectedIsEndNextDay)
        // 立即计算时长
        this.calculateDuration()
      })
    },

    /**
     * 检查并更新次日逻辑
     */
    checkAndUpdateNextDayLogic() {
      const { selectedStartHour, selectedStartMinute, selectedEndHour, selectedEndMinute, selectedIsStartNextDay, selectedIsEndNextDay } = this.data

      // 计算开始时间和结束时间的分钟数（不考虑次日标记）
      const startMinutes = selectedStartHour * 60 + selectedStartMinute
      const endMinutes = selectedEndHour * 60 + selectedEndMinute

      console.log('检查次日逻辑:', { startMinutes, endMinutes, selectedIsStartNextDay, selectedIsEndNextDay })

      // 如果开始时间已经是次日，结束时间必须也是次日
      if (selectedIsStartNextDay) {
        console.log('开始时间是次日，结束时间也必须是次日')
        if (!selectedIsEndNextDay || !this.data.isEndNextDayDisabled) {
          this.setData({
            selectedIsEndNextDay: true,
            isEndNextDayDisabled: true
          })
        }
        return
      }

      // 如果开始时间 >= 结束时间，且结束时间已经勾选次日
      if (startMinutes >= endMinutes && selectedIsEndNextDay) {
        console.log('开始时间 >= 结束时间，且结束时间已勾选次日，禁用开始时间次日选项')

        // 禁用开始时间次日选项，确保开始时间不是次日
        this.setData({
          selectedIsStartNextDay: false,
          isStartNextDayDisabled: true
        })

        console.log('已禁用开始时间次日选项')
        return
      }

      // 如果开始时间 >= 结束时间（在同一天的情况下）
      if (startMinutes >= endMinutes && !selectedIsEndNextDay) {
        console.log('需要自动设置结束时间为次日，并禁用开始时间次日选项')

        // 自动勾选结束时间次日，并禁用该选项
        // 同时取消勾选开始时间次日，并禁用该选项
        this.setData({
          selectedIsEndNextDay: true,
          isEndNextDayDisabled: true,
          selectedIsStartNextDay: false,
          isStartNextDayDisabled: true
        })

        console.log('已自动设置结束时间为次日，并禁用开始时间次日选项')
      } else {
        // 恢复两个次日选项的可用状态（除非开始时间是次日）
        const needUpdate = this.data.isEndNextDayDisabled || this.data.isStartNextDayDisabled
        if (needUpdate && !selectedIsStartNextDay) {
          console.log('恢复次日选项可用状态')
          this.setData({
            isEndNextDayDisabled: false,
            isStartNextDayDisabled: false
          })
        }
      }
    },

    /**
     * 计算时间段时长
     */
    calculateDuration() {
      const { selectedStartHour, selectedStartMinute, selectedEndHour, selectedEndMinute, selectedIsStartNextDay, selectedIsEndNextDay } = this.data

      // 确保所有值都是数字类型
      const startHour = parseInt(selectedStartHour)
      const startMinute = parseInt(selectedStartMinute)
      const endHour = parseInt(selectedEndHour)
      const endMinute = parseInt(selectedEndMinute)

      // 转换为分钟，考虑次日标记
      let startMinutes = startHour * 60 + startMinute
      let endMinutes = endHour * 60 + endMinute

      // 如果开始时间是次日，加24小时
      if (selectedIsStartNextDay) {
        startMinutes += 24 * 60
      }

      // 如果结束时间是次日，加24小时
      if (selectedIsEndNextDay) {
        endMinutes += 24 * 60
      }

      // 计算时长
      let durationMinutes = endMinutes - startMinutes

      // 如果时长为负数或0，显示错误（这种情况应该已经被checkAndUpdateNextDayLogic处理了）
      if (durationMinutes <= 0) {
        this.setData({ duration: '时间设置无效' })
        return
      }

      // 转换为小时和分钟
      const hours = Math.floor(durationMinutes / 60)
      const minutes = durationMinutes % 60

      let duration = ''
      if (hours > 0) {
        duration += `${hours}小时`
      }
      if (minutes > 0) {
        duration += `${minutes}分钟`
      }
      if (duration === '') {
        duration = '0分钟'
      }

      this.setData({
        duration: duration
      }, () => {
        console.log('时长已更新到界面:', this.data.duration)
      })

      console.log('计算时长:', duration, {
        startHour,
        startMinute,
        endHour,
        endMinute,
        startMinutes,
        endMinutes,
        durationMinutes,
        hours,
        minutes,
        selectedIsStartNextDay,
        selectedIsEndNextDay
      })
    },



    /**
     * 确认选择
     */
    onConfirm() {
      const { selectedStartHour, selectedStartMinute, selectedEndHour, selectedEndMinute, selectedIsStartNextDay, selectedIsEndNextDay } = this.data

      const startTime = `${selectedStartHour.toString().padStart(2, '0')}:${selectedStartMinute.toString().padStart(2, '0')}`
      const endTime = `${selectedEndHour.toString().padStart(2, '0')}:${selectedEndMinute.toString().padStart(2, '0')}`

      console.log('时间段选择器 - 确认选择:', { startTime, endTime, isStartNextDay: selectedIsStartNextDay, isEndNextDay: selectedIsEndNextDay })

      this.triggerEvent('confirm', {
        startTime,
        endTime,
        isStartNextDay: selectedIsStartNextDay,
        isEndNextDay: selectedIsEndNextDay,
        duration: this.data.duration
      })
    },

    /**
     * 取消选择
     */
    onCancel() {
      console.log('时间段选择器 - 取消选择')
      this.triggerEvent('cancel')
    },

    /**
     * 关闭选择器
     */
    onClose() {
      console.log('时间段选择器 - 关闭选择器')
      this.triggerEvent('close')
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止事件冒泡，防止点击内容区域时关闭模态框
    }
  }
})
