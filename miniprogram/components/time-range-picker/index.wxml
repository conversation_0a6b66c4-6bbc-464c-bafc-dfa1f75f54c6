<!-- 时间段选择器 -->
<view class="time-range-picker" wx:if="{{show}}" bind:tap="onClose">
  <view class="picker-content" catch:tap="onStopPropagation">
    
    <!-- 模态框头部 -->
    <view class="picker-header">
      <view class="header-title">{{title}}</view>
      <view class="header-close" bind:tap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>
    
    <!-- 时间选择区域 -->
    <view class="time-selection">

      <!-- 时间选择行 -->
      <view class="time-row">
        <!-- 开始时间 -->
        <view class="time-column">
          <view class="time-header">
            <view class="time-label">开始时间</view>
            <label class="next-day-option {{isStartNextDayDisabled ? 'next-day-disabled' : ''}}" bind:tap="onStartNextDayTap">
              <checkbox value="start-next-day" checked="{{selectedIsStartNextDay}}" disabled="{{isStartNextDayDisabled}}" bind:change="onStartNextDayChange" />
              <text class="next-day-text">次日</text>
            </label>
          </view>
          <view class="time-pickers">
            <picker-view class="time-picker" value="{{[startHourIndex]}}" bind:change="onStartHourChange" immediate-change="true">
              <picker-view-column>
                <view class="picker-item" wx:for="{{hours}}" wx:key="*this">{{item}}</view>
              </picker-view-column>
            </picker-view>

            <view class="time-separator">:</view>

            <picker-view class="time-picker" value="{{[startMinuteIndex]}}" bind:change="onStartMinuteChange" immediate-change="true">
              <picker-view-column>
                <view class="picker-item" wx:for="{{minutes}}" wx:key="*this">{{item}}</view>
              </picker-view-column>
            </picker-view>
          </view>
        </view>

        <!-- 范围分隔符 -->
        <view class="range-separator">
          <text class="range-dash">-</text>
        </view>

        <!-- 结束时间 -->
        <view class="time-column">
          <view class="time-header">
            <view class="time-label">结束时间</view>
            <label class="next-day-option {{isEndNextDayDisabled ? 'next-day-disabled' : ''}}" bind:tap="onEndNextDayTap">
              <checkbox value="end-next-day" checked="{{selectedIsEndNextDay}}" disabled="{{isEndNextDayDisabled}}" bind:change="onEndNextDayChange" />
              <text class="next-day-text">次日</text>
            </label>
          </view>
          <view class="time-pickers">
            <picker-view class="time-picker" value="{{[endHourIndex]}}" bind:change="onEndHourChange" immediate-change="true">
              <picker-view-column>
                <view class="picker-item" wx:for="{{hours}}" wx:key="*this">{{item}}</view>
              </picker-view-column>
            </picker-view>

            <view class="time-separator">:</view>

            <picker-view class="time-picker" value="{{[endMinuteIndex]}}" bind:change="onEndMinuteChange" immediate-change="true">
              <picker-view-column>
                <view class="picker-item" wx:for="{{minutes}}" wx:key="*this">{{item}}</view>
              </picker-view-column>
            </picker-view>
          </view>
        </view>
      </view>
      
      <!-- 时长显示 -->
      <view class="duration-section">
        <view class="duration-label">时长:</view>
        <view class="duration-value">{{duration}}</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="picker-actions">
      <view class="action-btn cancel-btn" bind:tap="onCancel">
        <text>取消</text>
      </view>
      <view class="action-btn confirm-btn" bind:tap="onConfirm">
        <text>确定</text>
      </view>
    </view>
  </view>
</view>
