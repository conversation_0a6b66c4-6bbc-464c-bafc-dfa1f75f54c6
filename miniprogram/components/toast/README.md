# Toast 消息提示组件

一个现代化的 toast 消息提示组件，支持多种消息类型、位置和优雅的动画效果。

## 功能特性

- 🎨 **现代化UI设计**：使用渐变背景、圆角、阴影等现代设计元素
- 🎭 **多种消息类型**：支持 none、success、info、warning、error 五种类型
- 📍 **灵活位置控制**：支持 top、center、bottom、left、right 五个位置
- ✨ **优雅动画效果**：不同位置使用不同的滑入/滑出动画
- ⏰ **自定义显示时长**：默认 2000ms，可设置为 null 不自动关闭
- 🖱️ **点击交互**：支持点击事件，可由父组件处理
- 📱 **响应式适配**：自动适配不同屏幕尺寸

## 使用方法

### 1. 在页面 JSON 中引入组件

```json
{
  "usingComponents": {
    "toast": "/components/toast/index"
  }
}
```

### 2. 在页面 WXML 中使用

```xml
<!-- 基础用法 -->
<toast 
  message="{{toastMessage}}"
  type="{{toastType}}"
  position="{{toastPosition}}"
  visible="{{showToast}}"
  bind:toastHide="onToastHide"
/>

<!-- 可点击的 toast -->
<toast 
  message="点击我试试"
  type="success"
  position="center"
  visible="{{showClickableToast}}"
  clickable="{{true}}"
  bind:toastClick="onToastClick"
  bind:toastHide="onToastHide"
/>
```

### 3. 在页面 JS 中控制

```javascript
Page({
  data: {
    showToast: false,
    toastMessage: '',
    toastType: 'none',
    toastPosition: 'center'
  },

  // 显示不同类型的 toast
  showSuccessToast() {
    this.setData({
      toastMessage: '操作成功！',
      toastType: 'success',
      toastPosition: 'top',
      showToast: true
    });
  },

  showErrorToast() {
    this.setData({
      toastMessage: '操作失败，请重试',
      toastType: 'error',
      toastPosition: 'center',
      showToast: true
    });
  },

  showWarningToast() {
    this.setData({
      toastMessage: '请注意检查输入内容',
      toastType: 'warning',
      toastPosition: 'bottom',
      showToast: true
    });
  },

  // 事件处理
  onToastHide() {
    this.setData({
      showToast: false
    });
  },

  onToastClick(e) {
    console.log('Toast 被点击了:', e.detail);
    // 处理点击事件
  }
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| message | String | '' | 消息内容 |
| type | String | 'none' | 消息类型：none、success、info、warning、error |
| position | String | 'center' | 显示位置：top、center、bottom、left、right |
| duration | Number | 2000 | 显示时长（毫秒），设置为 null 则不自动关闭 |
| visible | Boolean | false | 是否显示 |
| clickable | Boolean | false | 是否可点击 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| toastShow | toast 显示时触发 | {message, type} |
| toastHide | toast 隐藏时触发 | {message, type} |
| toastClick | 点击 toast 时触发（需要设置 clickable 为 true） | {message, type} |

## 样式定制

组件使用了现代化的设计风格，如需定制样式，可以通过以下 CSS 变量或直接修改组件样式文件：

- 不同类型使用了渐变背景色
- 支持毛玻璃效果（backdrop-filter）
- 响应式设计，自动适配移动端

## 最佳实践

1. **选择合适的位置**：
   - `top`：适合成功提示
   - `center`：适合重要信息
   - `bottom`：适合一般提示
   - `left/right`：适合侧边提示

2. **合理设置时长**：
   - 成功提示：1500-2000ms
   - 错误提示：3000-4000ms
   - 重要信息：设置为 null，需要用户手动关闭

3. **避免频繁显示**：
   - 在前一个 toast 消失后再显示新的
   - 可以通过 toastHide 事件来控制

## 演示页面

完整的演示页面位于 `pages/toast-demo/index`，展示了所有功能和效果。

## 示例效果

- **Success Toast**：绿色渐变背景，✓ 图标
- **Error Toast**：红色渐变背景，✕ 图标
- **Warning Toast**：橙色渐变背景，⚠ 图标
- **Info Toast**：蓝色渐变背景，ⓘ 图标
- **None Toast**：灰色背景，无图标
