Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 消息内容
    message: {
      type: String,
      value: ''
    },
    // 消息类型：none, success, info, warning, error
    type: {
      type: String,
      value: 'none'
    },
    // 显示位置：top, center, bottom, left, right
    position: {
      type: String,
      value: 'center'
    },
    // 显示时长（毫秒），null 则不自动关闭
    duration: {
      type: Number,
      value: 2000
    },
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isShowing: false,
    isClosing: false,
    timer: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 显示 toast
     */
    show() {
      if (this.data.isShowing) return;
      
      this.setData({
        isShowing: true,
        isClosing: false
      });

      // 触发显示事件
      this.triggerEvent('toastShow', {
        message: this.properties.message,
        type: this.properties.type
      });

      // 设置自动关闭
      if (this.properties.duration !== null && this.properties.duration > 0) {
        this.data.timer = setTimeout(() => {
          this.hide();
        }, this.properties.duration);
      }
    },

    /**
     * 隐藏 toast
     */
    hide() {
      if (!this.data.isShowing || this.data.isClosing) return;

      // 清除定时器
      if (this.data.timer) {
        clearTimeout(this.data.timer);
        this.data.timer = null;
      }

      // 先设置关闭状态，触发出场动画
      this.setData({
        isClosing: true
      });

      // 等待动画完成后隐藏
      setTimeout(() => {
        this.setData({
          isShowing: false,
          isClosing: false
        });

        // 触发隐藏事件
        this.triggerEvent('toastHide', {
          message: this.properties.message,
          type: this.properties.type
        });
      }, 320); // 稍微增加动画时长，确保动画完成
    },

    /**
     * 点击 toast 处理
     */
    onToastClick() {
      if (!this.properties.clickable) return;

      // 触发点击事件
      this.triggerEvent('toastClick', {
        message: this.properties.message,
        type: this.properties.type
      });
    },


  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    detached() {
      // 组件销毁时清除定时器
      if (this.data.timer) {
        clearTimeout(this.data.timer);
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.show();
      } else {
        this.hide();
      }
    }
  }
});
