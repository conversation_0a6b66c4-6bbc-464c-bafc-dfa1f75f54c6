<wxs module="utils">
var getIcon = function(type) {
  var iconMap = {
    'success': '✓',
    'info': 'ⓘ',
    'warning': '⚠',
    'error': '✕',
    'none': ''
  };
  return iconMap[type] || '';
};

module.exports = {
  getIcon: getIcon
};
</wxs>

<view
  wx:if="{{isShowing}}"
  class="toast-container toast-{{position}} {{isClosing ? 'closing' : ''}}"
  bindtap="onToastClick"
>
  <view class="toast-content toast-{{type}} {{clickable ? 'clickable' : ''}}">
    <view wx:if="{{type !== 'none'}}" class="toast-icon">{{utils.getIcon(type)}}</view>
    <view class="toast-message">{{message}}</view>
  </view>
</view>
