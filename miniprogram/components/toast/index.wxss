/* Toast 组件样式 */

/* 容器基础样式 */
.toast-container {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
  /* 移除 transition，避免与 animation 冲突 */
}

/* 位置样式 */
.toast-top {
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%) translateY(-100rpx);
  animation: slideInFromTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.toast-center {
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%) scale(0.8);
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.toast-bottom {
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%) translateY(100rpx);
  animation: slideInFromBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.toast-left {
  top: 50%;
  left: 40rpx;
  transform: translateY(-50%) translateX(-100rpx);
  animation: slideInFromLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.toast-right {
  top: 50%;
  right: 40rpx;
  transform: translateY(-50%) translateX(100rpx);
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 出场动画 - 使用 !important 确保优先级，并重置 animation-fill-mode */
.toast-container.closing {
  animation-fill-mode: forwards !important;
}

.toast-top.closing {
  animation: slideOutToTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

.toast-center.closing {
  animation: scaleOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

.toast-bottom.closing {
  animation: slideOutToBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

.toast-left.closing {
  animation: slideOutToLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

.toast-right.closing {
  animation: slideOutToRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
}

/* 内容样式 */
.toast-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20rpx);
  max-width: 600rpx;
  min-width: 200rpx;
  transition: all 0.2s ease;
}

.toast-content.clickable {
  cursor: pointer;
}

.toast-content.clickable:active {
  transform: scale(0.98);
}

/* 图标样式 */
.toast-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

/* 消息文本样式 */
.toast-message {
  font-size: 28rpx;
  line-height: 1.4;
  word-break: break-all;
  flex: 1;
}

/* 类型样式 */
.toast-none {
  background: rgba(55, 65, 81, 0.9);
  color: white;
}

.toast-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(5, 150, 105, 0.9) 100%);
  color: white;
}

.toast-success .toast-icon {
  color: rgba(255, 255, 255, 0.9);
}

.toast-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
  color: white;
}

.toast-info .toast-icon {
  color: rgba(255, 255, 255, 0.9);
}

.toast-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.9) 0%, rgba(217, 119, 6, 0.9) 100%);
  color: white;
}

.toast-warning .toast-icon {
  color: rgba(255, 255, 255, 0.9);
}

.toast-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
  color: white;
}

.toast-error .toast-icon {
  color: rgba(255, 255, 255, 0.9);
}

/* 动画关键帧 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-50rpx);
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50rpx);
    opacity: 0;
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(50rpx);
  }
  to {
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes slideOutToBottom {
  from {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(50rpx);
    opacity: 0;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-100rpx);
  }
  to {
    transform: translateY(-50%) translateX(0);
  }
}

@keyframes slideOutToLeft {
  from {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
  to {
    transform: translateY(-50%) translateX(-50rpx);
    opacity: 0;
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100rpx);
  }
  to {
    transform: translateY(-50%) translateX(0);
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
  to {
    transform: translateY(-50%) translateX(50rpx);
    opacity: 0;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-50%) scale(0.8);
  }
  to {
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
}

@keyframes scaleOut {
  from {
    transform: translateX(-50%) translateY(-50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(-50%) translateY(-50%) scale(0.8);
    opacity: 0;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .toast-content {
    max-width: calc(100vw - 80rpx);
    margin: 0 20rpx;
  }
  
  .toast-left {
    left: 20rpx;
  }
  
  .toast-right {
    right: 20rpx;
  }
}
