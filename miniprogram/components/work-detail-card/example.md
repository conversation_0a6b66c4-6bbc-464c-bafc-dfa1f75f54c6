# 工作履历详情卡片组件使用示例

## 组件说明

`work-detail-card` 组件用于展示工作履历的详细信息，包括任职时间、工作时长、薪资详情、发薪日和备注等信息。

## 使用方法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "work-detail-card": "/components/work-detail-card/index"
  }
}
```

### 2. 在页面中使用组件

```xml
<!-- 工作履历详情页面 -->
<view class="work-detail-page">
  <work-detail-card work-data="{{currentWork}}"></work-detail-card>
</view>
```

### 3. 在页面JS中准备数据

```javascript
Page({
  data: {
    currentWork: {
      company: '某科技公司',
      position: '前端开发工程师',
      startDate: '2025-03-06',
      endDate: '2025-08-04', // 可选，不传表示至今
      probationEndDate: '2025-06-06',
      probationSalary: 8000,
      formalSalary: 10000,
      payDays: [
        { day: 10, name: '发薪日' },
        { day: 20, name: '发薪日' }
      ],
      notes: '入职的第一家公司，做互联网IT产品的。'
    }
  },

  onLoad() {
    // 可以从数据管理器获取工作数据
    // const workData = dataManager.getWork(workId);
    // this.setData({ currentWork: workData });
  }
});
```

## 数据格式说明

### workData 对象属性

| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| company | String | 是 | 公司名称 |
| position | String | 是 | 职位名称 |
| startDate | String/Date | 是 | 入职日期 |
| endDate | String/Date | 否 | 离职日期，不传表示至今 |
| probationEndDate | String/Date | 否 | 试用期结束日期 |
| probationSalary | Number | 否 | 试用期薪资 |
| formalSalary | Number | 是 | 正式薪资 |
| payDays | Array | 否 | 发薪日数组 |
| notes | String | 否 | 备注信息 |

### payDays 数组元素格式

```javascript
{
  day: 10,           // 发薪日期（1-31）
  name: '发薪日'      // 发薪日名称
}
```

## 样式特点

- **任职时间**: 灰色背景，简洁展示时间范围
- **工作时长**: 蓝色渐变背景，突出显示工作时长
- **薪资详情**: 绿色渐变背景，清晰对比试用期和正式薪资
- **发薪日**: 紫色主题，标签式展示多个发薪日
- **备注**: 橙色主题，文本内容展示

## 功能特性

1. **自动计算工作时长**: 根据入职和离职日期自动计算工作时长
2. **日期格式化**: 自动将日期格式化为中文显示
3. **薪资格式化**: 自动添加千分位分隔符
4. **条件显示**: 根据数据自动显示或隐藏相应区块
5. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **编辑功能**: 可以添加编辑按钮，支持直接编辑工作信息
2. **分享功能**: 可以添加分享按钮，生成工作履历卡片
3. **导出功能**: 支持导出为图片或PDF
4. **主题切换**: 支持多种主题色彩方案
