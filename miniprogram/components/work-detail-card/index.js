// 工作履历详情卡片组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 工作数据
    workData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          this.processWorkData(newVal);
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    processedData: {}
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理工作数据，计算工作时长等
     */
    processWorkData(workData) {
      const processedData = { ...workData };
      
      // 格式化日期
      if (processedData.startDate) {
        processedData.startDate = this.formatDate(processedData.startDate);
      }
      
      if (processedData.endDate) {
        processedData.endDate = this.formatDate(processedData.endDate);
      }
      
      if (processedData.probationEndDate) {
        processedData.probationEndDate = this.formatDate(processedData.probationEndDate);
      }
      
      // 计算工作时长
      processedData.duration = this.calculateWorkDuration(
        processedData.startDate, 
        processedData.endDate
      );
      
      // 格式化薪资
      if (processedData.probationSalary) {
        processedData.probationSalary = this.formatSalary(processedData.probationSalary);
      }
      
      if (processedData.formalSalary) {
        processedData.formalSalary = this.formatSalary(processedData.formalSalary);
      }
      
      this.setData({
        processedData: processedData
      });
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      if (!date) return '';
      
      const dateObj = new Date(date);
      const year = dateObj.getFullYear();
      const month = dateObj.getMonth() + 1;
      const day = dateObj.getDate();
      
      return `${year}年${month}月${day}日`;
    },

    /**
     * 计算工作时长
     */
    calculateWorkDuration(startDate, endDate) {
      if (!startDate) return '';
      
      const start = new Date(startDate);
      const end = endDate ? new Date(endDate) : new Date();
      
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // 计算年月日
      const years = Math.floor(diffDays / 365);
      const months = Math.floor((diffDays % 365) / 30);
      const days = diffDays % 30;
      
      let duration = '';
      if (years > 0) {
        duration += `${years}年`;
      }
      if (months > 0) {
        duration += `${months}个月`;
      }
      if (days > 0 || duration === '') {
        duration += `${days}天`;
      }
      
      return duration;
    },

    /**
     * 格式化薪资
     */
    formatSalary(salary) {
      if (!salary) return '0';
      
      // 添加千分位分隔符
      return salary.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }
});
