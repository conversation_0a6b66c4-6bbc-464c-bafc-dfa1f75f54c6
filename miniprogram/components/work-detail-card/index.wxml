<!--工作履历详情卡片组件-->
<view class="work-detail-card">
  <!-- 任职时间 -->
  <view class="detail-section employment-period">
    <view class="section-header">
      <text class="icon">📅</text>
      <text class="title">任职时间</text>
    </view>
    <view class="period-content">
      <text class="period-text">{{workData.startDate}} 至 {{workData.endDate || '至今'}}</text>
    </view>
  </view>

  <!-- 工作时长 -->
  <view class="detail-section work-duration">
    <view class="section-header">
      <text class="icon">⏰</text>
      <text class="title">工作时长</text>
    </view>
    <view class="duration-content">
      <text class="duration-text">{{workData.duration}}</text>
    </view>
  </view>

  <!-- 薪资详情 -->
  <view class="detail-section salary-details">
    <view class="section-header">
      <text class="icon">💰</text>
      <text class="title">薪资详情</text>
    </view>
    <view class="salary-content">
      <!-- 试用期薪资 -->
      <view class="salary-item probation" wx:if="{{workData.probationSalary}}">
        <view class="salary-label">
          <text class="label-text">试用期</text>
          <text class="salary-date">{{workData.startDate}}</text>
        </view>
        <view class="salary-amount">¥{{workData.probationSalary}}</view>
      </view>
      
      <!-- 正式薪资 -->
      <view class="salary-item formal">
        <view class="salary-label">
          <text class="label-text">正式</text>
          <text class="salary-date">{{workData.probationEndDate || workData.startDate}} 起</text>
        </view>
        <view class="salary-amount">¥{{workData.formalSalary}}</view>
      </view>
    </view>
  </view>

  <!-- 发薪日 -->
  <view class="detail-section payday-section" wx:if="{{workData.payDays && workData.payDays.length > 0}}">
    <view class="section-header">
      <text class="icon">💜</text>
      <text class="title">发薪日</text>
    </view>
    <view class="payday-content">
      <view class="payday-item" wx:for="{{workData.payDays}}" wx:key="day">
        <text class="payday-text">{{item.day}}日 {{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 备注 -->
  <view class="detail-section notes-section" wx:if="{{workData.notes}}">
    <view class="section-header">
      <text class="icon">📝</text>
      <text class="title">备注</text>
    </view>
    <view class="notes-content">
      <text class="notes-text">{{workData.notes}}</text>
    </view>
  </view>
</view>
