/* 工作履历详情卡片样式 */
.work-detail-card {
  padding: 0;
  background: transparent;
}

/* 通用区块样式 */
.detail-section {
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 区块标题样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-header .icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-header .title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 任职时间样式 */
.employment-period {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
}

.period-content {
  padding-left: 44rpx;
}

.period-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 工作时长样式 */
.work-duration {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1rpx solid #90caf9;
}

.duration-content {
  padding-left: 44rpx;
}

.duration-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1976d2;
}

/* 薪资详情样式 */
.salary-details {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border: 1rpx solid #a5d6a7;
}

.salary-content {
  padding-left: 44rpx;
}

.salary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(76, 175, 80, 0.2);
}

.salary-item:last-child {
  border-bottom: none;
}

.salary-label {
  display: flex;
  flex-direction: column;
}

.label-text {
  font-size: 26rpx;
  color: #2e7d32;
  font-weight: 500;
}

.salary-date {
  font-size: 22rpx;
  color: #66bb6a;
  margin-top: 4rpx;
}

.salary-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #1b5e20;
}

/* 发薪日样式 */
.payday-section {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  border: 1rpx solid #ce93d8;
}

.payday-content {
  padding-left: 44rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.payday-item {
  background: rgba(156, 39, 176, 0.1);
  border: 1rpx solid #ba68c8;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

.payday-text {
  font-size: 24rpx;
  color: #7b1fa2;
  font-weight: 500;
}

/* 备注样式 */
.notes-section {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 1rpx solid #ffcc02;
}

.notes-content {
  padding-left: 44rpx;
}

.notes-text {
  font-size: 26rpx;
  color: #e65100;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .detail-section {
    padding: 20rpx;
    margin-bottom: 12rpx;
  }
  
  .section-header .icon {
    font-size: 28rpx;
  }
  
  .section-header .title {
    font-size: 26rpx;
  }
  
  .salary-amount {
    font-size: 28rpx;
  }
  
  .duration-text {
    font-size: 28rpx;
  }
}
