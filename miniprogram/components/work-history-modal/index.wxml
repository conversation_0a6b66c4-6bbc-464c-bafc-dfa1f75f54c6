<!-- 添加/编辑工作履历模态框组件 -->
<view wx:if="{{visible}}" class="modal-overlay {{modalVisible ? 'show' : ''}}" bindtap="hideModal">
  <view class="modal-content" catchtap="preventModalClose">
    <!-- 模态框头部 -->
    <view class="modal-header">
      <view class="modal-title-section">
        <text class="modal-title">{{editingWork ? '编辑工作履历' : '添加工作履历'}}</text>
      </view>
      <view class="modal-close" bindtap="hideModal">
        <text class="close-icon">✕</text>
      </view>
    </view>
    
    <!-- 模态框主体 -->
    <scroll-view class="modal-body" scroll-y="true">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-icon">🏢</text>
          <text class="section-title">基本信息</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">工作单位 <text class="required">*</text></text>
          <input 
            class="form-input {{errors.company ? 'error' : ''}}"
            placeholder="请输入工作单位"
            value="{{formData.company}}"
            data-field="company"
            bindinput="onInputChange"
          />
          <text wx:if="{{errors.company}}" class="error-text">{{errors.company}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">工作职位 <text class="required">*</text></text>
          <input 
            class="form-input {{errors.position ? 'error' : ''}}"
            placeholder="请输入工作职位"
            value="{{formData.position}}"
            data-field="position"
            bindinput="onInputChange"
          />
          <text wx:if="{{errors.position}}" class="error-text">{{errors.position}}</text>
        </view>
      </view>
      
      <!-- 时间信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-icon">📅</text>
          <text class="section-title">时间信息</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">入职时间 <text class="required">*</text></text>
          <picker 
            class="form-picker {{errors.startDate ? 'error' : ''}}"
            mode="date"
            value="{{formData.startDate}}"
            data-field="startDate"
            bindchange="onDateChange"
          >
            <view class="picker-display">
              <text class="picker-text {{!formData.startDate ? 'placeholder' : ''}}">{{formData.startDate || '请选择入职时间'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <text wx:if="{{errors.startDate}}" class="error-text">{{errors.startDate}}</text>
        </view>
        
        <view class="form-item">
          <view class="form-label-row">
            <text class="form-label">转正日期</text>
            <view wx:if="{{formData.probationEndDate}}" class="clear-date-btn" bindtap="clearDate" data-field="probationEndDate">
              <text class="clear-icon">✕</text>
              <text class="clear-text">清除</text>
            </view>
          </view>
          <picker
            class="form-picker {{errors.probationEndDate ? 'error' : ''}}"
            mode="date"
            value="{{formData.probationEndDate}}"
            data-field="probationEndDate"
            bindchange="onDateChange"
          >
            <view class="picker-display">
              <text class="picker-text {{!formData.probationEndDate ? 'placeholder' : ''}}">{{formData.probationEndDate || '请选择转正日期'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <text wx:if="{{errors.probationEndDate}}" class="error-text">{{errors.probationEndDate}}</text>
        </view>
        
        <view class="form-item">
          <view class="form-label-row">
            <text class="form-label">离职时间</text>
            <view wx:if="{{formData.endDate}}" class="clear-date-btn" bindtap="clearDate" data-field="endDate">
              <text class="clear-icon">✕</text>
              <text class="clear-text">清除</text>
            </view>
          </view>
          <picker
            class="form-picker {{errors.endDate ? 'error' : ''}}"
            mode="date"
            value="{{formData.endDate}}"
            data-field="endDate"
            bindchange="onDateChange"
          >
            <view class="picker-display">
              <text class="picker-text {{!formData.endDate ? 'placeholder' : ''}}">{{formData.endDate || '请选择离职时间'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <text wx:if="{{errors.endDate}}" class="error-text">{{errors.endDate}}</text>
        </view>
      </view>
      
      <!-- 工资信息 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-icon">💰</text>
          <text class="section-title">工资信息</text>
        </view>
        <text class="section-subtitle">仅用于记录，不参与收入计算</text>
        
        <view class="form-item">
          <text class="form-label">试用工资</text>
          <input
            class="form-input {{errors.probationSalary ? 'error' : ''}}"
            placeholder="请输入试用期工资"
            value="{{formData.probationSalary}}"
            type="digit"
            data-field="probationSalary"
            bindinput="onSalaryInputChange"
          />
          <text wx:if="{{errors.probationSalary}}" class="error-text">{{errors.probationSalary}}</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">正式工资</text>
          <input
            class="form-input {{errors.formalSalary ? 'error' : ''}}"
            placeholder="请输入正式工资"
            value="{{formData.formalSalary}}"
            type="digit"
            data-field="formalSalary"
            bindinput="onSalaryInputChange"
          />
          <text wx:if="{{errors.formalSalary}}" class="error-text">{{errors.formalSalary}}</text>
        </view>
      </view>

      <!-- 发薪日设置 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-icon">📅</text>
          <text class="section-title">发薪日设置</text>
        </view>
        <text class="section-subtitle">设置每月发薪日期，用于倒计天数</text>

        <!-- 发薪日列表 -->
        <view wx:for="{{formData.payDays}}" wx:key="index" class="payday-item">
          <view class="payday-header">
            <view class="payday-info">
              <input
                class="payday-name-input"
                placeholder="发薪日名称"
                value="{{item.name}}"
                data-index="{{index}}"
                data-field="name"
                bindinput="onPayDayChange"
              />
            </view>
            <view class="payday-actions">
              <text class="remove-btn" bindtap="removePayDay" data-index="{{index}}">✕</text>
            </view>
          </view>

          <view class="payday-day-setting">
            <text class="payday-label">每月</text>
            <picker
              class="payday-day-picker"
              mode="selector"
              range="{{['1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31']}}"
              value="{{item.day - 1}}"
              data-index="{{index}}"
              data-field="day"
              bindchange="onPayDayChange"
            >
              <view class="picker-display">
                <text class="picker-text">{{item.day}}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
            <text class="payday-label">号发薪</text>
          </view>
        </view>

        <!-- 添加发薪日按钮 -->
        <view class="add-payday-btn" bindtap="addPayDay">
          <text class="add-icon">+</text>
          <text class="add-text">添加发薪日</text>
        </view>

        <!-- 发薪日验证错误 -->
        <text wx:if="{{errors.payDays}}" class="error-text">{{errors.payDays}}</text>
      </view>

      <!-- 备注 -->
      <view class="form-section">
        <view class="section-header">
          <text class="section-icon">📝</text>
          <text class="section-title">备注</text>
        </view>

        <view class="form-item">
          <textarea
            class="form-textarea"
            placeholder="请输入备注信息"
            value="{{formData.notes}}"
            data-field="notes"
            bindinput="onInputChange"
            maxlength="200"
          />
          <view class="textarea-counter">
            <text class="counter-text">{{formData.notes ? formData.notes.length : 0}}/200</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 模态框底部 -->
    <view class="modal-footer">
      <button class="footer-btn cancel-btn" bindtap="hideModal">
        <text class="btn-text">取消</text>
      </button>
      <button class="footer-btn save-btn" bindtap="saveWorkHistory">
        <text class="btn-text">保存</text>
      </button>
    </view>
  </view>
</view>
