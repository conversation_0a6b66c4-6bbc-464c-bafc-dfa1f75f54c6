/**
 * 基础API调用器
 * 提供最简单的云函数调用封装
 */

const { getVersion } = require('../config/version.js')

class BaseApiClient {
  constructor() {
    this.cloudFunctionName = 'cloud-functions'
  }

  /**
   * 基础云函数调用方法
   * @param {string} type - API类型
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} 响应结果
   */
  async callCloudFunction(type, data = {}) {
    try {
      console.log(`[API] 调用 ${type}:`, data)

      const result = await wx.cloud.callFunction({
        name: this.cloudFunctionName,
        data: {
          type,
          data,
          version: getVersion(),
        }
      })

      console.log(`[API] ${type} 响应:`, result.result)
      
      if (!result.result) {
        throw new Error('云函数返回数据格式错误')
      }

      return result.result
    } catch (error) {
      console.error(`[API] ${type} 调用失败:`, error)
      
      // 统一错误处理
      if (error.errCode) {
        // 微信云函数错误
        throw new Error(`云函数调用失败: ${error.errMsg || error.message}`)
      } else {
        // 其他错误
        throw new Error(error.message || '网络请求失败')
      }
    }
  }

  /**
   * 带加载提示的调用方法
   * @param {string} type - API类型
   * @param {Object} data - 请求数据
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 响应结果
   */
  async callWithLoading(type, data = {}, options = {}) {
    const { 
      loadingText = '加载中...',
      showLoading = true,
      showError = true 
    } = options

    try {
      if (showLoading) {
        wx.showLoading({ title: loadingText, mask: true })
      }

      const result = await this.callCloudFunction(type, data)

      if (showLoading) {
        wx.hideLoading()
      }

      return result
    } catch (error) {
      if (showLoading) {
        wx.hideLoading()
      }

      if (showError) {
        wx.showToast({
          title: error.message,
          icon: 'none',
          duration: 2000
        })
      }

      throw error
    }
  }
}

// 创建单例实例
const baseApiClient = new BaseApiClient()

export default baseApiClient
