/**
 * 增强API调用器
 * 提供缓存、重试、加载状态管理等高级功能
 */

import baseApiClient from './base.js'
import cacheManager from './utils/cache.js'
import { retryManager } from './utils/retry.js'

class EnhancedApiClient {
  constructor() {
    this.baseClient = baseApiClient
    this.cache = cacheManager
    this.retry = retryManager
    this.loadingStates = new Map()
    
    // 默认配置
    this.defaultOptions = {
      cache: false,
      cacheTTL: 5 * 60 * 1000,      // 默认缓存5分钟
      cacheIgnoreKeys: [],           // 缓存时忽略的参数键
      retry: false,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      loading: false,
      loadingKey: null,              // 加载状态的唯一标识
      showLoading: false,
      showError: true
    }
  }

  /**
   * 增强的API调用方法
   * @param {string} type - API类型
   * @param {Object} data - 请求数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 响应结果
   */
  async callWithOptions(type, data = {}, options = {}) {
    const finalOptions = { ...this.defaultOptions, ...options }
    const loadingKey = finalOptions.loadingKey || `${type}_${Date.now()}`

    try {
      // 1. 检查缓存
      if (finalOptions.cache) {
        const cacheKey = this.cache.generateCacheKey(
          type, 
          data, 
          finalOptions.cacheIgnoreKeys
        )
        const cachedResult = this.cache.get(cacheKey)
        
        if (cachedResult) {
          console.log(`[EnhancedAPI] 使用缓存数据: ${type}`)
          return cachedResult
        }
      }

      // 2. 设置加载状态
      if (finalOptions.loading) {
        this.setLoading(loadingKey, true)
      }

      if (finalOptions.showLoading) {
        wx.showLoading({ 
          title: finalOptions.loadingText || '加载中...', 
          mask: true 
        })
      }

      // 3. 执行请求（带重试）
      let result
      if (finalOptions.retry) {
        result = await this.retry.execute(
          () => this.baseClient.callCloudFunction(type, data),
          finalOptions.retryConfig
        )
      } else {
        result = await this.baseClient.callCloudFunction(type, data)
      }

      // 4. 设置缓存
      if (finalOptions.cache && result.success) {
        const cacheKey = this.cache.generateCacheKey(
          type, 
          data, 
          finalOptions.cacheIgnoreKeys
        )
        this.cache.set(cacheKey, result, finalOptions.cacheTTL)
      }

      return result

    } catch (error) {
      if (finalOptions.showError) {
        wx.showToast({
          title: error.message,
          icon: 'none',
          duration: 2000
        })
      }
      throw error

    } finally {
      // 5. 清理加载状态
      if (finalOptions.loading) {
        this.setLoading(loadingKey, false)
      }

      if (finalOptions.showLoading) {
        wx.hideLoading()
      }
    }
  }

  /**
   * 设置加载状态
   * @param {string} key - 加载状态键
   * @param {boolean} loading - 是否加载中
   */
  setLoading(key, loading) {
    if (loading) {
      this.loadingStates.set(key, true)
    } else {
      this.loadingStates.delete(key)
    }
    
    console.log(`[EnhancedAPI] 加载状态 ${key}: ${loading}`)
  }

  /**
   * 获取加载状态
   * @param {string} key - 加载状态键
   * @returns {boolean} 是否加载中
   */
  isLoading(key) {
    return this.loadingStates.has(key)
  }

  /**
   * 获取所有加载状态
   * @returns {Object} 所有加载状态
   */
  getAllLoadingStates() {
    const states = {}
    for (const [key, value] of this.loadingStates.entries()) {
      states[key] = value
    }
    return states
  }

  /**
   * 清除指定API的缓存
   * @param {string} type - API类型
   * @param {Object} data - 请求数据（可选）
   */
  clearCache(type, data = null) {
    if (data === null) {
      // 清除该API类型的所有缓存
      const allCacheInfo = this.cache.getAllCacheInfo()
      let deletedCount = 0
      
      allCacheInfo.forEach(info => {
        if (info.key.startsWith(`${type}_`)) {
          this.cache.delete(info.key)
          deletedCount++
        }
      })
      
      console.log(`[EnhancedAPI] 清除 ${type} 的所有缓存，共 ${deletedCount} 项`)
      return deletedCount
    } else {
      // 清除特定参数的缓存
      const cacheKey = this.cache.generateCacheKey(type, data)
      const deleted = this.cache.delete(cacheKey)
      console.log(`[EnhancedAPI] 清除缓存 ${cacheKey}: ${deleted}`)
      return deleted ? 1 : 0
    }
  }

  /**
   * 预热缓存
   * @param {string} type - API类型
   * @param {Object} data - 请求数据
   * @param {Object} options - 缓存选项
   */
  async warmupCache(type, data = {}, options = {}) {
    const cacheOptions = {
      cache: true,
      cacheTTL: options.cacheTTL || this.defaultOptions.cacheTTL,
      cacheIgnoreKeys: options.cacheIgnoreKeys || [],
      loading: false,
      showLoading: false,
      showError: false
    }

    try {
      await this.callWithOptions(type, data, cacheOptions)
      console.log(`[EnhancedAPI] 缓存预热成功: ${type}`)
    } catch (error) {
      console.warn(`[EnhancedAPI] 缓存预热失败: ${type}`, error.message)
    }
  }

  /**
   * 批量预热缓存
   * @param {Array} requests - 请求列表 [{type, data, options}]
   */
  async batchWarmupCache(requests) {
    const promises = requests.map(req => 
      this.warmupCache(req.type, req.data, req.options)
    )
    
    try {
      await Promise.allSettled(promises)
      console.log(`[EnhancedAPI] 批量缓存预热完成，共 ${requests.length} 个请求`)
    } catch (error) {
      console.warn('[EnhancedAPI] 批量缓存预热部分失败', error)
    }
  }

  /**
   * 获取API统计信息
   */
  getStats() {
    return {
      cache: this.cache.getStats(),
      retry: this.retry.getStats(),
      loading: {
        activeCount: this.loadingStates.size,
        activeKeys: Array.from(this.loadingStates.keys())
      }
    }
  }

  /**
   * 重置所有统计信息
   */
  resetStats() {
    this.retry.resetStats()
    // 缓存统计信息在缓存管理器中，这里不重置以保持缓存数据
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.loadingStates.clear()
    this.cache.cleanup() // 清理过期缓存
  }
}

// 创建增强API客户端实例
const enhancedApiClient = new EnhancedApiClient()

export default enhancedApiClient
