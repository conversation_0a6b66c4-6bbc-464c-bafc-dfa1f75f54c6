/**
 * 迁移指南
 * 展示如何从现有代码迁移到新的API系统
 */

/**
 * 迁移示例1：基础云函数调用
 */
export const basicMigration = {
  
  // 旧代码（在页面中）
  oldCode: `
    // 每个页面都有类似的代码
    async callCloudFunction(action, data = {}) {
      try {
        const result = await wx.cloud.callFunction({
          name: 'cloud-functions',
          data: {
            type: action,
            data: {
              ...data
            }
          }
        })
        return result.result
      } catch (error) {
        console.error('云函数调用失败:', error)
        throw new Error('网络请求失败')
      }
    }

    // 使用方式
    const result = await this.callCloudFunction('getFriendApps')
  `,

  // 新代码
  newCode: `
    // 导入API
    import { api } from '../../core/api/index.js'

    // 使用方式1：基础调用
    const result = await api.call('getFriendApps')

    // 使用方式2：模块化调用
    const result = await api.friendApps.getFriendApps()

    // 使用方式3：带缓存的调用
    const result = await api.friendApps.getFriendAppsWithCache()
  `
}

/**
 * 迁移示例2：友情应用页面
 */
export const friendAppsPageMigration = {
  
  // 旧代码
  oldCode: `
    // pages/friend-apps/index.js
    Page({
      data: {
        apps: [],
        loading: true,
        error: false
      },

      async loadFriendApps() {
        try {
          this.setData({
            loading: true,
            error: false
          })

          const result = await this.callCloudFunction('getFriendApps')
          
          if (result.success) {
            this.setData({
              apps: result.data.apps || [],
              loading: false
            })
          } else {
            throw new Error(result.message || '获取友情应用失败')
          }
        } catch (error) {
          console.error('加载友情应用失败:', error)
          this.setData({
            loading: false,
            error: true,
            apps: []
          })
        }
      },

      async callCloudFunction(action, data = {}) {
        // ... 重复的代码
      }
    })
  `,

  // 新代码
  newCode: `
    // pages/friend-apps/index.js
    import { api } from '../../core/api/index.js'

    Page({
      data: {
        apps: [],
        loading: true,
        error: false
      },

      async loadFriendApps() {
        try {
          this.setData({
            loading: true,
            error: false
          })

          // 使用新的API，自动处理缓存和错误
          const result = await api.friendApps.getFriendAppsWithCache({}, {
            showLoading: false  // 使用自定义loading
          })
          
          if (result.success) {
            this.setData({
              apps: result.data.apps || [],
              loading: false
            })
          } else {
            throw new Error(result.message || '获取友情应用失败')
          }
        } catch (error) {
          console.error('加载友情应用失败:', error)
          this.setData({
            loading: false,
            error: true,
            apps: []
          })
        }
      },

      // 按分类加载
      async loadAppsByCategory(category) {
        const result = await api.friendApps.getFriendAppsByCategory(category)
        // 处理结果...
      },

      // 搜索应用
      async searchApps(keyword) {
        const result = await api.friendApps.searchFriendApps(keyword)
        // 处理结果...
      },

      // 清除缓存并刷新
      async onRefresh() {
        api.friendApps.clearFriendAppsCache('all')
        await this.loadFriendApps()
      }
    })
  `
}

/**
 * 迁移示例3：签到页面
 */
export const checkInPageMigration = {
  
  // 旧代码
  oldCode: `
    // pages/check-in/index.js
    Page({
      async loadCheckInStatus() {
        try {
          this.setData({
            'loading.status': true
          })

          const result = await wx.cloud.callFunction({
            name: 'cloud-functions',
            data: {
              type: 'getCheckInStatus',
              data: {}
            }
          })

          if (result.result.success) {
            this.setData({
              checkInStatus: result.result.data
            })
          } else {
            wx.showToast({
              title: result.result.message || '获取签到状态失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('加载签到状态失败:', error)
          wx.showToast({
            title: '网络异常，请重试',
            icon: 'none'
          })
        } finally {
          this.setData({
            'loading.status': false
          })
        }
      },

      async onCheckIn() {
        try {
          this.setData({
            'loading.checkIn': true
          })

          const result = await wx.cloud.callFunction({
            name: 'cloud-functions',
            data: {
              type: 'checkIn',
              data: {}
            }
          })

          if (result.result.success) {
            // 刷新数据
            this.loadCheckInStatus()
            // ... 其他处理
          }
        } catch (error) {
          // 错误处理
        } finally {
          this.setData({
            'loading.checkIn': false
          })
        }
      }
    })
  `,

  // 新代码
  newCode: `
    // pages/check-in/index.js
    import { api } from '../../core/api/index.js'

    Page({
      async loadCheckInStatus() {
        try {
          this.setData({
            'loading.status': true
          })

          // 使用新API，自动处理缓存和错误
          const result = await api.checkIn.getCheckInStatus({
            showLoading: false,  // 使用自定义loading
            showError: true      // 自动显示错误
          })

          if (result.success) {
            this.setData({
              checkInStatus: result.data
            })
          }
        } catch (error) {
          // 错误已经自动处理
          console.error('加载签到状态失败:', error)
        } finally {
          this.setData({
            'loading.status': false
          })
        }
      },

      async onCheckIn() {
        try {
          this.setData({
            'loading.checkIn': true
          })

          // 使用新API，自动处理重试和缓存清理
          const result = await api.checkIn.checkIn({
            showLoading: false,  // 使用自定义loading
            showError: true      // 自动显示错误
          })

          if (result.success) {
            // 缓存已经自动清理，直接重新加载
            await this.loadCheckInStatus()
            // ... 其他处理
          }
        } catch (error) {
          // 错误已经自动处理
          console.error('签到失败:', error)
        } finally {
          this.setData({
            'loading.checkIn': false
          })
        }
      }
    })
  `
}

/**
 * 迁移示例4：积分页面
 */
export const pointsPageMigration = {
  
  // 旧代码
  oldCode: `
    // pages/points/index.js
    Page({
      async loadPointsRecords() {
        try {
          this.setData({
            'loading.records': true
          })

          const result = await wx.cloud.callFunction({
            name: 'cloud-functions',
            data: {
              type: 'getPointsRecords',
              data: {
                type: this.data.filter.type || undefined,
                limit: this.data.pagination.limit,
                skip: this.data.pagination.skip
              }
            }
          })

          if (result.result.success) {
            const data = result.result.data
            const newRecords = this.data.pagination.skip === 0 
              ? data.records 
              : [...this.data.records, ...data.records]

            this.setData({
              records: newRecords,
              'pagination.hasMore': data.records.length === this.data.pagination.limit
            })
          }
        } catch (error) {
          // 错误处理
        } finally {
          this.setData({
            'loading.records': false
          })
        }
      }
    })
  `,

  // 新代码
  newCode: `
    // pages/points/index.js
    import { api } from '../../core/api/index.js'

    Page({
      async loadPointsRecords() {
        try {
          this.setData({
            'loading.records': true
          })

          // 使用新的分页API，自动处理缓存
          const result = await api.points.getPointsRecordsByPage(
            this.data.pagination.page,
            this.data.pagination.limit,
            { type: this.data.filter.type },
            { showLoading: false }
          )

          if (result.success) {
            const data = result.data
            const newRecords = this.data.pagination.page === 1 
              ? data.records 
              : [...this.data.records, ...data.records]

            this.setData({
              records: newRecords,
              'pagination.hasMore': data.records.length === this.data.pagination.limit
            })
          }
        } catch (error) {
          console.error('加载积分记录失败:', error)
        } finally {
          this.setData({
            'loading.records': false
          })
        }
      },

      // 按类型筛选（每种类型独立缓存）
      async filterByType(type) {
        this.setData({
          'filter.type': type,
          'pagination.page': 1,
          records: []
        })

        const result = await api.points.getPointsRecordsByType(type)
        // 处理结果...
      },

      // 积分变动后清除缓存
      onPointsChanged() {
        api.points.onPointsChanged()
      }
    })
  `
}

/**
 * 迁移步骤指南
 */
export const migrationSteps = {
  
  step1: {
    title: '第一步：导入新的API模块',
    description: '在需要调用云函数的页面中导入API模块',
    code: `
      // 在页面顶部添加
      import { api } from '../../core/api/index.js'
      
      // 或者导入特定模块
      import { friendAppsApi, userApi } from '../../core/api/index.js'
    `
  },

  step2: {
    title: '第二步：替换云函数调用',
    description: '将原有的 wx.cloud.callFunction 调用替换为新的API调用',
    before: `
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: { type: 'getFriendApps', data: {} }
      })
    `,
    after: `
      const result = await api.call('getFriendApps')
      // 或者
      const result = await api.friendApps.getFriendApps()
    `
  },

  step3: {
    title: '第三步：利用缓存功能',
    description: '为合适的API调用添加缓存，提升用户体验',
    code: `
      // 添加缓存
      const result = await api.friendApps.getFriendAppsWithCache()
      
      // 自定义缓存配置
      const result = await api.callWithOptions('getStoreItems', {}, {
        cache: true,
        cacheTTL: 10 * 60 * 1000  // 缓存10分钟
      })
    `
  },

  step4: {
    title: '第四步：删除重复代码',
    description: '删除页面中重复的 callCloudFunction 方法',
    code: `
      // 删除这些重复的方法
      // async callCloudFunction(action, data = {}) { ... }
      
      // 统一使用新的API系统
    `
  },

  step5: {
    title: '第五步：优化错误处理',
    description: '利用新系统的统一错误处理机制',
    code: `
      // 简化的错误处理
      try {
        const result = await api.user.getUserInfo({
          showError: true  // 自动显示错误提示
        })
      } catch (error) {
        // 只需要处理业务逻辑相关的错误
      }
    `
  },

  step6: {
    title: '第六步：添加高级功能',
    description: '根据需要添加重试、加载状态管理等高级功能',
    code: `
      // 添加重试机制
      const result = await api.callWithOptions('unstableApi', {}, {
        retry: true,
        retryConfig: { maxRetries: 3 }
      })
      
      // 加载状态管理
      const result = await api.callWithOptions('longRunningApi', {}, {
        loading: true,
        loadingKey: 'myOperation'
      })
    `
  }
}

/**
 * 迁移检查清单
 */
export const migrationChecklist = [
  '✅ 已导入新的API模块',
  '✅ 已替换所有 wx.cloud.callFunction 调用',
  '✅ 已删除重复的 callCloudFunction 方法',
  '✅ 已为合适的API添加缓存',
  '✅ 已优化错误处理逻辑',
  '✅ 已测试所有功能正常工作',
  '✅ 已添加必要的高级功能（重试、加载状态等）',
  '✅ 已更新相关文档和注释'
]

export default {
  basicMigration,
  friendAppsPageMigration,
  checkInPageMigration,
  pointsPageMigration,
  migrationSteps,
  migrationChecklist
}
