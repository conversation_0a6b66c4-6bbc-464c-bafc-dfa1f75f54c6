/**
 * API使用示例
 * 展示如何使用新的统一API调用系统
 */

import { api, apiManager } from '../index.js'

/**
 * 示例1：基础使用方式
 */
export const basicUsageExamples = {
  
  // 最简单的调用方式
  async simpleCall() {
    try {
      const result = await api.call('getUserInfo')
      console.log('用户信息:', result)
      return result
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },

  // 带加载提示的调用
  async callWithLoading() {
    try {
      const result = await api.callWithLoading('getCheckInStatus', {}, {
        loadingText: '获取签到状态中...',
        showLoading: true,
        showError: true
      })
      console.log('签到状态:', result)
      return result
    } catch (error) {
      // 错误已经自动显示，这里可以做其他处理
      console.error('获取签到状态失败:', error)
      throw error
    }
  }
}

/**
 * 示例2：模块化API使用
 */
export const moduleApiExamples = {
  
  // 使用友情应用API
  async friendAppsExample() {
    try {
      // 获取热门应用（带缓存）
      const popularApps = await api.friendApps.getPopularFriendApps()
      console.log('热门应用:', popularApps)

      // 按分类获取应用（每个分类独立缓存）
      const gameApps = await api.friendApps.getFriendAppsByCategory('game')
      const toolApps = await api.friendApps.getFriendAppsByCategory('tool')
      console.log('游戏应用:', gameApps)
      console.log('工具应用:', toolApps)

      // 搜索应用（搜索结果独立缓存）
      const searchResults = await api.friendApps.searchFriendApps('小程序')
      console.log('搜索结果:', searchResults)

      return { popularApps, gameApps, toolApps, searchResults }
    } catch (error) {
      console.error('友情应用API示例失败:', error)
      throw error
    }
  },

  // 使用用户API
  async userApiExample() {
    try {
      // 获取用户信息（带缓存）
      const userInfo = await api.user.getUserInfo()
      console.log('用户信息:', userInfo)

      // 获取VIP记录
      const vipRecords = await api.user.getVipRecords()
      console.log('VIP记录:', vipRecords)

      // 更新用户信息（会自动清除相关缓存）
      const updateResult = await api.user.updateUserInfo({
        nickname: '新昵称'
      })
      console.log('更新结果:', updateResult)

      return { userInfo, vipRecords, updateResult }
    } catch (error) {
      console.error('用户API示例失败:', error)
      throw error
    }
  },

  // 使用签到API
  async checkInApiExample() {
    try {
      // 获取签到状态
      const status = await api.checkIn.getCheckInStatus()
      console.log('签到状态:', status)

      // 如果今天还没签到，执行签到
      if (!status.data.hasCheckedInToday) {
        const checkInResult = await api.checkIn.checkIn()
        console.log('签到结果:', checkInResult)
      }

      // 获取签到日历
      const calendar = await api.checkIn.getCheckInCalendar('2025-01')
      console.log('签到日历:', calendar)

      return { status, calendar }
    } catch (error) {
      console.error('签到API示例失败:', error)
      throw error
    }
  }
}

/**
 * 示例3：高级功能使用
 */
export const advancedUsageExamples = {
  
  // 使用增强功能
  async enhancedFeaturesExample() {
    try {
      // 带重试的调用
      const result1 = await api.callWithOptions('getStoreItems', {}, {
        cache: true,
        cacheTTL: 10 * 60 * 1000,  // 缓存10分钟
        retry: true,
        retryConfig: {
          maxRetries: 3,
          baseDelay: 1000
        },
        loading: true,
        showLoading: true
      })
      console.log('商店商品:', result1)

      // 自定义缓存策略
      const result2 = await api.callWithOptions('getPointsRecords', {
        type: 'checkin',
        page: 1
      }, {
        cache: true,
        cacheTTL: 2 * 60 * 1000,   // 缓存2分钟
        cacheIgnoreKeys: ['timestamp'], // 忽略时间戳参数
        loading: true,
        loadingKey: 'getPointsRecords_checkin'
      })
      console.log('签到积分记录:', result2)

      return { result1, result2 }
    } catch (error) {
      console.error('高级功能示例失败:', error)
      throw error
    }
  },

  // 缓存管理示例
  async cacheManagementExample() {
    // 预热缓存
    await api.warmup()

    // 清除特定缓存
    api.friendApps.clearFriendAppsCache('category', 'game')
    api.user.clearUserCache('info')

    // 获取缓存统计
    const stats = api.getStats()
    console.log('API统计信息:', stats)

    // 清除所有缓存
    api.clearCache()
  },

  // 批量操作示例
  async batchOperationsExample() {
    try {
      // 并行获取多个数据
      const [userInfo, checkInStatus, pointsBalance] = await Promise.all([
        api.user.getUserInfo({ showLoading: false }),
        api.checkIn.getCheckInStatus({ showLoading: false }),
        api.points.getPointsBalance({ showLoading: false })
      ])

      console.log('批量获取结果:', {
        userInfo,
        checkInStatus,
        pointsBalance
      })

      return { userInfo, checkInStatus, pointsBalance }
    } catch (error) {
      console.error('批量操作示例失败:', error)
      throw error
    }
  }
}

/**
 * 示例4：页面集成示例
 */
export const pageIntegrationExamples = {
  
  // 页面数据加载示例
  async pageDataLoadExample() {
    try {
      // 显示全局加载
      wx.showLoading({ title: '加载中...', mask: true })

      // 并行加载页面所需数据
      const [friendApps, userInfo, checkInStatus] = await Promise.all([
        api.friendApps.getFriendAppsWithCache({}, { showLoading: false }),
        api.user.getUserInfo({ showLoading: false }),
        api.checkIn.getCheckInStatus({ showLoading: false })
      ])

      // 隐藏全局加载
      wx.hideLoading()

      // 返回页面数据
      return {
        friendApps: friendApps.data,
        userInfo: userInfo.data,
        checkInStatus: checkInStatus.data
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      throw error
    }
  },

  // 下拉刷新示例
  async pullRefreshExample() {
    try {
      // 清除相关缓存
      api.friendApps.clearFriendAppsCache('all')
      api.user.clearUserCache('info')

      // 重新加载数据
      const refreshedData = await this.pageDataLoadExample()
      
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })

      return refreshedData
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
      throw error
    }
  },

  // 分页加载示例
  async paginationExample(page = 1, pageSize = 10) {
    try {
      const result = await api.points.getPointsRecordsByPage(page, pageSize, {}, {
        showLoading: page === 1, // 只有第一页显示加载
        loadingText: page === 1 ? '加载中...' : '加载更多...'
      })

      return {
        records: result.data.records,
        hasMore: result.data.records.length === pageSize,
        currentPage: page
      }
    } catch (error) {
      console.error('分页加载失败:', error)
      throw error
    }
  }
}

/**
 * 示例5：错误处理示例
 */
export const errorHandlingExamples = {
  
  // 统一错误处理
  async unifiedErrorHandling() {
    try {
      const result = await api.call('someApiThatMightFail')
      return result
    } catch (error) {
      // 根据错误类型进行不同处理
      if (error.message.includes('网络')) {
        wx.showModal({
          title: '网络异常',
          content: '请检查网络连接后重试',
          showCancel: false
        })
      } else if (error.message.includes('权限')) {
        wx.showModal({
          title: '权限不足',
          content: '请升级会员后使用此功能',
          showCancel: false
        })
      } else {
        wx.showToast({
          title: error.message,
          icon: 'none'
        })
      }
      throw error
    }
  },

  // 重试机制示例
  async retryExample() {
    try {
      const result = await api.callWithOptions('unstableApi', {}, {
        retry: true,
        retryConfig: {
          maxRetries: 3,
          baseDelay: 1000,
          retryCondition: (error) => {
            // 只有网络错误才重试
            return error.message.includes('网络') || error.message.includes('timeout')
          }
        },
        showLoading: true,
        showError: true
      })
      return result
    } catch (error) {
      console.error('重试后仍然失败:', error)
      throw error
    }
  }
}

// 导出所有示例
export default {
  basicUsageExamples,
  moduleApiExamples,
  advancedUsageExamples,
  pageIntegrationExamples,
  errorHandlingExamples
}
