/**
 * API模块统一导出
 * 提供简单和增强两种使用方式
 */

// 基础客户端
import baseApiClient from './base.js'
import enhancedApiClient from './enhanced.js'

// 工具类
import cacheManager from './utils/cache.js'
import { retryManager } from './utils/retry.js'

// API模块
import friendAppsApi from './modules/friend-apps.js'
import userApi from './modules/user.js'
import checkInApi from './modules/check-in.js'
import pointsApi from './modules/points.js'
import storeApi from './modules/store.js'
import holidayApi from './modules/holiday.js'
import generalApi from './modules/general.js'
import feedbackApi from './modules/feedback.js'
import invitationApi from './modules/invitation.js'
import syncApi from './modules/sync.js'
import fishingApi from './modules/fishing.js'

/**
 * API管理器
 * 提供统一的API管理和配置
 */
class ApiManager {
  constructor() {
    this.base = baseApiClient
    this.enhanced = enhancedApiClient
    this.cache = cacheManager
    this.retry = retryManager
    
    // API模块
    this.friendApps = friendAppsApi
    this.user = userApi
    this.checkIn = checkInApi
    this.points = pointsApi
    this.store = storeApi
    this.holiday = holidayApi
    this.feedback = feedbackApi
    this.invitation = invitationApi
    this.sync = syncApi
    this.fishing = fishingApi
    
    // 初始化配置
    this.config = {
      debug: false,
      defaultCacheTTL: 5 * 60 * 1000,  // 默认缓存5分钟
      defaultRetryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      }
    }
  }

  /**
   * 设置全局配置
   * @param {Object} config - 配置对象
   */
  setConfig(config) {
    this.config = { ...this.config, ...config }
    
    if (config.debug !== undefined) {
      console.log(`[ApiManager] 调试模式: ${config.debug ? '开启' : '关闭'}`)
    }
  }

  /**
   * 获取全局统计信息
   */
  getGlobalStats() {
    return {
      cache: this.cache.getStats(),
      retry: this.retry.getStats(),
      loading: this.enhanced.getAllLoadingStates()
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    console.log('[ApiManager] 清除所有API缓存')
    this.cache.clear()
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    console.log('[ApiManager] 清理过期缓存')
    return this.cache.cleanup()
  }

  /**
   * 预热所有模块的缓存
   */
  async warmupAllCache() {
    console.log('[ApiManager] 开始预热所有模块缓存')
    
    const warmupPromises = [
      this.friendApps.warmupCache(),
      this.holiday.warmupCache(),
      this.fishing.warmupCache()
    ]

    try {
      await Promise.allSettled(warmupPromises)
      console.log('[ApiManager] 所有模块缓存预热完成')
    } catch (error) {
      console.warn('[ApiManager] 缓存预热部分失败', error)
    }
  }

  /**
   * 重置所有统计信息
   */
  resetAllStats() {
    console.log('[ApiManager] 重置所有统计信息')
    this.enhanced.resetStats()
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      config: this.config,
      stats: this.getGlobalStats(),
      cacheInfo: this.cache.getAllCacheInfo()
    }
  }

  /**
   * 应用启动时的初始化
   */
  async initialize() {
    console.log('[ApiManager] 初始化API管理器')
    
    // 清理过期缓存
    this.cleanupExpiredCache()
    
    // 如果需要，可以预热缓存
    if (this.config.warmupOnInit) {
      await this.warmupAllCache()
    }
    
    console.log('[ApiManager] API管理器初始化完成')
  }

  /**
   * 应用销毁时的清理
   */
  destroy() {
    console.log('[ApiManager] 销毁API管理器')
    this.enhanced.cleanup()
  }
}

// 创建API管理器实例
const apiManager = new ApiManager()

// 简化的导出方式
export default apiManager

// 分别导出各个模块（向后兼容）
export {
  // 客户端
  baseApiClient,
  enhancedApiClient,
  
  // 工具
  cacheManager,
  retryManager,
  
  // API模块
  friendAppsApi,
  userApi,
  checkInApi,
  pointsApi,
  storeApi,
  holidayApi,
  generalApi,
  feedbackApi,
  invitationApi,
  syncApi,
  fishingApi,

  // 管理器
  apiManager
}

// 便捷访问方式
export const api = {
  // 增强调用（仅供内部使用）
  callWithOptions: enhancedApiClient.callWithOptions.bind(enhancedApiClient),
  
  // 模块API
  friendApps: friendAppsApi,
  user: userApi,
  checkIn: checkInApi,
  points: pointsApi,
  store: storeApi,
  holiday: holidayApi,
  general: generalApi,
  feedback: feedbackApi,
  invitation: invitationApi,
  sync: syncApi,
  fishing: fishingApi,
  
  // 工具方法
  clearCache: apiManager.clearAllCache.bind(apiManager),
  getStats: apiManager.getGlobalStats.bind(apiManager),
  warmup: apiManager.warmupAllCache.bind(apiManager)
}
