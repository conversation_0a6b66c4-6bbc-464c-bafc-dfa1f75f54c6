/**
 * 签到API模块
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

export class CheckInApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
  }

  /**
   * 执行签到
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 签到结果
   */
  async checkIn(options = {}) {
    const result = await this.enhancedClient.callWithOptions(
      'checkIn', 
      {}, 
      {
        loading: true,
        loadingKey: 'checkIn',
        showLoading: true,
        showError: true,
        retry: true,
        retryConfig: {
          maxRetries: 2,
          baseDelay: 1000
        },
        ...options
      }
    )

    // 签到成功后清除相关缓存
    if (result.success) {
      this.clearCheckInCache('status')
      this.clearCheckInCache('calendar')
      this.clearCheckInCache('history')
    }

    return result
  }

  /**
   * 获取签到状态
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 签到状态
   */
  async getCheckInStatus(options = {}) {
    return this.enhancedClient.callWithOptions(
      'getCheckInStatus', 
      {}, 
      {
        cache: true,
        cacheTTL: 1 * 60 * 1000,  // 缓存1分钟
        loading: true,
        loadingKey: 'getCheckInStatus',
        ...options
      }
    )
  }

  /**
   * 获取签到日历
   * @param {string} yearMonth - 年月 (YYYY-MM)
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 签到日历
   */
  async getCheckInCalendar(yearMonth, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getCheckInCalendar', 
      { yearMonth }, 
      {
        cache: true,
        cacheTTL: 5 * 60 * 1000,  // 缓存5分钟
        loading: true,
        loadingKey: `getCheckInCalendar_${yearMonth}`,
        ...options
      }
    )
  }

  /**
   * 获取签到历史
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 签到历史
   */
  async getCheckInHistory(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getCheckInHistory', 
      params, 
      {
        cache: true,
        cacheTTL: 3 * 60 * 1000,  // 缓存3分钟
        loading: true,
        loadingKey: 'getCheckInHistory',
        cacheIgnoreKeys: ['timestamp'], // 忽略时间戳参数
        ...options
      }
    )
  }

  /**
   * 清除签到相关缓存
   * @param {string} type - 缓存类型
   */
  clearCheckInCache(type = 'all') {
    console.log(`[CheckInAPI] 清除签到缓存: ${type}`)
    
    switch (type) {
      case 'all':
        this.enhancedClient.clearCache('getCheckInStatus')
        this.enhancedClient.clearCache('getCheckInCalendar')
        this.enhancedClient.clearCache('getCheckInHistory')
        break
      case 'status':
        this.enhancedClient.clearCache('getCheckInStatus')
        break
      case 'calendar':
        this.enhancedClient.clearCache('getCheckInCalendar')
        break
      case 'history':
        this.enhancedClient.clearCache('getCheckInHistory')
        break
    }
  }
}

// 创建签到API实例
const checkInApi = new CheckInApi()

export default checkInApi
export { checkInApi }
