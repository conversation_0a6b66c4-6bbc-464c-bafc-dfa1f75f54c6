/**
 * 反馈API模块
 * 封装反馈相关的API调用
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 反馈API类
 */
export class FeedbackApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 反馈相关操作不缓存（涉及状态变更）
      submitFeedback: {
        ttl: 0,
        ignoreKeys: []
      },
      getUserFeedbacks: {
        ttl: 0,
        ignoreKeys: []
      },
      deleteFeedback: {
        ttl: 0,
        ignoreKeys: []
      }
    }
  }

  /**
   * 提交反馈
   * @param {Object} params - 反馈数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 提交结果
   */
  async submitFeedback(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: true,
      showError: true,
      retry: true,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('submitFeedback', params, finalOptions)
  }

  /**
   * 获取用户反馈列表
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 反馈列表
   */
  async getUserFeedbacks(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('getUserFeedbacks', params, finalOptions)
  }

  /**
   * 删除反馈
   * @param {Object} params - 删除参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 删除结果
   */
  async deleteFeedback(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: true,
      showError: true,
      retry: true,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('deleteFeedback', params, finalOptions)
  }

  /**
   * 获取反馈API统计信息
   */
  getStats() {
    return {
      cacheConfig: this.cacheConfig,
      supportedApis: [
        'submitFeedback',
        'getUserFeedbacks',
        'deleteFeedback'
      ]
    }
  }
}

// 创建单例实例
const feedbackApi = new FeedbackApi()

export default feedbackApi
