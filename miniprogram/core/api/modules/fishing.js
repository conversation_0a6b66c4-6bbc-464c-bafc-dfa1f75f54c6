/**
 * 摸鱼API模块
 * 封装摸鱼相关的API调用
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 摸鱼API类
 */
export class FishingApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 摸鱼人数缓存配置
      getCurrentFishingCount: {
        ttl: 30 * 1000,             // 缓存30秒
        ignoreKeys: []
      },
      
      // 摸鱼状态操作不缓存（涉及状态变更）
      startFishingStatus: {
        ttl: 0,
        ignoreKeys: []
      },
      endFishingStatus: {
        ttl: 0,
        ignoreKeys: []
      },
      getUserFishingStatus: {
        ttl: 0,
        ignoreKeys: []
      }
    }
  }

  /**
   * 获取当前摸鱼人数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 摸鱼人数数据
   */
  async getCurrentFishingCount(options = {}) {
    const finalOptions = {
      cache: true,
      ttl: this.cacheConfig.getCurrentFishingCount.ttl,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('getCurrentFishingCount', {}, finalOptions)
  }

  /**
   * 开始摸鱼状态
   * @param {Object} params - 摸鱼数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 开始结果
   */
  async startFishingStatus(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('startFishingStatus', params, finalOptions)
  }

  /**
   * 结束摸鱼状态
   * @param {Object} params - 结束参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 结束结果
   */
  async endFishingStatus(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('endFishingStatus', params, finalOptions)
  }

  /**
   * 获取用户摸鱼状态
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 用户摸鱼状态
   */
  async getUserFishingStatus(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('getUserFishingStatus', params, finalOptions)
  }

  /**
   * 清除摸鱼API缓存
   * @param {string} type - 缓存类型 ('all' | 'count')
   */
  clearFishingCache(type = 'all') {
    const cacheKeys = []
    
    switch (type) {
      case 'count':
        cacheKeys.push('getCurrentFishingCount')
        break
      case 'all':
        cacheKeys.push('getCurrentFishingCount')
        break
      default:
        console.warn(`[FishingApi] 未知的缓存类型: ${type}`)
        return
    }

    cacheKeys.forEach(key => {
      this.enhancedClient.cache.clearByPattern(key)
    })
    
    console.log(`[FishingApi] 已清除缓存: ${cacheKeys.join(', ')}`)
  }

  /**
   * 预热摸鱼API缓存
   */
  async warmupCache() {
    console.log('[FishingApi] 开始预热缓存')
    
    try {
      await this.getCurrentFishingCount({ cache: true })
      console.log('[FishingApi] 缓存预热完成')
    } catch (error) {
      console.error('[FishingApi] 缓存预热失败:', error)
    }
  }

  /**
   * 获取摸鱼API统计信息
   */
  getStats() {
    return {
      cacheConfig: this.cacheConfig,
      supportedApis: [
        'getCurrentFishingCount',
        'startFishingStatus',
        'endFishingStatus',
        'getUserFishingStatus'
      ]
    }
  }
}

// 创建单例实例
const fishingApi = new FishingApi()

export default fishingApi
