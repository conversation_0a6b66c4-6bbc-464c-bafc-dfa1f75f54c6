/**
 * 友情应用API模块
 * 展示缓存功能的使用示例，支持不同参数的差异化缓存
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 友情应用API类
 */
export class FriendAppsApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 获取友情应用列表的缓存配置
      getFriendApps: {
        ttl: 10 * 60 * 1000,        // 缓存10分钟
        ignoreKeys: ['timestamp']    // 忽略时间戳参数
      },
      
      // 获取应用详情的缓存配置
      getFriendAppDetail: {
        ttl: 30 * 60 * 1000,        // 缓存30分钟
        ignoreKeys: []
      }
    }
  }

  /**
   * 获取友情应用列表（基础版本）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 应用列表
   */
  async getFriendApps(params = {}) {
    return this.baseClient.callCloudFunction('getFriendApps', params)
  }

  /**
   * 获取友情应用列表（增强版本，带缓存）
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 应用列表
   */
  async getFriendAppsWithCache(params = {}, options = {}) {
    const defaultOptions = {
      cache: true,
      cacheTTL: this.cacheConfig.getFriendApps.ttl,
      cacheIgnoreKeys: this.cacheConfig.getFriendApps.ignoreKeys,
      loading: true,
      loadingKey: `getFriendApps_${JSON.stringify(params)}`,
      showLoading: options.showLoading !== false,
      retry: true,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      }
    }

    const finalOptions = { ...defaultOptions, ...options }
    
    return this.enhancedClient.callWithOptions('getFriendApps', params, finalOptions)
  }

  /**
   * 获取友情应用列表（支持不同参数的差异化缓存）
   * 
   * 示例用法：
   * - getFriendAppsByCategory('game') // 游戏类应用，独立缓存
   * - getFriendAppsByCategory('tool') // 工具类应用，独立缓存
   * - getFriendAppsBySort('popular') // 按热度排序，独立缓存
   * - getFriendAppsBySort('latest')  // 按最新排序，独立缓存
   */
  
  /**
   * 按分类获取友情应用（每个分类独立缓存）
   * @param {string} category - 应用分类
   * @param {Object} extraParams - 额外参数
   * @returns {Promise<Object>} 应用列表
   */
  async getFriendAppsByCategory(category, extraParams = {}) {
    const params = {
      category,
      ...extraParams
    }

    console.log(`[FriendAppsAPI] 获取分类应用: ${category}`)
    
    return this.getFriendAppsWithCache(params, {
      loadingKey: `getFriendApps_category_${category}`,
      cacheTTL: 15 * 60 * 1000  // 分类数据缓存15分钟
    })
  }

  /**
   * 按排序方式获取友情应用（每种排序独立缓存）
   * @param {string} sortBy - 排序方式 (popular, latest, rating)
   * @param {Object} extraParams - 额外参数
   * @returns {Promise<Object>} 应用列表
   */
  async getFriendAppsBySort(sortBy, extraParams = {}) {
    const params = {
      sortBy,
      ...extraParams
    }

    console.log(`[FriendAppsAPI] 获取排序应用: ${sortBy}`)
    
    return this.getFriendAppsWithCache(params, {
      loadingKey: `getFriendApps_sort_${sortBy}`,
      cacheTTL: 8 * 60 * 1000   // 排序数据缓存8分钟
    })
  }

  /**
   * 分页获取友情应用（每页独立缓存）
   * @param {number} page - 页码
   * @param {number} pageSize - 每页大小
   * @param {Object} extraParams - 额外参数
   * @returns {Promise<Object>} 应用列表
   */
  async getFriendAppsByPage(page = 1, pageSize = 10, extraParams = {}) {
    const params = {
      page,
      pageSize,
      ...extraParams
    }

    console.log(`[FriendAppsAPI] 获取分页应用: 第${page}页，每页${pageSize}条`)
    
    return this.getFriendAppsWithCache(params, {
      loadingKey: `getFriendApps_page_${page}_${pageSize}`,
      cacheTTL: 5 * 60 * 1000,  // 分页数据缓存5分钟
      cacheIgnoreKeys: ['timestamp', 'requestId'] // 忽略这些参数的缓存差异
    })
  }

  /**
   * 搜索友情应用（搜索结果独立缓存）
   * @param {string} keyword - 搜索关键词
   * @param {Object} extraParams - 额外参数
   * @returns {Promise<Object>} 搜索结果
   */
  async searchFriendApps(keyword, extraParams = {}) {
    const params = {
      keyword: keyword.trim(),
      ...extraParams
    }

    console.log(`[FriendAppsAPI] 搜索应用: ${keyword}`)
    
    return this.getFriendAppsWithCache(params, {
      loadingKey: `searchFriendApps_${keyword}`,
      cacheTTL: 3 * 60 * 1000,  // 搜索结果缓存3分钟
      showLoading: true
    })
  }

  /**
   * 获取热门友情应用（长期缓存）
   * @returns {Promise<Object>} 热门应用列表
   */
  async getPopularFriendApps() {
    console.log('[FriendAppsAPI] 获取热门应用')
    
    return this.getFriendAppsWithCache({ popular: true }, {
      loadingKey: 'getFriendApps_popular',
      cacheTTL: 30 * 60 * 1000,  // 热门数据缓存30分钟
      retry: true
    })
  }

  /**
   * 清除友情应用相关缓存
   * @param {string} type - 缓存类型 (all, category, sort, page, search, popular)
   * @param {string} value - 具体值（如分类名、排序方式等）
   */
  clearFriendAppsCache(type = 'all', value = null) {
    console.log(`[FriendAppsAPI] 清除缓存: ${type}${value ? ` - ${value}` : ''}`)
    
    switch (type) {
      case 'all':
        return this.enhancedClient.clearCache('getFriendApps')
        
      case 'category':
        return this.enhancedClient.clearCache('getFriendApps', { category: value })
        
      case 'sort':
        return this.enhancedClient.clearCache('getFriendApps', { sortBy: value })
        
      case 'page':
        const [page, pageSize] = (value || '1,10').split(',')
        return this.enhancedClient.clearCache('getFriendApps', { 
          page: parseInt(page), 
          pageSize: parseInt(pageSize) 
        })
        
      case 'search':
        return this.enhancedClient.clearCache('getFriendApps', { keyword: value })
        
      case 'popular':
        return this.enhancedClient.clearCache('getFriendApps', { popular: true })
        
      default:
        console.warn(`[FriendAppsAPI] 未知的缓存类型: ${type}`)
        return 0
    }
  }

  /**
   * 预热常用缓存
   */
  async warmupCache() {
    console.log('[FriendAppsAPI] 开始预热缓存')
    
    const warmupRequests = [
      // 预热热门应用
      { type: 'getFriendApps', data: { popular: true } },
      
      // 预热第一页数据
      { type: 'getFriendApps', data: { page: 1, pageSize: 10 } },
      
      // 预热常用分类
      { type: 'getFriendApps', data: { category: 'game' } },
      { type: 'getFriendApps', data: { category: 'tool' } },
      
      // 预热常用排序
      { type: 'getFriendApps', data: { sortBy: 'popular' } },
      { type: 'getFriendApps', data: { sortBy: 'latest' } }
    ]

    await this.enhancedClient.batchWarmupCache(warmupRequests)
    console.log('[FriendAppsAPI] 缓存预热完成')
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.enhancedClient.getStats()
  }
}

// 创建友情应用API实例
const friendAppsApi = new FriendAppsApi()

// 导出实例和类
export default friendAppsApi
export { friendAppsApi }
