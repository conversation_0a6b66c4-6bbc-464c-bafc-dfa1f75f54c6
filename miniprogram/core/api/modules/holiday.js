/**
 * 节假日API模块
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

export class HolidayApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
  }

  /**
   * 获取节假日数据
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 节假日数据
   */
  async getHolidayData(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getHolidayData', 
      params, 
      {
        cache: true,
        cacheTTL: 24 * 60 * 60 * 1000,  // 缓存24小时
        loading: true,
        loadingKey: `getHolidayData_${params.year || 'current'}`,
        cacheIgnoreKeys: ['timestamp'], // 忽略时间戳参数
        ...options
      }
    )
  }

  /**
   * 获取周末数据
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 周末数据
   */
  async getWeekendData(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getWeekendData', 
      params, 
      {
        cache: true,
        cacheTTL: 24 * 60 * 60 * 1000,  // 缓存24小时
        loading: true,
        loadingKey: `getWeekendData_${params.year || 'current'}`,
        cacheIgnoreKeys: ['timestamp'],
        ...options
      }
    )
  }

  /**
   * 按年份获取节假日数据（每年独立缓存）
   * @param {number} year - 年份
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 节假日数据
   */
  async getHolidayDataByYear(year, options = {}) {
    return this.getHolidayData({ year }, {
      cacheTTL: 7 * 24 * 60 * 60 * 1000,  // 历史数据缓存7天
      ...options
    })
  }

  /**
   * 按年份获取周末数据（每年独立缓存）
   * @param {number} year - 年份
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 周末数据
   */
  async getWeekendDataByYear(year, options = {}) {
    return this.getWeekendData({ year }, {
      cacheTTL: 7 * 24 * 60 * 60 * 1000,  // 历史数据缓存7天
      ...options
    })
  }

  /**
   * 获取当前年份的节假日数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 当前年份节假日数据
   */
  async getCurrentYearHolidayData(options = {}) {
    const currentYear = new Date().getFullYear()
    return this.getHolidayDataByYear(currentYear, options)
  }

  /**
   * 获取当前年份的周末数据
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 当前年份周末数据
   */
  async getCurrentYearWeekendData(options = {}) {
    const currentYear = new Date().getFullYear()
    return this.getWeekendDataByYear(currentYear, options)
  }

  /**
   * 清除节假日相关缓存
   * @param {string} type - 缓存类型
   * @param {number} year - 年份（可选）
   */
  clearHolidayCache(type = 'all', year = null) {
    console.log(`[HolidayAPI] 清除节假日缓存: ${type}${year ? ` - ${year}` : ''}`)
    
    switch (type) {
      case 'all':
        this.enhancedClient.clearCache('getHolidayData')
        this.enhancedClient.clearCache('getWeekendData')
        break
      case 'holiday':
        if (year) {
          this.enhancedClient.clearCache('getHolidayData', { year })
        } else {
          this.enhancedClient.clearCache('getHolidayData')
        }
        break
      case 'weekend':
        if (year) {
          this.enhancedClient.clearCache('getWeekendData', { year })
        } else {
          this.enhancedClient.clearCache('getWeekendData')
        }
        break
    }
  }

  /**
   * 预热常用年份的缓存
   */
  async warmupCache() {
    console.log('[HolidayAPI] 开始预热节假日缓存')
    
    const currentYear = new Date().getFullYear()
    const warmupRequests = [
      // 预热当前年份
      { type: 'getHolidayData', data: { year: currentYear } },
      { type: 'getWeekendData', data: { year: currentYear } },
      
      // 预热下一年
      { type: 'getHolidayData', data: { year: currentYear + 1 } },
      { type: 'getWeekendData', data: { year: currentYear + 1 } }
    ]

    await this.enhancedClient.batchWarmupCache(warmupRequests)
    console.log('[HolidayAPI] 节假日缓存预热完成')
  }
}

// 创建节假日API实例
const holidayApi = new HolidayApi()

export default holidayApi
export { holidayApi }
