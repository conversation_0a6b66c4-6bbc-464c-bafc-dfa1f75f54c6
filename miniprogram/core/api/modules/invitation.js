/**
 * 邀请API模块
 * 封装邀请相关的API调用
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

/**
 * 邀请API类
 */
export class InvitationApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
    
    // 缓存配置
    this.cacheConfig = {
      // 邀请统计不缓存（数据实时性要求高）
      getInvitationStats: {
        ttl: 0,
        ignoreKeys: []
      },

      // 处理邀请不缓存（涉及状态变更）
      processInvitation: {
        ttl: 0,
        ignoreKeys: []
      }
    }
  }

  /**
   * 获取邀请统计信息
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 邀请统计数据
   */
  async getInvitationStats(options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: false,
      showError: false,
      ...options
    }

    return this.enhancedClient.callWithOptions('getInvitationStats', {}, finalOptions)
  }

  /**
   * 处理邀请绑定
   * @param {Object} params - 邀请参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 处理结果
   */
  async processInvitation(params = {}, options = {}) {
    const finalOptions = {
      cache: false,
      showLoading: true,
      showError: true,
      retry: true,
      retryConfig: {
        maxRetries: 2,
        baseDelay: 1000
      },
      ...options
    }

    return this.enhancedClient.callWithOptions('processInvitation', params, finalOptions)
  }

  /**
   * 获取邀请API统计信息
   */
  getStats() {
    return {
      cacheConfig: this.cacheConfig,
      supportedApis: [
        'getInvitationStats',
        'processInvitation'
      ]
    }
  }
}

// 创建单例实例
const invitationApi = new InvitationApi()

export default invitationApi
