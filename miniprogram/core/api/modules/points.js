/**
 * 积分API模块
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

export class PointsApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
  }

  /**
   * 获取积分余额
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 积分余额
   */
  async getPointsBalance(options = {}) {
    return this.enhancedClient.callWithOptions(
      'getPointsBalance', 
      {}, 
      {
        cache: true,
        cacheTTL: 2 * 60 * 1000,  // 缓存2分钟
        loading: true,
        loadingKey: 'getPointsBalance',
        ...options
      }
    )
  }

  /**
   * 获取积分记录
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 积分记录
   */
  async getPointsRecords(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getPointsRecords',
      params,
      {
        cache: true,
        cacheTTL: 1 * 60 * 1000,  // 缓存1分钟
        loading: true,
        loadingKey: `getPointsRecords_${params.type || 'all'}`,
        cacheIgnoreKeys: ['timestamp'], // 忽略时间戳参数
        ...options
      }
    )
  }

  /**
   * 获取积分统计
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 积分统计
   */
  async getPointsStats(options = {}) {
    return this.enhancedClient.callWithOptions(
      'getPointsStats', 
      {}, 
      {
        cache: true,
        cacheTTL: 5 * 60 * 1000,  // 缓存5分钟
        loading: true,
        loadingKey: 'getPointsStats',
        ...options
      }
    )
  }



  /**
   * 清除积分相关缓存
   * @param {string} type - 缓存类型
   * @param {string} value - 具体值
   */
  clearPointsCache(type = 'all', value = null) {
    console.log(`[PointsAPI] 清除积分缓存: ${type}${value ? ` - ${value}` : ''}`)
    
    switch (type) {
      case 'all':
        this.enhancedClient.clearCache('getPointsBalance')
        this.enhancedClient.clearCache('getPointsRecords')
        this.enhancedClient.clearCache('getPointsStats')
        break
      case 'balance':
        this.enhancedClient.clearCache('getPointsBalance')
        break
      case 'records':
        this.enhancedClient.clearCache('getPointsRecords')
        break
      case 'stats':
        this.enhancedClient.clearCache('getPointsStats')
        break
      case 'recordsByType':
        this.enhancedClient.clearCache('getPointsRecords', { type: value })
        break
    }
  }

  /**
   * 积分变动后的缓存更新
   * 当积分发生变化时调用此方法清除相关缓存
   */
  onPointsChanged() {
    console.log('[PointsAPI] 积分发生变化，清除相关缓存')
    this.clearPointsCache('balance')
    this.clearPointsCache('records')
    this.clearPointsCache('stats')
  }
}

// 创建积分API实例
const pointsApi = new PointsApi()

export default pointsApi
export { pointsApi }
