/**
 * 商店API模块
 */

import baseApiClient from '../base.js'
import enhancedApiClient from '../enhanced.js'

export class StoreApi {
  constructor() {
    this.baseClient = baseApiClient
    this.enhancedClient = enhancedApiClient
  }

  /**
   * 获取商店商品
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 商品列表
   */
  async getStoreItems(options = {}) {
    return this.enhancedClient.callWithOptions(
      'getStoreItems', 
      {}, 
      {
        cache: true,
        cacheTTL: 10 * 60 * 1000,  // 缓存10分钟
        loading: true,
        loadingKey: 'getStoreItems',
        ...options
      }
    )
  }

  /**
   * 购买商品
   * @param {string} itemId - 商品ID
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 购买结果
   */
  async purchaseItem(itemId, options = {}) {
    const result = await this.enhancedClient.callWithOptions(
      'purchaseItem', 
      { itemId }, 
      {
        loading: true,
        loadingKey: 'purchaseItem',
        showLoading: true,
        showError: true,
        retry: true,
        retryConfig: {
          maxRetries: 1,
          baseDelay: 1000
        },
        ...options
      }
    )

    // 购买成功后清除相关缓存
    if (result.success) {
      this.enhancedClient.clearCache('getStoreItems')
      this.enhancedClient.clearCache('getMyRedemptionCodes')
      // 清除积分相关缓存（因为购买会消耗积分）
      this.enhancedClient.clearCache('getPointsBalance')
      this.enhancedClient.clearCache('getPointsRecords')
    }

    return result
  }

  /**
   * 获取我的兑换码
   * @param {Object} params - 查询参数
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 兑换码列表
   */
  async getMyRedemptionCodes(params = {}, options = {}) {
    return this.enhancedClient.callWithOptions(
      'getMyRedemptionCodes', 
      params, 
      {
        cache: true,
        cacheTTL: 2 * 60 * 1000,  // 缓存2分钟
        loading: true,
        loadingKey: 'getMyRedemptionCodes',
        ...options
      }
    )
  }

  /**
   * 兑换码
   * @param {string} code - 兑换码
   * @param {Object} options - 调用选项
   * @returns {Promise<Object>} 兑换结果
   */
  async redeemCode(code, options = {}) {
    const result = await this.enhancedClient.callWithOptions(
      'redeemCode', 
      { code }, 
      {
        loading: true,
        loadingKey: 'redeemCode',
        showLoading: true,
        showError: true,
        retry: true,
        retryConfig: {
          maxRetries: 1,
          baseDelay: 1000
        },
        ...options
      }
    )

    // 兑换成功后清除相关缓存
    if (result.success) {
      this.enhancedClient.clearCache('getMyRedemptionCodes')
      // 可能影响积分或其他数据
      this.enhancedClient.clearCache('getPointsBalance')
      this.enhancedClient.clearCache('getUserInfo')
    }

    return result
  }

  /**
   * 清除商店相关缓存
   * @param {string} type - 缓存类型
   */
  clearStoreCache(type = 'all') {
    console.log(`[StoreAPI] 清除商店缓存: ${type}`)
    
    switch (type) {
      case 'all':
        this.enhancedClient.clearCache('getStoreItems')
        this.enhancedClient.clearCache('getMyRedemptionCodes')
        break
      case 'items':
        this.enhancedClient.clearCache('getStoreItems')
        break
      case 'codes':
        this.enhancedClient.clearCache('getMyRedemptionCodes')
        break
    }
  }
}

// 创建商店API实例
const storeApi = new StoreApi()

export default storeApi
export { storeApi }
