/**
 * 缓存工具类
 * 支持参数差异化缓存、过期时间、缓存统计等功能
 */

class CacheManager {
  constructor() {
    this.cache = new Map()
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    }
  }

  /**
   * 生成缓存key
   * @param {string} type - API类型
   * @param {Object} params - 参数对象
   * @param {Array} ignoreKeys - 忽略的参数键名
   * @returns {string} 缓存key
   */
  generateCacheKey(type, params = {}, ignoreKeys = []) {
    // 过滤掉忽略的键
    const filteredParams = {}
    Object.keys(params).forEach(key => {
      if (!ignoreKeys.includes(key)) {
        filteredParams[key] = params[key]
      }
    })

    // 对参数进行排序，确保相同参数生成相同key
    const sortedParams = this._sortObject(filteredParams)
    const paramsStr = JSON.stringify(sortedParams)
    
    return `${type}_${this._hashCode(paramsStr)}`
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存key
   * @param {*} value - 缓存值
   * @param {number} ttl - 过期时间（毫秒），0表示永不过期
   */
  set(key, value, ttl = 0) {
    const now = Date.now()
    const item = {
      value,
      createdAt: now,
      expiresAt: ttl > 0 ? now + ttl : 0,
      accessCount: 0,
      lastAccessed: now
    }

    this.cache.set(key, item)
    this.stats.sets++

    console.log(`[Cache] 设置缓存: ${key}, TTL: ${ttl}ms`)
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存key
   * @returns {*} 缓存值，不存在或过期返回null
   */
  get(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      this.stats.misses++
      return null
    }

    const now = Date.now()
    
    // 检查是否过期
    if (item.expiresAt > 0 && now > item.expiresAt) {
      this.cache.delete(key)
      this.stats.misses++
      this.stats.deletes++
      console.log(`[Cache] 缓存过期删除: ${key}`)
      return null
    }

    // 更新访问信息
    item.accessCount++
    item.lastAccessed = now
    
    this.stats.hits++
    console.log(`[Cache] 缓存命中: ${key}`)
    return item.value
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存key
   */
  delete(key) {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.stats.deletes++
      console.log(`[Cache] 删除缓存: ${key}`)
    }
    return deleted
  }

  /**
   * 清空所有缓存
   */
  clear() {
    const size = this.cache.size
    this.cache.clear()
    this.stats.deletes += size
    console.log(`[Cache] 清空所有缓存，共删除 ${size} 项`)
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    let deletedCount = 0

    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt > 0 && now > item.expiresAt) {
        this.cache.delete(key)
        deletedCount++
      }
    }

    this.stats.deletes += deletedCount
    console.log(`[Cache] 清理过期缓存，删除 ${deletedCount} 项`)
    return deletedCount
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0

    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: `${hitRate}%`
    }
  }

  /**
   * 获取所有缓存信息（调试用）
   */
  getAllCacheInfo() {
    const info = []
    for (const [key, item] of this.cache.entries()) {
      info.push({
        key,
        size: JSON.stringify(item.value).length,
        createdAt: new Date(item.createdAt).toLocaleString(),
        expiresAt: item.expiresAt > 0 ? new Date(item.expiresAt).toLocaleString() : '永不过期',
        accessCount: item.accessCount,
        lastAccessed: new Date(item.lastAccessed).toLocaleString()
      })
    }
    return info
  }

  /**
   * 对象排序（用于生成一致的key）
   */
  _sortObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this._sortObject(item))
    }

    const sorted = {}
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this._sortObject(obj[key])
    })

    return sorted
  }

  /**
   * 简单哈希函数
   */
  _hashCode(str) {
    let hash = 0
    if (str.length === 0) return hash
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36)
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager()

export default cacheManager
