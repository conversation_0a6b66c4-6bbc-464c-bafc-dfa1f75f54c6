/**
 * 重试工具
 * 提供请求重试功能
 */

/**
 * 重试配置
 */
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,           // 最大重试次数
  baseDelay: 1000,         // 基础延迟时间（毫秒）
  maxDelay: 10000,         // 最大延迟时间（毫秒）
  backoffFactor: 2,        // 退避因子
  retryCondition: null     // 重试条件函数
}

/**
 * 默认重试条件
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否应该重试
 */
function defaultRetryCondition(error) {
  // 网络错误或超时错误才重试
  const retryableErrors = [
    'network error',
    'timeout',
    'request:fail',
    'cloud function timeout'
  ]
  
  const errorMessage = error.message?.toLowerCase() || ''
  return retryableErrors.some(keyword => errorMessage.includes(keyword))
}

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 计算退避延迟时间
 * @param {number} attempt - 当前重试次数（从0开始）
 * @param {Object} config - 重试配置
 */
function calculateBackoffDelay(attempt, config) {
  const exponentialDelay = config.baseDelay * Math.pow(config.backoffFactor, attempt)
  const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5) // 添加抖动
  return Math.min(jitteredDelay, config.maxDelay)
}

/**
 * 重试装饰器
 * @param {Function} fn - 要重试的异步函数
 * @param {Object} options - 重试选项
 * @returns {Function} 包装后的函数
 */
export function withRetry(fn, options = {}) {
  const config = { ...DEFAULT_RETRY_CONFIG, ...options }
  const retryCondition = config.retryCondition || defaultRetryCondition

  return async function(...args) {
    let lastError
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        console.log(`[Retry] 尝试执行 (${attempt + 1}/${config.maxRetries + 1})`)
        const result = await fn.apply(this, args)
        
        if (attempt > 0) {
          console.log(`[Retry] 重试成功，共重试 ${attempt} 次`)
        }
        
        return result
      } catch (error) {
        lastError = error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === config.maxRetries) {
          console.error(`[Retry] 重试失败，已达到最大重试次数 ${config.maxRetries}`)
          throw error
        }
        
        // 检查是否应该重试
        if (!retryCondition(error)) {
          console.log(`[Retry] 错误不满足重试条件: ${error.message}`)
          throw error
        }
        
        // 计算延迟时间并等待
        const delayTime = calculateBackoffDelay(attempt, config)
        console.log(`[Retry] 第 ${attempt + 1} 次重试失败: ${error.message}，${delayTime}ms 后重试`)
        await delay(delayTime)
      }
    }
    
    throw lastError
  }
}

/**
 * 重试管理器
 */
export class RetryManager {
  constructor(defaultConfig = {}) {
    this.defaultConfig = { ...DEFAULT_RETRY_CONFIG, ...defaultConfig }
    this.stats = {
      totalAttempts: 0,
      totalRetries: 0,
      successAfterRetry: 0,
      failedAfterRetry: 0
    }
  }

  /**
   * 执行带重试的函数
   * @param {Function} fn - 要执行的函数
   * @param {Object} config - 重试配置
   */
  async execute(fn, config = {}) {
    const finalConfig = { ...this.defaultConfig, ...config }
    const retryCondition = finalConfig.retryCondition || defaultRetryCondition
    
    let lastError
    let retryCount = 0
    
    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      this.stats.totalAttempts++
      
      try {
        const result = await fn()
        
        if (retryCount > 0) {
          this.stats.successAfterRetry++
          console.log(`[RetryManager] 重试成功，共重试 ${retryCount} 次`)
        }
        
        return result
      } catch (error) {
        lastError = error
        
        if (attempt === finalConfig.maxRetries) {
          this.stats.failedAfterRetry++
          break
        }
        
        if (!retryCondition(error)) {
          break
        }
        
        retryCount++
        this.stats.totalRetries++
        
        const delayTime = calculateBackoffDelay(attempt, finalConfig)
        console.log(`[RetryManager] 第 ${attempt + 1} 次重试失败，${delayTime}ms 后重试`)
        await delay(delayTime)
      }
    }
    
    throw lastError
  }

  /**
   * 获取重试统计信息
   */
  getStats() {
    const successRate = this.stats.totalAttempts > 0 
      ? ((this.stats.totalAttempts - this.stats.failedAfterRetry) / this.stats.totalAttempts * 100).toFixed(2)
      : 0

    return {
      ...this.stats,
      successRate: `${successRate}%`,
      avgRetriesPerRequest: this.stats.totalAttempts > 0 
        ? (this.stats.totalRetries / this.stats.totalAttempts).toFixed(2)
        : 0
    }
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      totalAttempts: 0,
      totalRetries: 0,
      successAfterRetry: 0,
      failedAfterRetry: 0
    }
  }
}

// 创建默认重试管理器实例
export const retryManager = new RetryManager()

export default {
  withRetry,
  RetryManager,
  retryManager
}
