/**
 * 小程序版本配置
 * 用于版本追踪和统计分析
 */

/**
 * 当前小程序版本
 * 格式：主版本号.次版本号.修订号
 * 
 * 版本号规则：
 * - 主版本号：重大功能更新或架构变更
 * - 次版本号：新功能添加或重要改进
 * - 修订号：bug修复或小幅优化
 */
const APP_VERSION = '0.1.2'

/**
 * 版本发布日期
 */
const VERSION_DATE = '2025-08-05'

/**
 * 版本描述
 */
const VERSION_DESCRIPTION = '内测版本'

/**
 * 获取完整版本信息
 * @returns {Object} 版本信息对象
 */
function getVersionInfo() {
  return {
    version: APP_VERSION,
    date: VERSION_DATE,
    description: VERSION_DESCRIPTION,
    timestamp: Date.now()
  }
}

/**
 * 获取版本号
 * @returns {string} 版本号字符串
 */
function getVersion() {
  return APP_VERSION
}

/**
 * 比较版本号
 * @param {string} version1 版本号1
 * @param {string} version2 版本号2
 * @returns {number} 比较结果：-1(小于), 0(等于), 1(大于)
 */
function compareVersion(version1, version2) {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length)
  
  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part < v2Part) return -1
    if (v1Part > v2Part) return 1
  }
  
  return 0
}

// 导出版本信息
module.exports = {
  APP_VERSION,
  VERSION_DATE,
  VERSION_DESCRIPTION,
  getVersionInfo,
  getVersion,
  compareVersion
}
