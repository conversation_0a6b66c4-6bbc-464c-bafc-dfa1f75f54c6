/**
 * 核心模块统一导出文件
 * 提供所有核心管理器和服务的统一入口
 */

// 导入所有模块
const { getDataManager } = require('./managers/data-manager.js')
const { getUserManager } = require('./managers/user-manager.js')
const { getSyncManager } = require('./managers/sync-manager.js')
const { getDashboardService } = require('./services/dashboard-service.js')
const { getWorkHistoryService } = require('./services/work-history-service.js')
const { getTimeSegmentService } = require('./services/time-segment-service.js')
const { getDashboardBaseService } = require('./services/dashboard-base-service.js')
const { getDataImportExportService } = require('./services/data-import-export-service.js')
const { getStatisticsService } = require('./services/statistics-service.js')

// 统一导出
module.exports = {
  // 数据管理器工厂函数
  getDataManager,
  getUserManager,
  getSyncManager,

  // 服务工厂函数
  getDashboardService,
  getWorkHistoryService,
  getTimeSegmentService,
  getDashboardBaseService,
  getDataImportExportService,
  getStatisticsService
}
