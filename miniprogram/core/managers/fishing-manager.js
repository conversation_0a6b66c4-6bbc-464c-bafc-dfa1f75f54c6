/**
 * 摸鱼管理器
 * 负责摸鱼数据的管理和计算
 *
 * 功能特性：
 * - 摸鱼记录的增删改查
 * - 摸鱼状态管理（开始/结束）
 * - 摸鱼收入计算
 * - 自动结束摸鱼处理
 * - LocalStorage状态持久化
 */

import { api } from '../api/index.js'
const { minutesToTimeDisplay, formatDuration, formatDateKey } = require('../../utils/helpers/time-utils.js')
const { TimeSegmentChangeEvent } = require('./time-segment-status-manager')

// 自动结束原因枚举
const AutoEndReason = {
  TIME_SEGMENT_END: 'time_segment_end',     // 时间段结束
  ENTER_REST: 'enter_rest',                 // 进入休息时间
  ENTER_NO_SEGMENT: 'enter_no_segment',     // 进入无时间段状态
  LEAVE_WORK: 'leave_work',                 // 离开工作状态
  MANUAL: 'manual'                          // 手动触发
}

/**
 * 摸鱼管理器类
 */
class FishingManager {
  constructor() {
    // 数据变化监听器数组
    this.changeListeners = []

    // LocalStorage键名
    this.FISHING_STATE_KEY = 'fishing_state'

    // 自动结束管理相关属性
    this.processedSessions = new Set() // 已处理的摸鱼会话ID集合
    this.isProcessing = false // 当前是否正在处理自动结束
    this.currentSessionId = null // 当前处理的会话ID
    this.lastProcessTime = 0 // 最后处理时间，用于防抖
    this.debounceDelay = 2000 // 防抖延迟（毫秒）
    this.autoEndListeners = [] // 自动结束监听器数组
  }

  /**
   * 验证摸鱼数据
   * @param {Object} fishingData - 摸鱼数据
   * @returns {Object} 验证结果 { isValid: boolean, errors: Array }
   */
  validateFishingData(fishingData) {
    const errors = []

    if (!fishingData) {
      errors.push('摸鱼数据不能为空')
      return { isValid: false, errors }
    }

    // 验证开始时间
    if (typeof fishingData.start !== 'number' || fishingData.start < 0) {
      errors.push('开始时间必须是非负数字')
    }

    // 验证结束时间
    if (typeof fishingData.end !== 'number' || fishingData.end < 0) {
      errors.push('结束时间必须是非负数字')
    }

    // 验证时间逻辑
    if (fishingData.start >= fishingData.end) {
      errors.push('结束时间必须大于开始时间')
    }

    // 验证备注长度
    if (fishingData.remark && fishingData.remark.length > 200) {
      errors.push('备注长度不能超过200个字符')
    }

    return { isValid: errors.length === 0, errors }
  }

  /**
   * 验证摸鱼时间段是否在工作时间段内
   * @param {Object} fishingData - 摸鱼数据
   * @param {Array} segments - 工作时间段数组
   * @returns {Object} 验证结果 { isValid: boolean, errors: Array, workSegment: Object }
   */
  validateFishingInWorkTime(fishingData, segments) {
    const errors = []
    let workSegment = null

    if (!segments || segments.length === 0) {
      errors.push('没有工作时间段，无法摸鱼')
      return { isValid: false, errors, workSegment }
    }

    // 查找包含摸鱼时间的工作时间段
    const containingSegments = segments.filter(segment => {
      // 只检查工作和加班时间段
      if (segment.type === 'rest') return false
      
      return fishingData.start >= segment.start && 
             fishingData.end <= segment.end
    })

    if (containingSegments.length === 0) {
      errors.push('摸鱼时间必须完全在工作时间段内')
    } else if (containingSegments.length > 1) {
      errors.push('摸鱼时间跨越了多个工作时间段')
    } else {
      workSegment = containingSegments[0]
    }

    return { 
      isValid: errors.length === 0, 
      errors, 
      workSegment 
    }
  }

  /**
   * 检查摸鱼时间段是否与现有摸鱼记录冲突
   * @param {Object} fishingData - 摸鱼数据
   * @param {Array} existingFishes - 现有摸鱼记录数组
   * @param {number} excludeId - 排除的ID（编辑时使用）
   * @returns {Object} 检查结果 { hasConflict: boolean, conflicts: Array }
   */
  checkFishingConflicts(fishingData, existingFishes, excludeId = null) {
    const conflicts = []

    if (!existingFishes || existingFishes.length === 0) {
      return { hasConflict: false, conflicts }
    }

    existingFishes.forEach(fish => {
      // 排除指定ID（编辑时使用）
      if (excludeId !== null && fish.id === excludeId) {
        return
      }

      // 检查时间重叠
      const hasOverlap = !(fishingData.end <= fish.start || fishingData.start >= fish.end)
      
      if (hasOverlap) {
        conflicts.push({
          id: fish.id,
          start: fish.start,
          end: fish.end,
          startTime: minutesToTimeDisplay(fish.start),
          endTime: minutesToTimeDisplay(fish.end)
        })
      }
    })

    return { 
      hasConflict: conflicts.length > 0, 
      conflicts 
    }
  }

  /**
   * 将摸鱼数据转换为显示格式
   * @param {Object} fishing - 原始摸鱼数据
   * @param {number} hourlyRate - 时薪（用于计算摸鱼收入）
   * @returns {Object} 转换后的摸鱼数据
   */
  convertFishingForDisplay(fishing, hourlyRate = 0) {
    if (!fishing) return fishing

    const duration = fishing.end - fishing.start
    const fishingIncome = hourlyRate > 0 ? (duration / 60) * hourlyRate : 0

    return {
      // 核心数据
      id: fishing.id,
      start: fishing.start,
      end: fishing.end,
      remark: fishing.remark || '',

      // 动态计算的数据
      startTime: minutesToTimeDisplay(fishing.start),
      endTime: minutesToTimeDisplay(fishing.end),
      duration: formatDuration(duration),
      durationMinutes: duration,
      income: fishingIncome
    }
  }

  /**
   * 计算摸鱼总收入
   * @param {Array} fishes - 摸鱼记录数组
   * @param {number} hourlyRate - 时薪
   * @returns {number} 总摸鱼收入
   */
  calculateFishingIncome(fishes, hourlyRate = 0) {
    if (!fishes || fishes.length === 0 || hourlyRate <= 0) {
      return 0
    }

    return fishes.reduce((total, fish) => {
      const duration = fish.end - fish.start
      const fishingIncome = (duration / 60) * hourlyRate
      return total + fishingIncome
    }, 0)
  }

  /**
   * 计算摸鱼总时长（分钟）
   * @param {Array} fishes - 摸鱼记录数组
   * @returns {number} 总摸鱼时长（分钟）
   */
  calculateFishingMinutes(fishes) {
    if (!fishes || fishes.length === 0) {
      return 0
    }

    return fishes.reduce((total, fish) => {
      return total + (fish.end - fish.start)
    }, 0)
  }

  /**
   * 保存摸鱼状态到LocalStorage
   * @param {Object} fishingState - 摸鱼状态
   */
  saveFishingState(fishingState) {
    try {
      wx.setStorageSync(this.FISHING_STATE_KEY, fishingState)
    } catch (error) {
      console.error('保存摸鱼状态失败:', error)
    }
  }

  /**
   * 从LocalStorage获取摸鱼状态
   * @returns {Object|null} 摸鱼状态
   */
  getFishingState() {
    try {
      return wx.getStorageSync(this.FISHING_STATE_KEY) || null
    } catch (error) {
      console.error('获取摸鱼状态失败:', error)
      return null
    }
  }

  /**
   * 清除摸鱼状态
   */
  clearFishingState() {
    try {
      wx.removeStorageSync(this.FISHING_STATE_KEY)
    } catch (error) {
      console.error('清除摸鱼状态失败:', error)
    }
  }

  /**
   * 计算当前时间在摸鱼时间段中的实际位置
   * @param {Object} fishingState - 摸鱼状态
   * @param {number} currentMinutes - 当前时间（分钟）
   * @returns {number} 当前时间在摸鱼时间段中的实际位置
   */
  calculateActualCurrentMinutes(fishingState, currentMinutes) {
    if (fishingState.isCrossDateSegment) {
      // 跨日期摸鱼：当前时间需要转换到跨日期时间段中的位置
      return currentMinutes + 1440
    } else {
      // 普通摸鱼：直接使用当前时间
      return currentMinutes
    }
  }

  /**
   * 计算当前摸鱼时长（毫秒）
   * @returns {number} 摸鱼时长（毫秒）
   */
  calculateCurrentFishingDurationMs() {
    const fishingState = this.getFishingState()
    if (!fishingState || !fishingState.isActive) {
      return 0
    }

    const now = new Date()
    const startTime = new Date(fishingState.startTime)

    return Math.max(0, now.getTime() - startTime.getTime())
  }

  /**
   * 计算当前摸鱼时长（分钟）
   * @returns {number} 摸鱼时长（分钟）
   */
  calculateCurrentFishingDuration() {
    const durationMs = this.calculateCurrentFishingDurationMs()
    return Math.floor(durationMs / (1000 * 60))
  }

  /**
   * 格式化摸鱼时长显示
   * @param {number} durationMs - 时长（毫秒）
   * @returns {string} 格式化的时长字符串
   */
  formatFishingDurationMs(durationMs) {
    const totalSeconds = Math.floor(durationMs / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  /**
   * 获取当前摸鱼时长的格式化字符串
   * @returns {string} 格式化的摸鱼时长
   */
  getCurrentFishingDurationString() {
    const durationMs = this.calculateCurrentFishingDurationMs()
    return this.formatFishingDurationMs(durationMs)
  }

  /**
   * 查找当前时间所在的工作时间段（支持跨日期）
   * @param {Array} segments - 时间段数组
   * @param {number} currentMinutes - 当前时间（分钟）
   * @param {boolean} isFromYesterday - 是否来自昨天的跨日期时间段
   * @returns {Object|null} 工作时间段或null
   */
  findCurrentWorkSegment(segments, currentMinutes, isFromYesterday = false) {
    for (const segment of segments) {
      if (segment.type === 'rest') continue

      // 检查是否为跨日期时间段
      const isCrossDate = segment.start >= 1440 || segment.end >= 1440

      if (isCrossDate && isFromYesterday) {
        // 处理昨天的跨日期时间段，当前时间在今天
        if (segment.start >= 1440) {
          // 整个时间段都在次日（今天）
          const startTimeToday = segment.start - 1440
          const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
          if (currentMinutes >= startTimeToday && currentMinutes <= endTimeToday) {
            return segment
          }
        } else {
          // 时间段跨越两天（如昨天22:00-今天06:00）
          // 当前时间在今天，检查是否在结束时间之前
          const endTimeToday = segment.end >= 1440 ? segment.end - 1440 : segment.end
          if (currentMinutes <= endTimeToday) {
            return segment
          }
        }
      } else if (!isCrossDate && !isFromYesterday) {
        // 普通时间段的处理（今天的时间段）
        if (currentMinutes >= segment.start && currentMinutes <= segment.end) {
          return segment
        }
      }
    }

    return null
  }

  /**
   * 清理资源（应用退出时调用）
   */
  cleanup() {
    this.changeListeners = []
  }

  /**
   * 开始摸鱼
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {Array} segments - 当日工作时间段
   * @param {string} remark - 备注
   * @param {Object} options - 额外选项
   * @returns {Object} 操作结果
   */
  startFishing(workId, date, segments, remark = '', options = {}) {
    try {
      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      // 如果是跨日期时间段，使用传入的实际开始时间
      const actualStartMinutes = options.actualStartMinutes || currentMinutes
      const isFromYesterday = options.isInCrossDateSegment || false

      // 检查是否在工作时间内（支持跨日期时间段）
      const workSegment = this.findCurrentWorkSegment(segments, currentMinutes, isFromYesterday)

      if (!workSegment) {
        return {
          success: false,
          message: '当前不在工作时间内，无法开始摸鱼'
        }
      }

      console.log('[FishingManager] 开始摸鱼:', {
        workId,
        date: formatDateKey(date),
        currentMinutes,
        actualStartMinutes,
        isFromYesterday,
        workSegment: {
          type: workSegment.type,
          start: workSegment.start,
          end: workSegment.end
        }
      })

      // 保存摸鱼状态
      const fishingState = {
        workId: workId,
        date: formatDateKey(date), // 使用统一的本地日期格式化方法
        startTime: now.toISOString(),
        startMinutes: actualStartMinutes, // 使用跨日期时间段中的实际位置
        originalStartMinutes: currentMinutes, // 原始开始时间（今天的时间）
        workSegment: workSegment,
        remark: remark,
        isActive: true,
        isCrossDateSegment: isFromYesterday, // 是否跨日期时间段
        displayDate: isFromYesterday ? formatDateKey(new Date()) : formatDateKey(date) // 显示日期
      }

      this.saveFishingState(fishingState)

      // 同步摸鱼状态到云端
      this.syncFishingStatusToCloud('start', {
        workId: workId,
        startMinutes: currentMinutes,
        workSegment: workSegment,
        remark: remark
      })

      // 通知仪表盘1开始摸鱼
      this.notifyFishingStart()

      return {
        success: true,
        message: '开始摸鱼',
        fishingState: fishingState
      }
    } catch (error) {
      console.error('开始摸鱼失败:', error)
      return {
        success: false,
        message: '开始摸鱼失败: ' + error.message
      }
    }
  }

  /**
   * 结束摸鱼
   * @param {Function} saveFishingRecord - 保存摸鱼记录的回调函数
   * @returns {Object} 操作结果
   */
  endFishing(saveFishingRecord) {
    try {
      const fishingState = this.getFishingState()
      console.log('当前摸鱼状态:', fishingState)

      if (!fishingState || !fishingState.isActive) {
        console.warn('结束摸鱼失败: 当前没有进行中的摸鱼')
        return {
          success: false,
          message: '当前没有进行中的摸鱼'
        }
      }

      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      // 使用统一的时长计算方法
      const durationMinutes = this.calculateCurrentFishingDuration()

      // 计算结束时间在时间段中的位置
      const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)

      console.log(`摸鱼时长计算: 当前实际时间(${currentActualMinutes}) - 开始时间(${fishingState.startMinutes}) = 时长(${durationMinutes}分钟)`)
      console.log(`跨日期摸鱼: ${fishingState.isCrossDateSegment}, 原始当前时间: ${currentMinutes}`)

      // 创建摸鱼记录
      const fishingRecord = {
        id: 0, // 将由调用方设置正确的ID
        start: fishingState.startMinutes,
        end: currentActualMinutes, // 使用计算后的结束时间
        remark: fishingState.remark
      }
      console.log('创建的摸鱼记录:', fishingRecord)

      // 总是允许结束摸鱼，但只有时长>=1分钟的才保存记录
      if (durationMinutes >= 1) {
        // 验证摸鱼记录
        const validation = this.validateFishingData(fishingRecord)
        if (!validation.isValid) {
          console.warn('摸鱼记录无效:', validation.errors)
          return {
            success: false,
            message: '摸鱼记录无效: ' + validation.errors.join(', ')
          }
        }

        // 保存摸鱼记录
        if (saveFishingRecord) {
          console.log('保存摸鱼记录:', fishingRecord)
          saveFishingRecord(fishingState.workId, fishingState.date, fishingRecord)
        }
      } else {
        console.log('摸鱼时长不足1分钟，不保存记录')
      }

      // 清除摸鱼状态
      this.clearFishingState()
      console.log('清除摸鱼状态')

      // 同步摸鱼状态到云端（结束摸鱼）
      this.syncFishingStatusToCloud('end')

      // 通知仪表盘1结束摸鱼
      this.notifyFishingEnd()

      return {
        success: true,
        message: '结束摸鱼',
        fishingRecord: durationMinutes >= 1 ? fishingRecord : null,
        duration: formatDuration(durationMinutes),
        saved: durationMinutes >= 1
      }
    } catch (error) {
      console.error('结束摸鱼失败:', error)
      return {
        success: false,
        message: '结束摸鱼失败: ' + error.message
      }
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知数据变化
   */
  notifyChange() {
    this.changeListeners.forEach(listener => {
      try {
        listener()
      } catch (error) {
        console.error('监听器执行失败:', error)
      }
    })
  }

  /**
   * 调整摸鱼开始时间
   * @param {number} adjustMinutes - 调整的分钟数，正数表示增加（开始时间提前），负数表示减少（开始时间延后）
   * @returns {Object} 调整结果
   */
  adjustFishingStartTime(adjustMinutes) {
    try {
      const fishingState = this.getFishingState()

      if (!fishingState || !fishingState.isActive) {
        return {
          success: false,
          message: '当前没有进行中的摸鱼'
        }
      }

      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      // 计算调整后的开始时间（分钟）
      const newStartMinutes = fishingState.startMinutes - adjustMinutes

      // 计算当前时间在摸鱼时间段中的实际位置
      const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)

      // 边界检查1：调整后的摸鱼时长不能小于1分钟
      const newDurationMinutes = currentActualMinutes - newStartMinutes
      if (newDurationMinutes < 1) {
        return {
          success: false,
          message: '摸鱼时长不能少于1分钟',
          canDecrease: false
        }
      }

      // 边界检查2：调整后的开始时间不能早于工作时间段开始时间
      const workSegment = fishingState.workSegment
      if (newStartMinutes < workSegment.start) {
        return {
          success: false,
          message: '开始时间不能早于工作时间段开始',
          canIncrease: false
        }
      }

      // 边界检查3：调整后的开始时间不能早于上一个摸鱼记录的结束时间
      const previousFishingEndTime = this.getPreviousFishingEndTime(fishingState)
      if (previousFishingEndTime !== null && newStartMinutes < previousFishingEndTime) {
        return {
          success: false,
          message: '开始时间不能早于上一个摸鱼时间段的结束时间',
          canIncrease: false
        }
      }

      // 计算调整后的开始时间（Date对象）
      const originalStartTime = new Date(fishingState.startTime)
      const newStartTime = new Date(originalStartTime.getTime() - adjustMinutes * 60 * 1000)

      // 更新摸鱼状态
      const updatedFishingState = {
        ...fishingState,
        startTime: newStartTime.toISOString(),
        startMinutes: newStartMinutes
      }

      // 保存更新后的状态
      this.saveFishingState(updatedFishingState)

      return {
        success: true,
        message: `摸鱼时间已${adjustMinutes > 0 ? '增加' : '减少'}${Math.abs(adjustMinutes)}分钟`,
        fishingState: updatedFishingState,
        adjustedMinutes: adjustMinutes
      }

    } catch (error) {
      console.error('调整摸鱼时间失败:', error)
      return {
        success: false,
        message: '调整摸鱼时间失败: ' + error.message
      }
    }
  }

  /**
   * 获取上一个摸鱼记录的结束时间
   * @param {Object} currentFishingState - 当前摸鱼状态
   * @returns {number|null} 上一个摸鱼记录的结束时间（分钟），如果没有则返回null
   */
  getPreviousFishingEndTime(currentFishingState) {
    try {
      // 这里需要通过回调获取当天的摸鱼记录，因为fishing-manager不直接访问数据
      // 我们需要在data-manager中提供这个数据
      if (this.getPreviousFishingEndTimeCallback) {
        return this.getPreviousFishingEndTimeCallback(currentFishingState)
      }
      return null
    } catch (error) {
      console.error('获取上一个摸鱼记录结束时间失败:', error)
      return null
    }
  }

  /**
   * 设置获取上一个摸鱼记录结束时间的回调
   * @param {Function} callback - 回调函数
   */
  setPreviousFishingEndTimeCallback(callback) {
    this.getPreviousFishingEndTimeCallback = callback
  }

  /**
   * 检查摸鱼时间调整的边界条件
   * @param {number} adjustMinutes - 要调整的分钟数
   * @returns {Object} 边界检查结果
   */
  checkFishingTimeAdjustment(adjustMinutes) {
    try {
      const fishingState = this.getFishingState()

      if (!fishingState || !fishingState.isActive) {
        return {
          canAdjust: false,
          canIncrease: false,
          canDecrease: false,
          message: '当前没有进行中的摸鱼'
        }
      }

      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const workSegment = fishingState.workSegment

      // 计算当前时间在摸鱼时间段中的实际位置
      const currentActualMinutes = this.calculateActualCurrentMinutes(fishingState, currentMinutes)

      // 检查减少时间的边界（开始时间延后）
      const newStartMinutesForDecrease = fishingState.startMinutes + 1 // 减少1分钟
      const newDurationForDecrease = currentActualMinutes - newStartMinutesForDecrease
      const canDecrease = newDurationForDecrease >= 1

      // 检查增加时间的边界（开始时间提前）
      const newStartMinutesForIncrease = fishingState.startMinutes - 1 // 增加1分钟
      let canIncrease = newStartMinutesForIncrease >= workSegment.start

      // 检查是否会早于上一个摸鱼记录的结束时间
      if (canIncrease) {
        const previousFishingEndTime = this.getPreviousFishingEndTime(fishingState)
        if (previousFishingEndTime !== null && newStartMinutesForIncrease < previousFishingEndTime) {
          canIncrease = false
        }
      }

      return {
        canAdjust: true,
        canIncrease: canIncrease,
        canDecrease: canDecrease,
        currentDuration: currentActualMinutes - fishingState.startMinutes,
        workSegmentStart: workSegment.start
      }

    } catch (error) {
      console.error('检查摸鱼时间调整边界失败:', error)
      return {
        canAdjust: false,
        canIncrease: false,
        canDecrease: false,
        message: '检查失败'
      }
    }
  }

  /**
   * 同步摸鱼状态到云端
   * @param {string} action - 操作类型：'start' 或 'end'
   * @param {Object} data - 摸鱼数据（仅在开始时需要）
   */
  syncFishingStatusToCloud(action, data = {}) {
    const apiMethod = action === 'start' ?
      api.fishing.startFishingStatus :
      api.fishing.endFishingStatus

    return apiMethod(action === 'start' ? data : {}).catch(err => {
      console.warn(`摸鱼状态同步失败，但不影响本地功能: ${action}`, err)
      // 不阻断本地摸鱼功能的正常使用
    })
  }

  /**
   * 通知仪表盘1开始摸鱼
   */
  notifyFishingStart() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      // 查找仪表盘1组件
      if (currentPage && currentPage.selectComponent) {
        const dashboard1 = currentPage.selectComponent('#dashboard1')
        if (dashboard1 && dashboard1.onFishingStart) {
          dashboard1.onFishingStart()
        }
      }
    } catch (error) {
      console.warn('通知仪表盘1开始摸鱼失败:', error)
    }
  }

  /**
   * 通知仪表盘1结束摸鱼
   */
  notifyFishingEnd() {
    try {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      // 查找仪表盘1组件
      if (currentPage && currentPage.selectComponent) {
        const dashboard1 = currentPage.selectComponent('#dashboard1')
        if (dashboard1 && dashboard1.onFishingEnd) {
          dashboard1.onFishingEnd()
        }
      }
    } catch (error) {
      console.warn('通知仪表盘1结束摸鱼失败:', error)
    }
  }

  // ==================== 自动结束管理方法 ====================

  /**
   * 生成摸鱼会话ID
   * @param {Object} fishingState - 摸鱼状态
   * @returns {string} 会话ID
   */
  generateSessionId(fishingState) {
    if (!fishingState) return null

    // 使用工作ID、日期和开始时间生成唯一会话ID
    return `${fishingState.workId}_${fishingState.date}_${fishingState.startMinutes}`
  }

  /**
   * 检查并处理摸鱼自动结束
   * @param {string} reason - 触发原因
   * @param {Object} context - 上下文信息
   */
  checkAndHandleAutoEnd(reason, context = {}) {
    try {
      // 防抖：如果距离上次处理时间太近，则跳过
      const now = Date.now()
      if (now - this.lastProcessTime < this.debounceDelay) {
        console.log('[FishingManager] 防抖跳过，距离上次处理时间太近')
        return
      }

      // 获取当前摸鱼状态
      const fishingState = this.getFishingState()

      if (!fishingState || !fishingState.isActive) {
        console.log('[FishingManager] 当前没有活跃的摸鱼状态')
        return
      }

      // 生成会话ID
      const sessionId = this.generateSessionId(fishingState)
      if (!sessionId) {
        console.log('[FishingManager] 无法生成会话ID')
        return
      }

      // 检查是否已经处理过这个会话
      if (this.processedSessions.has(sessionId)) {
        console.log('[FishingManager] 会话已处理过，跳过:', sessionId)
        return
      }

      // 检查是否正在处理
      if (this.isProcessing) {
        console.log('[FishingManager] 正在处理其他会话，跳过')
        return
      }

      // 判断是否需要自动结束
      if (this.shouldAutoEnd(reason, fishingState, context)) {
        console.log('[FishingManager] 需要自动结束摸鱼:', reason, sessionId)
        this.executeAutoEnd(sessionId, reason, fishingState, context)
      }

    } catch (error) {
      console.error('[FishingManager] 检查自动结束失败:', error)
    }
  }

  /**
   * 判断是否应该自动结束摸鱼
   * @param {string} reason - 触发原因
   * @param {Object} fishingState - 摸鱼状态
   * @param {Object} context - 上下文信息
   * @returns {boolean} 是否应该自动结束
   */
  shouldAutoEnd(reason, fishingState, context) {
    // 根据不同原因判断是否需要自动结束
    switch (reason) {
      case AutoEndReason.TIME_SEGMENT_END:
        return this.checkTimeSegmentEnd(fishingState, context)

      case AutoEndReason.ENTER_REST:
      case AutoEndReason.ENTER_NO_SEGMENT:
      case AutoEndReason.LEAVE_WORK:
        return true // 这些情况都需要自动结束

      case AutoEndReason.MANUAL:
        return true // 手动触发总是执行

      default:
        console.warn('[FishingManager] 未知的自动结束原因:', reason)
        return false
    }
  }

  /**
   * 检查时间段结束的情况
   * @param {Object} fishingState - 摸鱼状态
   * @param {Object} context - 上下文信息
   * @returns {boolean} 是否需要自动结束
   */
  checkTimeSegmentEnd(fishingState, context) {
    const now = context.currentTime || new Date()
    const currentMinutes = now.getHours() * 60 + now.getMinutes()
    const workSegment = fishingState.workSegment

    if (!workSegment) {
      return false
    }

    // 检查是否超出了摸鱼开始时的工作时间段
    return currentMinutes >= workSegment.end
  }

  /**
   * 执行自动结束摸鱼
   * @param {string} sessionId - 会话ID
   * @param {string} reason - 结束原因
   * @param {Object} fishingState - 摸鱼状态
   * @param {Object} context - 上下文信息
   */
  executeAutoEnd(sessionId, reason, fishingState, context) {
    // 设置处理状态
    this.isProcessing = true
    this.currentSessionId = sessionId
    this.lastProcessTime = Date.now()

    // 标记会话已处理
    this.processedSessions.add(sessionId)

    try {
      console.log('[FishingManager] 开始执行自动结束:', sessionId, reason)

      // 调用结束摸鱼方法
      const result = this.endFishing((workId, dateStr, fishingRecord) => {
        // 保存摸鱼记录的回调
        const dataManager = getApp().getDataManager()
        const date = new Date(dateStr)
        dataManager.addFishing(workId, date, fishingRecord)
      })

      if (result.success) {
        // 显示自动结束提示
        const reasonText = this.getAutoEndReasonText(reason)
        wx.showToast({
          title: `摸鱼自动结束：${reasonText}`,
          icon: 'none',
          duration: 3000
        })

        // 通知监听器
        this.notifyAutoEndListeners({
          sessionId,
          reason,
          result,
          fishingState
        })

        console.log('[FishingManager] 自动结束执行成功:', sessionId)
      } else {
        console.warn('[FishingManager] 自动结束执行失败:', result.message)
      }

    } catch (error) {
      console.error('[FishingManager] 执行自动结束失败:', error)
    } finally {
      // 重置处理状态
      this.isProcessing = false
      this.currentSessionId = null
    }
  }

  /**
   * 获取自动结束原因的文本描述
   * @param {string} reason - 原因代码
   * @returns {string} 文本描述
   */
  getAutoEndReasonText(reason) {
    const reasonTexts = {
      [AutoEndReason.TIME_SEGMENT_END]: '工作时间段结束',
      [AutoEndReason.ENTER_REST]: '进入休息时间',
      [AutoEndReason.ENTER_NO_SEGMENT]: '离开工作时间段',
      [AutoEndReason.LEAVE_WORK]: '离开工作状态',
      [AutoEndReason.MANUAL]: '手动触发'
    }

    return reasonTexts[reason] || '未知原因'
  }

  /**
   * 处理时间段状态变化事件
   * @param {Object} eventData - 事件数据
   */
  handleTimeSegmentChange(eventData) {
    const { event, newStatus } = eventData

    console.log('[FishingManager] 收到时间段状态变化:', event, newStatus)

    // 根据不同的时间段变化事件触发相应的自动结束检查
    switch (event) {
      case TimeSegmentChangeEvent.LEAVE_WORK:
        this.checkAndHandleAutoEnd(AutoEndReason.LEAVE_WORK, eventData)
        break

      case TimeSegmentChangeEvent.ENTER_REST:
        this.checkAndHandleAutoEnd(AutoEndReason.ENTER_REST, eventData)
        break

      case TimeSegmentChangeEvent.ENTER_NO_SEGMENT:
        this.checkAndHandleAutoEnd(AutoEndReason.ENTER_NO_SEGMENT, eventData)
        break

      default:
        // 其他事件不需要处理
        break
    }
  }

  /**
   * 处理时间段结束检测（由实时收入计算器调用）
   * @param {Object} context - 上下文信息
   */
  handleTimeSegmentEnd(context) {
    this.checkAndHandleAutoEnd(AutoEndReason.TIME_SEGMENT_END, context)
  }

  /**
   * 添加自动结束监听器
   * @param {Function} listener - 监听器函数
   */
  addAutoEndListener(listener) {
    if (typeof listener === 'function') {
      this.autoEndListeners.push(listener)
    }
  }

  /**
   * 移除自动结束监听器
   * @param {Function} listener - 监听器函数
   */
  removeAutoEndListener(listener) {
    const index = this.autoEndListeners.indexOf(listener)
    if (index > -1) {
      this.autoEndListeners.splice(index, 1)
    }
  }

  /**
   * 通知所有自动结束监听器
   * @param {Object} data - 事件数据
   */
  notifyAutoEndListeners(data) {
    this.autoEndListeners.forEach(listener => {
      try {
        listener(data)
      } catch (error) {
        console.error('[FishingManager] 自动结束监听器执行失败:', error)
      }
    })
  }

  /**
   * 清理已处理的会话（定期清理，避免内存泄漏）
   */
  cleanupProcessedSessions() {
    // 清理超过1小时的会话记录
    if (this.processedSessions.size > 100) {
      this.processedSessions.clear()
      console.log('[FishingManager] 清理已处理的会话记录')
    }
  }

}

// 导出单例实例
let fishingManagerInstance = null

function getFishingManager() {
  if (!fishingManagerInstance) {
    fishingManagerInstance = new FishingManager()
  }
  return fishingManagerInstance
}

module.exports = {
  FishingManager,
  AutoEndReason,
  getFishingManager
}
