/**
 * 时间段状态管理器
 * 负责检测当前时间段状态变化并通知相关组件
 */

// 时间段状态枚举
const TimeSegmentStatus = {
  WORKING: 'working',        // 在工作时间段内
  RESTING: 'resting',        // 在休息时间段内  
  NO_SEGMENT: 'no_segment'   // 不在任何时间段内
}

// 状态变化事件枚举
const TimeSegmentChangeEvent = {
  ENTER_WORK: 'enter_work',      // 进入工作时间段
  LEAVE_WORK: 'leave_work',      // 离开工作时间段
  ENTER_REST: 'enter_rest',      // 进入休息时间段
  LEAVE_REST: 'leave_rest',      // 离开休息时间段
  ENTER_NO_SEGMENT: 'enter_no_segment',  // 进入无时间段状态
  LEAVE_NO_SEGMENT: 'leave_no_segment'   // 离开无时间段状态
}

class TimeSegmentStatusManager {
  constructor() {
    this.currentStatus = TimeSegmentStatus.NO_SEGMENT
    this.currentSegment = null
    this.currentSegmentIndex = -1
    this.listeners = []
    this.checkTimer = null
    this.isRunning = false
    this.lastCheckTime = 0
    this.checkInterval = 1000 // 默认1秒检查一次
  }

  /**
   * 启动状态检测
   * @param {Object} options - 配置选项
   */
  start(options = {}) {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    this.checkInterval = options.checkInterval || 1000

    // 立即执行一次检测
    this.checkStatus()

    // 启动定时检测
    this.scheduleNextCheck()
  }

  /**
   * 停止状态检测
   */
  stop() {
    this.isRunning = false
    if (this.checkTimer) {
      clearTimeout(this.checkTimer)
      this.checkTimer = null
    }
  }

  /**
   * 检查当前时间段状态
   */
  checkStatus() {
    if (!this.isRunning) {
      return
    }

    try {
      const dataManager = getApp().getDataManager()
      const currentWork = dataManager.getCurrentWork()
      
      if (!currentWork) {
        this.updateStatus(TimeSegmentStatus.NO_SEGMENT, null, -1)
        return
      }

      const now = new Date()
      const dayData = dataManager.getDayData(currentWork.id, now)
      
      if (!dayData.segments || dayData.segments.length === 0) {
        this.updateStatus(TimeSegmentStatus.NO_SEGMENT, null, -1)
        return
      }

      // 检查当前时间在哪个时间段内
      const result = this.findCurrentSegment(dayData.segments, now)
      
      if (result.segment) {
        const status = result.segment.type === 'rest' ? 
          TimeSegmentStatus.RESTING : TimeSegmentStatus.WORKING
        this.updateStatus(status, result.segment, result.index)
      } else {
        this.updateStatus(TimeSegmentStatus.NO_SEGMENT, null, -1)
      }

    } catch (error) {
      console.error('[TimeSegmentStatusManager] 检查状态失败:', error)
    }
  }

  /**
   * 查找当前时间所在的时间段
   * @param {Array} segments - 时间段数组
   * @param {Date} currentTime - 当前时间
   * @returns {Object} 查找结果
   */
  findCurrentSegment(segments, currentTime) {
    // 使用秒级精度进行更准确的时间检测
    const currentSeconds = currentTime.getHours() * 3600 + currentTime.getMinutes() * 60 + currentTime.getSeconds()

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]

      // 处理跨日期时间段
      const isCrossDate = segment.start >= 1440 || segment.end >= 1440

      if (isCrossDate) {
        // 跨日期时间段的处理
        if (this.isInCrossDateSegment(segment, currentTime)) {
          return { segment, index: i }
        }
      } else {
        // 普通时间段的处理：使用秒级精确比较
        const segmentStartSeconds = segment.start * 60
        const segmentEndSeconds = segment.end * 60
        if (currentSeconds >= segmentStartSeconds && currentSeconds <= segmentEndSeconds) {
          return { segment, index: i }
        }
      }
    }

    return { segment: null, index: -1 }
  }

  /**
   * 检查当前时间是否在跨日期时间段内
   * @param {Object} segment - 时间段
   * @param {Date} currentTime - 当前时间
   * @returns {boolean} 是否在时间段内
   */
  isInCrossDateSegment(segment, currentTime) {
    // 使用秒级精度进行更准确的时间检测
    const currentSeconds = currentTime.getHours() * 3600 + currentTime.getMinutes() * 60 + currentTime.getSeconds()

    if (segment.start >= 1440) {
      // 整个时间段都在次日
      const segmentStartSeconds = (segment.start - 1440) * 60
      const segmentEndSeconds = (segment.end >= 1440 ? segment.end - 1440 : segment.end) * 60
      return currentSeconds >= segmentStartSeconds && currentSeconds <= segmentEndSeconds
    } else {
      // 时间段跨越两天（如22:00-次日06:00）
      const segmentStartSeconds = segment.start * 60
      const segmentEndSeconds = segment.end * 60
      return currentSeconds >= segmentStartSeconds || currentSeconds <= segmentEndSeconds
    }
  }

  /**
   * 更新状态
   * @param {string} newStatus - 新状态
   * @param {Object} newSegment - 新时间段
   * @param {number} newSegmentIndex - 新时间段索引
   */
  updateStatus(newStatus, newSegment, newSegmentIndex) {
    const oldStatus = this.currentStatus
    const oldSegment = this.currentSegment
    const oldSegmentIndex = this.currentSegmentIndex

    // 检查状态是否发生变化
    const statusChanged = oldStatus !== newStatus
    const segmentChanged = oldSegmentIndex !== newSegmentIndex

    if (statusChanged || segmentChanged) {
      this.currentStatus = newStatus
      this.currentSegment = newSegment
      this.currentSegmentIndex = newSegmentIndex

      // 触发状态变化事件
      this.notifyStatusChange(oldStatus, newStatus, oldSegment, newSegment)
    }

    // 计算下次检查的时间间隔
    this.calculateNextCheckInterval(newSegment)
  }

  /**
   * 通知状态变化
   * @param {string} oldStatus - 旧状态
   * @param {string} newStatus - 新状态
   * @param {Object} oldSegment - 旧时间段
   * @param {Object} newSegment - 新时间段
   */
  notifyStatusChange(oldStatus, newStatus, oldSegment, newSegment) {
    const changeEvent = this.getChangeEvent(oldStatus, newStatus)
    
    if (changeEvent) {
      console.log(`[TimeSegmentStatusManager] 状态变化: ${oldStatus} -> ${newStatus}, 事件: ${changeEvent}`)
      
      const eventData = {
        event: changeEvent,
        oldStatus,
        newStatus,
        oldSegment,
        newSegment,
        currentSegmentIndex: this.currentSegmentIndex
      }

      // 通知所有监听器
      this.listeners.forEach(listener => {
        try {
          listener(eventData)
        } catch (error) {
          console.error('[TimeSegmentStatusManager] 监听器执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取状态变化事件
   * @param {string} oldStatus - 旧状态
   * @param {string} newStatus - 新状态
   * @returns {string|null} 变化事件
   */
  getChangeEvent(oldStatus, newStatus) {
    if (oldStatus === newStatus) {
      return null
    }

    // 进入工作状态
    if (newStatus === TimeSegmentStatus.WORKING) {
      if (oldStatus === TimeSegmentStatus.RESTING) {
        return TimeSegmentChangeEvent.LEAVE_REST
      } else if (oldStatus === TimeSegmentStatus.NO_SEGMENT) {
        return TimeSegmentChangeEvent.LEAVE_NO_SEGMENT
      }
      return TimeSegmentChangeEvent.ENTER_WORK
    }

    // 离开工作状态
    if (oldStatus === TimeSegmentStatus.WORKING) {
      if (newStatus === TimeSegmentStatus.RESTING) {
        return TimeSegmentChangeEvent.ENTER_REST
      } else if (newStatus === TimeSegmentStatus.NO_SEGMENT) {
        return TimeSegmentChangeEvent.ENTER_NO_SEGMENT
      }
      return TimeSegmentChangeEvent.LEAVE_WORK
    }

    // 其他状态变化
    if (newStatus === TimeSegmentStatus.RESTING) {
      return TimeSegmentChangeEvent.ENTER_REST
    } else if (newStatus === TimeSegmentStatus.NO_SEGMENT) {
      return TimeSegmentChangeEvent.ENTER_NO_SEGMENT
    }

    return null
  }

  /**
   * 计算下次检查的时间间隔
   * @param {Object} currentSegment - 当前时间段
   */
  calculateNextCheckInterval(currentSegment) {
    if (!currentSegment) {
      // 不在任何时间段内，使用默认间隔
      this.checkInterval = 5000
      return
    }

    const now = new Date()
    const currentMinutes = now.getHours() * 60 + now.getMinutes()
    
    // 计算到时间段结束还有多少分钟
    let minutesToEnd
    if (currentSegment.start >= 1440) {
      // 跨日期时间段
      const segmentEndMinutes = currentSegment.end >= 1440 ? currentSegment.end - 1440 : currentSegment.end
      minutesToEnd = segmentEndMinutes - currentMinutes
    } else {
      minutesToEnd = currentSegment.end - currentMinutes
    }

    if (minutesToEnd <= 1) {
      // 即将结束，使用高频检查
      this.checkInterval = 1000
    } else if (minutesToEnd <= 5) {
      // 5分钟内结束，使用中频检查
      this.checkInterval = 10000
    } else {
      // 还有较长时间，使用低频检查
      this.checkInterval = 30000
    }
  }

  /**
   * 安排下次检查
   */
  scheduleNextCheck() {
    if (!this.isRunning) {
      return
    }

    this.checkTimer = setTimeout(() => {
      this.checkStatus()
      this.scheduleNextCheck()
    }, this.checkInterval)
  }

  /**
   * 添加状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    if (typeof listener === 'function') {
      this.listeners.push(listener)
    }
  }

  /**
   * 移除状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态信息
   */
  getCurrentStatus() {
    return {
      status: this.currentStatus,
      segment: this.currentSegment,
      segmentIndex: this.currentSegmentIndex,
      isWorking: this.currentStatus === TimeSegmentStatus.WORKING,
      isResting: this.currentStatus === TimeSegmentStatus.RESTING,
      hasSegment: this.currentStatus !== TimeSegmentStatus.NO_SEGMENT
    }
  }
}

// 导出单例实例
let timeSegmentStatusManagerInstance = null

function getTimeSegmentStatusManager() {
  if (!timeSegmentStatusManagerInstance) {
    timeSegmentStatusManagerInstance = new TimeSegmentStatusManager()
  }
  return timeSegmentStatusManagerInstance
}

module.exports = {
  TimeSegmentStatusManager,
  TimeSegmentStatus,
  TimeSegmentChangeEvent,
  getTimeSegmentStatusManager
}
