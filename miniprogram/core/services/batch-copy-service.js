/**
 * 批量复制服务
 * 专门处理日期安排的批量复制逻辑
 */
class BatchCopyService {
  constructor(timeSegmentService) {
    this.timeSegmentService = timeSegmentService
  }

  /**
   * 选择性复制日期数据
   * 注意：复制操作会完全清空目标日期的摸鱼记录和收入调整数据
   * @param {Date} sourceDate - 源日期
   * @param {Array<Date>} targetDates - 目标日期数组
   * @param {Object} options - 复制选项
   * @param {boolean} options.copyStatus - 是否复制状态
   * @param {boolean} options.copySegments - 是否复制时间段
   * @param {string} workId - 工作履历ID
   * @returns {Object} 复制结果
   */
  selectiveCopy(sourceDate, targetDates, options, workId) {
    try {
      const { copyStatus = true, copySegments = true } = options

      // 验证复制选项
      const validation = this.validateCopyOptions(sourceDate, options, workId)
      if (!validation.valid) {
        throw new Error(validation.message)
      }

      // 获取源数据
      const sourceData = this.timeSegmentService.getDayData(sourceDate, workId)
      const sourceStatus = this.timeSegmentService.getDateStatus(sourceDate, workId)

      // 检查源数据
      const hasSegments = sourceData?.segments?.length > 0
      const hasStatus = !!sourceStatus

      if (!hasSegments && !hasStatus) {
        throw new Error('源日期没有可复制的数据')
      }

      // 检查数据冲突
      const conflicts = this.checkDataConflicts(targetDates, workId)

      let successCount = 0
      let failCount = 0
      const results = []

      // 批量复制到目标日期
      targetDates.forEach(targetDate => {
        try {
          this._copyToSingleDate(sourceData, sourceStatus, targetDate, {
            copyStatus,
            copySegments,
            hasSegments,
            hasStatus
          }, workId)
          
          successCount++
          results.push({
            date: targetDate,
            success: true
          })
        } catch (error) {
          failCount++
          results.push({
            date: targetDate,
            success: false,
            error: error.message
          })
          console.error(`复制到 ${targetDate.toDateString()} 失败:`, error)
        }
      })

      return {
        success: successCount > 0,
        successCount,
        failCount,
        totalCount: targetDates.length,
        conflicts,
        results,
        copyType: this._getCopyTypeDescription(hasSegments, hasStatus, copyStatus, copySegments)
      }
    } catch (error) {
      console.error('批量复制失败:', error)
      throw error
    }
  }

  /**
   * 复制到单个日期
   * @private
   */
  _copyToSingleDate(sourceData, sourceStatus, targetDate, options, workId) {
    const { copyStatus, copySegments } = options

    // 构建新的数据对象，使用源数据完全覆盖目标数据
    const newData = {
      // 摸鱼记录和收入调整始终清空（无论源日期和目标日期原来是什么）
      fishes: [],
      fishingRecords: [],
      extraIncomes: [],
      extraIncomeItems: [],
      deductions: [],
      deductionItems: [],

      // 时间段：如果选择复制，则使用源数据（包括空数组）；否则清空
      segments: copySegments ? this._copySegments(sourceData.segments || []) : [],

      // 状态：如果选择复制，则使用源状态（包括null）；否则清空
      status: copyStatus ? sourceStatus : null,

      // 更新时间
      updateTime: new Date()
    }

    // 重新计算日收入（基于新的时间段）
    newData.dailyIncome = this.timeSegmentService.calculateDailyIncome(newData.segments)

    // 保存完整的数据结构
    this.timeSegmentService.saveDayData(targetDate, newData, workId)

    // 如果需要复制状态，确保状态正确设置
    if (copyStatus && sourceStatus) {
      this.timeSegmentService.setDateStatus(targetDate, sourceStatus, workId)
    }
  }

  /**
   * 复制时间段数据（深拷贝）
   * @private
   */
  _copySegments(segments) {
    if (!segments || !Array.isArray(segments)) return []
    
    return segments.map((segment, index) => ({
      id: index, // 重新分配ID
      start: segment.start,
      end: segment.end,
      type: segment.type,
      income: segment.income || 0
    }))
  }

  /**
   * 验证复制选项
   */
  validateCopyOptions(sourceDate, options, workId) {
    const { copyStatus = true, copySegments = true } = options

    // 至少要选择一个复制选项
    if (!copyStatus && !copySegments) {
      return {
        valid: false,
        message: '请至少选择一个复制选项'
      }
    }

    // 检查源数据
    const sourceData = this.timeSegmentService.getDayData(sourceDate, workId)
    const sourceStatus = this.timeSegmentService.getDateStatus(sourceDate, workId)
    
    const hasSegments = sourceData?.segments?.length > 0
    const hasStatus = !!sourceStatus

    // 如果选择复制时间段但源没有时间段
    if (copySegments && !hasSegments && !hasStatus) {
      return {
        valid: false,
        message: '源日期没有时间段数据可复制'
      }
    }

    // 如果选择复制状态但源没有状态
    if (copyStatus && !hasStatus && !hasSegments) {
      return {
        valid: false,
        message: '源日期没有状态数据可复制'
      }
    }

    return { valid: true }
  }

  /**
   * 检查数据冲突
   */
  checkDataConflicts(targetDates, workId) {
    const conflicts = []
    
    targetDates.forEach(date => {
      const dayData = this.timeSegmentService.getDayData(date, workId)
      
      // 检查是否有现有数据
      const hasSegments = dayData?.segments?.length > 0
      const hasStatus = !!dayData?.status
      const hasFishes = dayData?.fishes?.length > 0 || dayData?.fishingRecords?.length > 0
      const hasExtraData = (dayData?.extraIncomes?.length > 0) || 
                          (dayData?.extraIncomeItems?.length > 0) ||
                          (dayData?.deductions?.length > 0) ||
                          (dayData?.deductionItems?.length > 0)
      
      if (hasSegments || hasStatus || hasFishes || hasExtraData) {
        conflicts.push({
          date,
          hasSegments,
          hasStatus,
          hasFishes,
          hasExtraData
        })
      }
    })

    return {
      hasConflicts: conflicts.length > 0,
      conflictCount: conflicts.length,
      conflicts
    }
  }

  /**
   * 获取复制类型描述
   * @private
   */
  _getCopyTypeDescription(hasSegments, hasStatus, copyStatus, copySegments) {
    const parts = []
    
    if (copySegments && hasSegments) {
      parts.push('时间段安排')
    }
    
    if (copyStatus && hasStatus) {
      parts.push('日期状态')
    }
    
    return parts.join('和') || '数据'
  }

  /**
   * 获取可复制的数据类型
   */
  getAvailableCopyTypes(sourceDate, workId) {
    const sourceData = this.timeSegmentService.getDayData(sourceDate, workId)
    const sourceStatus = this.timeSegmentService.getDateStatus(sourceDate, workId)
    
    return {
      hasSegments: sourceData?.segments?.length > 0,
      hasStatus: !!sourceStatus,
      segmentCount: sourceData?.segments?.length || 0,
      statusText: sourceStatus ? this.timeSegmentService.getDateStatusConfig(sourceStatus)?.name : null
    }
  }
}

module.exports = BatchCopyService
