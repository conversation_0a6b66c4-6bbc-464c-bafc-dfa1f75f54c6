/**
 * 数据导入导出服务
 * 负责处理数据的导入导出业务逻辑
 */

const { DataValidator } = require('../../utils/validators/data-validator.js')
const { DataFormatter } = require('../../utils/formatters/data-formatter.js')

class DataImportExportService {
  constructor() {
    // 延迟初始化数据管理器，避免循环依赖
    this._dataManager = null
  }

  /**
   * 获取数据管理器实例（延迟初始化）
   * @returns {DataManager} 数据管理器实例
   */
  get dataManager() {
    if (!this._dataManager) {
      this._dataManager = getApp().getDataManager()
    }
    return this._dataManager
  }

  /**
   * 初始化服务
   * @param {Object} dataManager - 数据管理器实例
   */
  initialize(dataManager) {
    this.dataManager
  }

  /**
   * 导出数据
   * @returns {Object} 导出结果
   */
  exportData() {
    try {
      const exportData = this.dataManager.exportData()
      // 简单计算数据大小（字符长度）
      const dataSize = exportData.length

      return {
        success: true,
        data: exportData,
        size: dataSize,
        formattedSize: DataFormatter.formatFileSize(dataSize),
        message: '数据导出成功'
      }
    } catch (error) {
      console.error('[DataImportExportService] 导出数据失败:', error)
      return {
        success: false,
        error: error.message,
        message: '数据导出失败'
      }
    }
  }

  /**
   * 验证导入数据格式
   * @param {string} jsonData - JSON格式的数据字符串
   * @returns {Object} 验证结果
   */
  validateImportData(jsonData) {
    try {
      // 基础格式验证
      if (!DataValidator.isNonEmptyString(jsonData)) {
        return {
          isValid: false,
          error: '数据不能为空'
        }
      }

      // JSON格式验证
      let parsedData
      try {
        parsedData = JSON.parse(jsonData)
      } catch (parseError) {
        return {
          isValid: false,
          error: '数据格式不是有效的JSON'
        }
      }

      // 数据结构验证
      if (!parsedData.userData) {
        return {
          isValid: false,
          error: '缺少必要的用户数据字段'
        }
      }

      // 版本兼容性检查
      const currentVersion = this.dataManager?.userData?.version || '0.1.0'
      const importVersion = parsedData.version || '0.1.0'
      
      // 数据统计
      const stats = this.analyzeImportData(parsedData.userData)

      return {
        isValid: true,
        version: importVersion,
        currentVersion: currentVersion,
        stats: stats,
        message: '数据格式验证通过'
      }
    } catch (error) {
      console.error('[DataImportExportService] 验证导入数据失败:', error)
      return {
        isValid: false,
        error: '数据验证过程中发生错误: ' + error.message
      }
    }
  }

  /**
   * 分析导入数据统计信息
   * @param {Object} userData - 用户数据
   * @returns {Object} 统计信息
   */
  analyzeImportData(userData) {
    const stats = {
      workHistoryCount: 0,
      totalDaysWithData: 0,
      timeSegmentCount: 0,
      fishingRecordCount: 0,
      settingsCount: 0
    }

    try {
      // 统计工作履历 - 修复字段名称
      if (userData.workHistory) {
        stats.workHistoryCount = Object.keys(userData.workHistory).length

        // 统计时间追踪数据
        Object.values(userData.workHistory).forEach(work => {
          if (work.timeTracking) {
            const dates = Object.keys(work.timeTracking)
            stats.totalDaysWithData += dates.length

            dates.forEach(date => {
              const dayData = work.timeTracking[date]
              if (dayData.segments) {
                stats.timeSegmentCount += dayData.segments.length
              }
              if (dayData.fishes) {
                stats.fishingRecordCount += dayData.fishes.length
              }
            })
          }
        })
      }

      // 统计设置
      if (userData.settings) {
        stats.settingsCount = Object.keys(userData.settings).length
      }

    } catch (error) {
      console.error('[DataImportExportService] 分析导入数据失败:', error)
    }

    return stats
  }

  /**
   * 导入数据
   * @param {string} jsonData - JSON格式的数据字符串
   * @returns {Object} 导入结果
   */
  importData(jsonData) {
    try {
      // 验证数据格式
      const validation = this.validateImportData(jsonData)
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
          message: '数据格式验证失败'
        }
      }

      // 执行导入
      this.dataManager.importData(jsonData)

      return {
        success: true,
        stats: validation.stats,
        version: validation.version,
        message: '数据导入成功'
      }
    } catch (error) {
      console.error('[DataImportExportService] 导入数据失败:', error)
      return {
        success: false,
        error: error.message,
        message: '数据导入失败'
      }
    }
  }

  /**
   * 获取导入导出统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    try {
      const userData = this.dataManager.getUserData()
      const stats = this.analyzeImportData(userData)

      return {
        success: true,
        stats: stats,
        lastExportTime: userData.lastExportTime || null,
        lastImportTime: userData.lastImportTime || null
      }
    } catch (error) {
      console.error('[DataImportExportService] 获取统计信息失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 创建数据备份
   * @param {string} backupName - 备份名称
   * @returns {Object} 备份结果
   */
  createBackup(backupName = null) {
    try {
      const timestamp = new Date().toISOString()
      const name = backupName || `backup_${timestamp.replace(/[:.]/g, '-')}`
      
      const exportResult = this.exportData()
      if (!exportResult.success) {
        return exportResult
      }

      return {
        success: true,
        backupName: name,
        data: exportResult.data,
        size: exportResult.size,
        formattedSize: exportResult.formattedSize,
        timestamp: timestamp,
        message: '备份创建成功'
      }
    } catch (error) {
      console.error('[DataImportExportService] 创建备份失败:', error)
      return {
        success: false,
        error: error.message,
        message: '备份创建失败'
      }
    }
  }
}

// 导出单例实例
let dataImportExportServiceInstance = null

function getDataImportExportService() {
  if (!dataImportExportServiceInstance) {
    dataImportExportServiceInstance = new DataImportExportService()
  }
  return dataImportExportServiceInstance
}

module.exports = {
  DataImportExportService,
  getDataImportExportService
}
