/**
 * 收入调整服务
 * 负责额外收入和扣款的管理
 * 
 * 功能特性：
 * - 额外收入的增删改查
 * - 扣款的增删改查
 * - 收入调整统计
 * - 类型管理
 */

/**
 * 收入调整服务类
 */
class IncomeAdjustmentService {
  constructor() {
    // 获取数据管理器实例
    this.dataManager = getApp().getDataManager()
  }

  // ==================== 类型处理 ====================

  /**
   * 获取类型文本（现在直接返回用户输入的类型）
   * @param {string} type - 用户输入的类型
   * @param {string} category - 类别（'income' 或 'deduction'）
   * @returns {string} 类型文本
   */
  getTypeText(type, category) {
    // 现在类型是用户自定义输入，直接返回
    return type || '未设置类型'
  }

  // ==================== 辅助方法 ====================

  /**
   * 修复浮点数精度问题
   * @param {number} num - 数字
   * @param {number} decimals - 小数位数，默认2位
   * @returns {number} 修复精度后的数字
   */
  fixFloatPrecision(num, decimals = 2) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
  }

  /**
   * 安全地获取日期对象
   * @param {any} dateValue - 日期值
   * @returns {Date} 有效的日期对象
   */
  getSafeDate(dateValue) {
    try {

      // 如果没有值，返回当前日期
      if (!dateValue) {
        console.warn('收入调整服务：日期值为空，使用当前日期')
        return new Date()
      }

      // 如果已经是 Date 对象
      if (dateValue instanceof Date) {
        return isNaN(dateValue.getTime()) ? new Date() : dateValue
      }

      // 如果是字符串
      if (typeof dateValue === 'string') {
        const parsed = new Date(dateValue)
        return isNaN(parsed.getTime()) ? new Date() : parsed
      }

      // 如果是数字（时间戳）
      if (typeof dateValue === 'number') {
        const parsed = new Date(dateValue)
        return isNaN(parsed.getTime()) ? new Date() : parsed
      }

      // 如果是对象，尝试转换
      if (typeof dateValue === 'object') {
        // 可能是序列化的日期对象
        if (dateValue.getTime) {
          try {
            return new Date(dateValue.getTime())
          } catch (e) {
            // 忽略错误，继续下面的处理
          }
        }

        // 尝试直接转换
        const parsed = new Date(dateValue)
        return isNaN(parsed.getTime()) ? new Date() : parsed
      }

      // 其他情况返回当前日期
      console.warn('收入调整服务：无法处理日期，使用当前日期')
      return new Date()

    } catch (error) {
      console.error('日期转换失败:', error)
      return new Date()
    }
  }

  // ==================== 额外收入管理 ====================

  /**
   * 添加额外收入
   * @param {Date} date - 日期
   * @param {string} type - 收入类型
   * @param {number} amount - 金额
   * @param {string} description - 描述
   * @param {string} category - 分类（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {number} 新添加的收入项目ID
   */
  addExtraIncome(date, type, amount, description, category = '', workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)
      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保额外收入数组存在
      if (!dayData.extraIncomes || !Array.isArray(dayData.extraIncomes)) {
        dayData.extraIncomes = []
      }

      // 生成下一个可用ID
      const id = this.dataManager.timeTrackingManager.generateNextId(dayData.extraIncomes)

      // 创建额外收入项目
      const incomeItem = this.dataManager.timeTrackingManager.createExtraIncomeItem(
        id, type, amount, description
      )


      // 验证数据
      const validation = this.dataManager.timeTrackingManager.validateExtraIncomeItem(incomeItem)
      if (!validation.isValid) {
        throw new Error('额外收入数据验证失败: ' + validation.errors.join(', '))
      }

      // 添加到数组
      dayData.extraIncomes.push(incomeItem)

      // 更新统计信息
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)

      // 清除统计缓存
      const { StatisticsService } = require('./statistics-service.js')
      const statisticsService = new StatisticsService()
      statisticsService.clearStatisticsCache()

      return id

    } catch (error) {
      console.error('添加额外收入失败:', error)
      throw new Error('添加额外收入失败: ' + error.message)
    }
  }

  /**
   * 删除额外收入
   * @param {Date} date - 日期
   * @param {number} itemId - 收入项目ID
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  removeExtraIncome(date, itemId, workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保额外收入数组存在
      if (!dayData.extraIncomes || !Array.isArray(dayData.extraIncomes)) {
        dayData.extraIncomes = []
      }

      // 通过ID查找并删除项目
      const itemIndex = dayData.extraIncomes.findIndex(item => item.id === itemId)
      if (itemIndex === -1) {
        console.error('未找到要删除的额外收入项目，ID:', itemId, '现有项目:', dayData.extraIncomes)
        throw new Error('额外收入项目不存在')
      }

      dayData.extraIncomes.splice(itemIndex, 1)

      // 更新统计信息
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)


    } catch (error) {
      console.error('删除额外收入失败:', error)
      throw new Error('删除额外收入失败: ' + error.message)
    }
  }

  /**
   * 编辑额外收入
   * @param {Date} date - 日期
   * @param {Object} originalItem - 原始项目数据（用于匹配）
   * @param {string} type - 收入类型
   * @param {number} amount - 金额
   * @param {string} description - 描述
   * @param {string} category - 分类（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {boolean} 是否编辑成功
   */
  editExtraIncome(date, originalItem, type, amount, description, category = '', workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保额外收入数组存在
      if (!dayData.extraIncomes || !Array.isArray(dayData.extraIncomes)) {
        dayData.extraIncomes = []
      }

      // 验证原始项目数据
      if (!originalItem || typeof originalItem.id !== 'number') {
        console.error('原始项目数据验证失败:', originalItem)
        throw new Error('原始项目数据无效')
      }

      // 通过ID查找项目在数组中的索引
      const itemIndex = dayData.extraIncomes.findIndex(item => item.id === originalItem.id)
      if (itemIndex === -1) {
        console.error('未找到额外收入记录，ID:', originalItem.id, '现有项目:', dayData.extraIncomes)
        throw new Error('没有找到指定的额外收入记录')
      }


      // 创建更新后的项目数据
      const updatedItem = {
        id: originalItem.id,
        type: type,
        desc: description || '',
        amount: amount
      }

      // 验证数据
      const validation = this.dataManager.timeTrackingManager.validateExtraIncomeItem(updatedItem)
      if (!validation.isValid) {
        throw new Error('额外收入数据验证失败: ' + validation.errors.join(', '))
      }

      // 更新数组中的项目
      dayData.extraIncomes[itemIndex] = updatedItem

      // 重新计算汇总
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)

      // 清除统计缓存
      const { StatisticsService } = require('./statistics-service.js')
      const statisticsService = new StatisticsService()
      statisticsService.clearStatisticsCache()

      return true

    } catch (error) {
      console.error('编辑额外收入失败:', error)
      throw new Error('编辑额外收入失败: ' + error.message)
    }
  }

  // ==================== 扣款管理 ====================

  /**
   * 添加扣款
   * @param {Date} date - 日期
   * @param {string} type - 扣款类型
   * @param {number} amount - 金额
   * @param {string} description - 描述
   * @param {string} category - 分类（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {number} 新添加的扣款项目ID
   */
  addDeduction(date, type, amount, description, category = '', workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保扣款数组存在
      if (!dayData.deductions || !Array.isArray(dayData.deductions)) {
        dayData.deductions = []
      }

      // 生成下一个可用ID
      const id = this.dataManager.timeTrackingManager.generateNextId(dayData.deductions)

      // 创建扣款项目
      const deductionItem = this.dataManager.timeTrackingManager.createDeductionItem(
        id, type, amount, description
      )


      // 验证数据
      const validation = this.dataManager.timeTrackingManager.validateDeductionItem(deductionItem)
      if (!validation.isValid) {
        throw new Error('扣款数据验证失败: ' + validation.errors.join(', '))
      }

      // 添加到数组
      dayData.deductions.push(deductionItem)

      // 更新统计信息
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)

      // 清除统计缓存
      const { StatisticsService } = require('./statistics-service.js')
      const statisticsService = new StatisticsService()
      statisticsService.clearStatisticsCache()

      return id

    } catch (error) {
      console.error('添加扣款失败:', error)
      throw new Error('添加扣款失败: ' + error.message)
    }
  }

  /**
   * 删除扣款
   * @param {Date} date - 日期
   * @param {number} itemId - 扣款项目ID
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   */
  removeDeduction(date, itemId, workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保扣款数组存在
      if (!dayData.deductions || !Array.isArray(dayData.deductions)) {
        dayData.deductions = []
      }

      // 通过ID查找并删除项目
      const itemIndex = dayData.deductions.findIndex(item => item.id === itemId)
      if (itemIndex === -1) {
        console.error('未找到要删除的扣款项目，ID:', itemId, '现有项目:', dayData.deductions)
        throw new Error('扣款项目不存在')
      }

      dayData.deductions.splice(itemIndex, 1)

      // 更新统计信息
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)


    } catch (error) {
      console.error('删除扣款失败:', error)
      throw new Error('删除扣款失败: ' + error.message)
    }
  }

  /**
   * 编辑扣款
   * @param {Date} date - 日期
   * @param {Object} originalItem - 原始项目数据（用于匹配）
   * @param {string} type - 扣款类型
   * @param {number} amount - 金额
   * @param {string} description - 描述
   * @param {string} category - 分类（可选）
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {boolean} 是否编辑成功
   */
  editDeduction(date, originalItem, type, amount, description, category = '', workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      // 如果没有指定工作履历ID，使用当前工作
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          throw new Error('没有可用的工作履历ID')
        }
        workId = currentWork.id
      }

      // 获取日期数据
      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 确保扣款数组存在
      if (!dayData.deductions || !Array.isArray(dayData.deductions)) {
        dayData.deductions = []
      }

      // 验证原始项目数据
      if (!originalItem || typeof originalItem.id !== 'number') {
        console.error('原始项目数据验证失败:', originalItem)
        throw new Error('原始项目数据无效')
      }

      // 通过ID查找项目在数组中的索引
      const itemIndex = dayData.deductions.findIndex(item => item.id === originalItem.id)
      if (itemIndex === -1) {
        console.error('未找到扣款记录，ID:', originalItem.id, '现有项目:', dayData.deductions)
        throw new Error('没有找到指定的扣款记录')
      }


      // 创建更新后的项目数据
      const updatedItem = {
        id: originalItem.id,
        type: type,
        desc: description || '',
        amount: amount
      }

      // 验证数据
      const validation = this.dataManager.timeTrackingManager.validateDeductionItem(updatedItem)
      if (!validation.isValid) {
        throw new Error('扣款数据验证失败: ' + validation.errors.join(', '))
      }

      // 更新数组中的项目
      dayData.deductions[itemIndex] = updatedItem

      // 重新计算汇总
      this.dataManager.timeTrackingManager.updateDayDataStats(dayData)

      // 保存数据
      this.dataManager.saveDayData(workId, safeDate, dayData)

      // 清除统计缓存
      const { StatisticsService } = require('./statistics-service.js')
      const statisticsService = new StatisticsService()
      statisticsService.clearStatisticsCache()

      return true

    } catch (error) {
      console.error('编辑扣款失败:', error)
      throw new Error('编辑扣款失败: ' + error.message)
    }
  }

  // ==================== 查询方法 ====================

  /**
   * 获取指定日期的额外收入列表
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Array} 额外收入列表
   */
  getExtraIncomeItems(date, workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          return []
        }
        workId = currentWork.id
      }

      const dayData = this.dataManager.getDayData(workId, safeDate)
      return dayData.extraIncomes || []

    } catch (error) {
      console.error('获取额外收入列表失败:', error)
      return []
    }
  }

  /**
   * 获取指定日期的扣款列表
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Array} 扣款列表
   */
  getDeductionItems(date, workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          return []
        }
        workId = currentWork.id
      }

      const dayData = this.dataManager.getDayData(workId, safeDate)
      return dayData.deductions || []

    } catch (error) {
      console.error('获取扣款列表失败:', error)
      return []
    }
  }

  // ==================== 统计方法 ====================

  /**
   * 计算指定日期范围内的收入调整统计
   * @param {Array} dateRange - 日期范围 [startDate, endDate]
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 统计结果
   */
  calculateAdjustmentStatistics(dateRange = null, workId = null) {
    try {
      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          return {
            totalExtraIncome: 0,
            totalDeductions: 0,
            netAdjustment: 0,
            extraIncomeByType: {},
            deductionsByType: {},
            itemCount: { extraIncome: 0, deductions: 0 }
          }
        }
        workId = currentWork.id
      }

      const allData = this.dataManager.getTimeTracking(workId)
      const stats = {
        totalExtraIncome: 0,
        totalDeductions: 0,
        netAdjustment: 0,
        extraIncomeByType: {},
        deductionsByType: {},
        itemCount: { extraIncome: 0, deductions: 0 }
      }

      // 遍历所有日期数据
      Object.values(allData).forEach(dayData => {
        // 如果指定了日期范围，检查是否在范围内
        if (dateRange && !this.isDateInRange(dayData.workDate, dateRange)) {
          return
        }

        // 统计额外收入
        if (dayData.extraIncomes && Array.isArray(dayData.extraIncomes)) {
          dayData.extraIncomes.forEach(item => {
            stats.totalExtraIncome += item.amount || 0
            stats.itemCount.extraIncome++

            // 按类型统计
            if (!stats.extraIncomeByType[item.type]) {
              stats.extraIncomeByType[item.type] = {
                amount: 0,
                count: 0,
                typeName: item.type || '未设置类型'  // 直接使用用户输入的类型
              }
            }
            stats.extraIncomeByType[item.type].amount += item.amount || 0
            stats.extraIncomeByType[item.type].count++
          })
        }

        // 统计扣款
        if (dayData.deductions && Array.isArray(dayData.deductions)) {
          dayData.deductions.forEach(item => {
            stats.totalDeductions += item.amount || 0
            stats.itemCount.deductions++

            // 按类型统计
            if (!stats.deductionsByType[item.type]) {
              stats.deductionsByType[item.type] = {
                amount: 0,
                count: 0,
                typeName: item.type || '未设置类型'  // 直接使用用户输入的类型
              }
            }
            stats.deductionsByType[item.type].amount += item.amount || 0
            stats.deductionsByType[item.type].count++
          })
        }
      })

      // 修复精度并计算净调整
      stats.totalExtraIncome = this.fixFloatPrecision(stats.totalExtraIncome)
      stats.totalDeductions = this.fixFloatPrecision(stats.totalDeductions)
      stats.netAdjustment = this.fixFloatPrecision(stats.totalExtraIncome - stats.totalDeductions)

      // 修复按类型统计的精度
      Object.values(stats.extraIncomeByType).forEach(typeStats => {
        typeStats.amount = this.fixFloatPrecision(typeStats.amount)
      })
      Object.values(stats.deductionsByType).forEach(typeStats => {
        typeStats.amount = this.fixFloatPrecision(typeStats.amount)
      })

      return stats

    } catch (error) {
      console.error('计算收入调整统计失败:', error)
      return {
        totalExtraIncome: 0,
        totalDeductions: 0,
        netAdjustment: 0,
        extraIncomeByType: {},
        deductionsByType: {},
        itemCount: { extraIncome: 0, deductions: 0 }
      }
    }
  }

  /**
   * 检查日期是否在指定范围内
   * @param {Date} date - 要检查的日期
   * @param {Array} dateRange - 日期范围 [startDate, endDate]
   * @returns {boolean} 是否在范围内
   */
  isDateInRange(date, dateRange) {
    if (!dateRange || dateRange.length !== 2) {
      return true
    }

    const checkDate = new Date(date)
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])

    return checkDate >= startDate && checkDate < endDate
  }

  /**
   * 获取指定日期的收入调整汇总
   * @param {Date} date - 日期
   * @param {string} workId - 工作履历ID，为空则使用当前工作
   * @returns {Object} 收入调整汇总
   */
  getDayAdjustmentSummary(date, workId = null) {
    try {
      // 安全处理日期参数
      const safeDate = this.getSafeDate(date)

      if (!workId) {
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          return {
            extraIncome: 0,
            deductions: 0,
            netAdjustment: 0,
            extraIncomeItems: [],
            deductionItems: []
          }
        }
        workId = currentWork.id
      }

      const dayData = this.dataManager.getDayData(workId, safeDate)

      // 获取额外收入项目数组
      const extraIncomeItems = dayData.extraIncomes || []

      // 获取扣款项目数组
      const deductionItems = dayData.deductions || []
      // 计算总额
      const extraIncome = this.fixFloatPrecision(
        extraIncomeItems.reduce((total, item) => total + (Number(item.amount) || 0), 0)
      )
      const deductionsTotal = this.fixFloatPrecision(
        deductionItems.reduce((total, item) => total + (Number(item.amount) || 0), 0)
      )
      const netAdjustment = this.fixFloatPrecision(extraIncome - deductionsTotal)
      return {
        extraIncome: extraIncome,
        deductions: deductionsTotal,
        netAdjustment: netAdjustment,
        extraIncomeItems: extraIncomeItems,
        deductionItems: deductionItems
      }

    } catch (error) {
      console.error('获取日期收入调整汇总失败:', error)
      return {
        extraIncome: 0,
        deductions: 0,
        netAdjustment: 0,
        extraIncomeItems: [],
        deductionItems: []
      }
    }
  }
}

// 导出单例实例
let incomeAdjustmentServiceInstance = null

function getIncomeAdjustmentService() {
  if (!incomeAdjustmentServiceInstance) {
    incomeAdjustmentServiceInstance = new IncomeAdjustmentService()
  }
  return incomeAdjustmentServiceInstance
}

module.exports = {
  IncomeAdjustmentService,
  getIncomeAdjustmentService
}
