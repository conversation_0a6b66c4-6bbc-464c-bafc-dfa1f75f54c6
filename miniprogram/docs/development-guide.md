# 开发指南

## 项目概述

这是一个全新的微信小程序项目，专注于工作时间追踪和收入管理。项目采用现代化的模块化架构，具有清晰的分层设计和完善的工具类库。

**重要说明**: 这是一个新项目，没有历史用户数据，因此在开发新功能时**不需要考虑数据兼容性和迁移处理**。

## 项目架构

### 分层架构
```
Pages/Components (页面/组件层)
    ↓
Services (业务服务层)
    ↓
Managers (数据管理层)
    ↓
Utils (工具类层)
```

### 目录结构
```
miniprogram/
├── core/                    # 核心业务模块
│   ├── api/                # API客户端模块
│   │   ├── base.js         # 基础API客户端
│   │   ├── enhanced.js     # 增强API客户端
│   │   ├── modules/        # API模块（用户、签到、积分、商店等）
│   │   └── utils/          # API工具（缓存、重试）
│   ├── managers/           # 数据管理器
│   │   ├── data-manager.js        # 主数据管理器
│   │   ├── storage-manager.js     # 存储管理
│   │   ├── work-manager.js        # 工作履历管理
│   │   ├── user-manager.js        # 用户管理
│   │   ├── sync-manager.js        # 同步管理
│   │   └── fishing-manager.js     # 摸鱼管理
│   └── services/           # 业务服务
│       ├── dashboard-service.js   # 仪表盘服务
│       ├── work-history-service.js # 工作履历服务
│       ├── time-segment-service.js # 时间段服务
│       └── statistics-service.js  # 统计分析服务
├── utils/                   # 工具类模块
│   ├── validators/         # 验证器
│   ├── formatters/         # 格式化器
│   └── helpers/            # 辅助工具
├── pages/                   # 页面
├── components/              # 组件
│   ├── dashboard-switcher/  # 仪表盘切换器（共用组件）
│   ├── dashboard1/          # 经典仪表盘（自主导航栏）
│   ├── dashboard2/          # 现代仪表盘（自主导航栏）
│   ├── fishing-control/     # 摸鱼控制组件
│   └── fishing-remark-editor/ # 摸鱼备注编辑器组件
├── tests/                   # 测试
└── docs/                    # 文档
```

## 开发规范

### 1. 文件命名规范

#### 管理器文件
- 文件名：`{功能名}-manager.js`
- 类名：`{功能名}Manager`
- 示例：`work-manager.js` → `WorkManager`

#### 服务文件
- 文件名：`{功能名}-service.js`
- 类名：`{功能名}Service`
- 示例：`dashboard-service.js` → `DashboardService`

#### 工具类文件
- 文件名：`{功能名}-{类型}.js`
- 类名：`{功能名}{类型}`
- 示例：`data-validator.js` → `DataValidator`

#### 页面和组件
- 使用kebab-case命名
- 示例：`work-history`, `time-chart`

### 2. 代码组织原则

#### 单一职责原则
每个文件和类都应该有明确的单一职责：
- **管理器**: 负责特定领域的数据管理
- **服务**: 负责特定业务逻辑处理
- **工具类**: 负责通用功能实现

#### 依赖方向
```
高层模块 → 低层模块
页面/组件 → 服务 → 管理器 → 工具类
```

#### 模块独立性
- 工具类不应依赖业务逻辑
- 管理器之间可以相互依赖，但要避免循环依赖
- 服务层作为业务逻辑的封装，可以依赖多个管理器

### 3. 导入导出规范

#### CommonJS模块语法
```javascript
// 导出
class DataManager {
  // 类实现
}

module.exports = new DataManager()

// 导入
const dataManager = require('./core/managers/data-manager.js')
const { DataValidator } = require('./utils/validators/data-validator.js')
```

#### 统一导出文件
每个目录都应该有 `index.js` 文件提供统一导出：
```javascript
// core/index.js
const dataManager = require('./managers/data-manager.js')
const { DashboardService } = require('./services/dashboard-service.js')

module.exports = {
  dataManager,
  DashboardService
}

// utils/index.js
const { DataValidator } = require('./validators/data-validator.js')
const { DataFormatter } = require('./formatters/data-formatter.js')

module.exports = {
  DataValidator,
  DataFormatter
}
```

### 4. 微信小程序特殊规范

#### 避免使用的语法
```javascript
// ❌ 不要使用扩展运算符
const newObj = { ...oldObj, newProp: 'value' }

// ✅ 使用 Object.assign
const newObj = Object.assign({}, oldObj, { newProp: 'value' })

// ❌ 不要在WXML中使用复杂表达式
<view>{{ items.filter(item => item.active).length }}</view>

// ✅ 在JS中处理后传递
<view>{{ activeItemsCount }}</view>
```

#### 使用rpx单位
```css
/* ✅ 使用rpx适配不同设备 */
.container {
  width: 750rpx;
  padding: 20rpx;
}

/* ❌ 避免使用px */
.container {
  width: 375px;
  padding: 10px;
}
```

## 新功能开发流程

### 1. 需求分析
- 确定功能属于哪个业务领域
- 分析是否需要新的数据结构
- 确定涉及的页面和组件

### 2. 数据层设计

#### 新增数据管理器
如果需要管理新的数据领域：

```javascript
// core/managers/new-feature-manager.js
export class NewFeatureManager {
  constructor() {
    this.changeListeners = []
  }

  // 数据初始化
  initializeData() {
    return {
      // 新功能的数据结构
    }
  }

  // 数据验证
  validateData(data) {
    // 验证逻辑
    return { isValid: true, errors: [] }
  }

  // CRUD操作
  create(data) {
    // 创建逻辑
  }

  update(id, data) {
    // 更新逻辑
  }

  delete(id) {
    // 删除逻辑
  }

  // 监听器管理
  addChangeListener(listener) {
    this.changeListeners.push(listener)
  }

  notifyChange() {
    this.changeListeners.forEach(listener => listener())
  }
}

export default new NewFeatureManager()
```

#### 集成到主数据管理器
```javascript
// core/managers/data-manager.js
import newFeatureManager from './new-feature-manager.js'

export class DataManager {
  constructor() {
    // 添加新的管理器
    this.newFeatureManager = newFeatureManager
    
    // 在初始化数据中添加新字段
    this.userData = {
      // 现有字段...
      newFeature: this.newFeatureManager.initializeData()
    }
  }

  // 提供访问新功能数据的方法
  getNewFeatureData() {
    return this.userData.newFeature
  }

  updateNewFeatureData(data) {
    this.userData.newFeature = data
    this.saveData()
    this.notifyChange()
  }
}
```

### 3. 业务服务层

#### 创建新的服务
```javascript
// core/services/new-feature-service.js
import dataManager from '../managers/data-manager.js'
import { DataValidator } from '../../utils/validators/data-validator.js'

export class NewFeatureService {
  constructor() {
    this.dataManager = dataManager
  }

  // 业务逻辑方法
  processNewFeature(inputData) {
    try {
      // 1. 数据验证
      if (!DataValidator.isValidData(inputData)) {
        throw new Error('数据验证失败')
      }

      // 2. 业务逻辑处理
      const processedData = this.processBusinessLogic(inputData)

      // 3. 数据保存
      this.dataManager.updateNewFeatureData(processedData)

      return { success: true, data: processedData }
    } catch (error) {
      console.error('处理新功能失败:', error)
      return { success: false, error: error.message }
    }
  }

  processBusinessLogic(data) {
    // 具体的业务逻辑实现
    return data
  }
}
```

### 4. 工具类开发

#### 新增验证器
```javascript
// utils/validators/new-feature-validator.js
export class NewFeatureValidator {
  static validateSpecificData(data) {
    // 特定的验证逻辑
    return true
  }
}
```

#### 新增格式化器
```javascript
// utils/formatters/new-feature-formatter.js
export class NewFeatureFormatter {
  static formatSpecificData(data) {
    // 特定的格式化逻辑
    return formattedData
  }
}
```

### 5. 页面和组件开发

#### 页面开发
```javascript
// pages/new-feature/index.js
const { NewFeatureService } = require('../../core/services/new-feature-service.js')

Page({
  data: {
    // 页面数据
  },

  onLoad() {
    this.newFeatureService = new NewFeatureService()
    this.loadData()
  },

  async loadData() {
    try {
      const result = await this.newFeatureService.getData()
      this.setData({
        data: result
      })
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  }
})
```

#### 组件开发
```javascript
// components/new-feature-component/index.js
Component({
  properties: {
    data: {
      type: Object,
      value: {}
    }
  },

  data: {
    // 组件内部数据
  },

  methods: {
    handleAction() {
      // 组件方法
      this.triggerEvent('action', { data: this.data })
    }
  }
})
```

## 错误处理规范

### 1. 统一错误格式
```javascript
// 成功响应
{
  success: true,
  data: result,
  message: '操作成功'
}

// 错误响应
{
  success: false,
  error: 'ERROR_CODE',
  message: '用户友好的错误信息',
  details: '详细的错误信息（可选）'
}
```

### 2. 错误处理层级
```javascript
// 管理器层 - 抛出具体错误
throw new Error('数据验证失败: 字段不能为空')

// 服务层 - 捕获并转换错误
try {
  manager.operation()
} catch (error) {
  console.error('服务操作失败:', error)
  return { success: false, error: error.message }
}

// 页面层 - 显示用户友好信息
if (!result.success) {
  wx.showToast({
    title: result.message || '操作失败',
    icon: 'error'
  })
}
```

### 3. 日志记录
```javascript
// 使用统一的日志格式
console.log('[INFO] 操作成功:', data)
console.warn('[WARN] 警告信息:', warning)
console.error('[ERROR] 错误信息:', error)
```

## 性能优化规范

### 1. 数据缓存
```javascript
// 在管理器中实现缓存
class DataManager {
  constructor() {
    this.cache = new Map()
    this.cacheTimeout = 5000 // 5秒缓存
  }

  getData(key) {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }

    const data = this.loadData(key)
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    })
    return data
  }
}
```

### 2. 防抖和节流
```javascript
// 防抖保存
class DataManager {
  saveData() {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }
    
    this.saveTimer = setTimeout(() => {
      this.performSave()
    }, 1000)
  }
}
```

### 3. 模块化加载
```javascript
// 使用require加载模块
loadHeavyFeature() {
  const { HeavyFeature } = require('./heavy-feature.js')
  return new HeavyFeature()
}
```

## 测试规范

### 1. 单元测试
```javascript
// tests/managers/data-manager.test.js
import dataManager from '../../core/managers/data-manager.js'

describe('DataManager', () => {
  test('应该能够初始化数据', () => {
    const data = dataManager.getUserData()
    expect(data).toBeDefined()
    expect(data.version).toBe('0.3.0')
  })

  test('应该能够添加工作履历', () => {
    const workData = {
      company: '测试公司',
      position: '测试职位',
      startDate: new Date()
    }
    
    const workId = dataManager.addWork(workData)
    expect(workId).toBeDefined()
    
    const work = dataManager.getWork(workId)
    expect(work.company).toBe('测试公司')
  })
})
```

### 2. 集成测试
```javascript
// tests/integration/workflow.test.js
describe('完整工作流程', () => {
  test('应该能够完成完整的工作履历管理流程', async () => {
    // 1. 添加工作履历
    const workId = dataManager.addWork(workData)
    
    // 2. 添加时间记录
    const dayData = dataManager.getDayData(workId, new Date())
    dayData.status = 'work'
    dataManager.saveDayData(workId, new Date(), dayData)
    
    // 3. 验证数据
    const savedData = dataManager.getDayData(workId, new Date())
    expect(savedData.status).toBe('work')
  })
})
```

## 部署和发布

### 1. 代码检查
```bash
# 运行测试
npm test

# 代码格式检查
npm run lint

# 构建检查
npm run build
```

### 2. 版本管理
- 使用语义化版本号 (Semantic Versioning)
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 3. 发布流程
1. 完成功能开发和测试
2. 更新版本号和变更日志
3. 提交代码并创建标签
4. 构建和部署到测试环境
5. 测试通过后部署到生产环境

## 常见开发场景

### 1. 添加新的数据字段

#### 场景：为工作履历添加新字段
```javascript
// 1. 在 WorkManager 中添加验证
validateWorkData(workData) {
  const errors = []

  // 现有验证...

  // 新字段验证
  if (workData.newField && !this.isValidNewField(workData.newField)) {
    errors.push('新字段格式不正确')
  }

  return { isValid: errors.length === 0, errors }
}

// 2. 在 createWork 中处理新字段
createWork(workData) {
  const newWork = {
    // 现有字段...
    newField: workData.newField || null, // 新字段
    createTime: new Date(),
    updateTime: new Date()
  }
  return newWork
}
```

### 2. 添加新的页面

#### 创建页面文件结构
```
pages/new-page/
├── index.js      # 页面逻辑
├── index.wxml    # 页面结构
├── index.wxss    # 页面样式
└── index.json    # 页面配置
```

#### 页面模板
```javascript
// pages/new-page/index.js
const { NewPageService } = require('../../core/services/new-page-service.js')

Page({
  data: {
    loading: false,
    error: null,
    pageData: null
  },

  onLoad(options) {
    this.service = new NewPageService()
    this.loadPageData()
  },

  async loadPageData() {
    this.setData({ loading: true, error: null })

    try {
      const result = await this.service.getPageData()
      if (result.success) {
        this.setData({
          pageData: result.data,
          loading: false
        })
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      this.setData({
        error: error.message,
        loading: false
      })
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  onRefresh() {
    this.loadPageData()
  }
})
```

### 3. 添加新的组件

#### 组件模板
```javascript
// components/new-component/index.js
Component({
  options: {
    addGlobalClass: true
  },

  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onDataChange'
    },
    config: {
      type: Object,
      value: {}
    }
  },

  data: {
    internalData: null
  },

  lifetimes: {
    attached() {
      this.initComponent()
    }
  },

  methods: {
    initComponent() {
      // 组件初始化逻辑
    },

    onDataChange(newData, oldData) {
      // 数据变化处理
      this.processData(newData)
    },

    processData(data) {
      // 数据处理逻辑
      this.setData({
        internalData: this.formatData(data)
      })
    },

    formatData(data) {
      // 数据格式化
      return data
    },

    handleUserAction(e) {
      const { action, data } = e.currentTarget.dataset

      // 触发自定义事件
      this.triggerEvent('action', {
        action: action,
        data: data
      })
    }
  }
})
```

## 数据结构设计规范

### 1. 核心数据模型

#### 时间段数据模型
```javascript
// 时间段数据模型 - 只保留核心字段
const TimeSegmentModel = {
  id: 'number',        // 简化ID（递增数字：0, 1, 2...）
  start: 'number',     // 开始时间（分钟数：540 = 09:00）
  end: 'number',       // 结束时间（分钟数：720 = 12:00）
  type: 'string',      // 时间段类型（work, rest, overtime）
  income: 'number'     // 收入金额
}

// 动态计算字段（不存储）
const TimeSegmentDisplayModel = {
  ...TimeSegmentModel,
  hourlyRate: 'number',    // 动态计算：income / ((end - start) / 60)
  startTime: 'string',     // 动态格式化：minutesToTimeDisplay(start)
  endTime: 'string',       // 动态格式化：minutesToTimeDisplay(end)
  duration: 'string',      // 动态计算：formatDuration(end - start)
  durationMinutes: 'number', // 动态计算：end - start
  typeText: 'string'       // 动态映射：getTypeText(type)
}
```

#### 摸鱼数据模型
```javascript
// 摸鱼数据模型 - 只保留核心字段
const FishingModel = {
  id: 'number',        // 简化ID（递增数字：0, 1, 2...）
  start: 'number',     // 开始时间（分钟数：540 = 09:00）
  end: 'number',       // 结束时间（分钟数：720 = 12:00）
  remark: 'string'     // 备注（用户填写，描述摸鱼时在做什么）
}

// 动态计算字段（不存储）
const FishingDisplayModel = {
  ...FishingModel,
  startTime: 'string',     // 动态格式化：minutesToTimeDisplay(start)
  endTime: 'string',       // 动态格式化：minutesToTimeDisplay(end)
  duration: 'string',      // 动态计算：formatDuration(end - start)
  durationMinutes: 'number', // 动态计算：end - start
  income: 'number'         // 动态计算：基于时长和时薪计算的摸鱼收入
}
```

#### 日期数据模型
```javascript
// 日期数据模型 - 只保留核心字段
const DayDataModel = {
  date: 'string',              // 工作日期（YYYY-MM-DD格式）
  segments: 'Array<TimeSegmentModel>', // 时间段数组
  fishes: 'Array<FishingModel>', // 摸鱼数据数组（新增）
  status: 'string'             // 日期状态（active, completed）
}

// 动态计算字段（不存储，实时计算）
const DayDataDisplayModel = {
  ...DayDataModel,
  dailyIncome: 'number',       // 动态计算：sum(work segments income) + sum(fishing income)
  workIncome: 'number',        // 动态计算：sum(work segments income)
  fishingIncome: 'number',     // 动态计算：sum(fishing income)
  totalWorkMinutes: 'number',  // 动态计算：sum(work segments duration)
  totalFishingMinutes: 'number', // 动态计算：sum(fishing duration)
  totalWorkHours: 'number',    // 动态计算：totalWorkMinutes / 60
  averageHourlyRate: 'number', // 动态计算：totalIncome / totalWorkHours
  segmentCount: 'number',      // 动态计算：segments.length
  workSegmentCount: 'number',  // 动态计算：work segments count
  fishingCount: 'number'       // 动态计算：fishes.length
}
```

#### 收入调整数据模型
```javascript
// 收入调整数据模型 - 只保留核心字段
const IncomeAdjustmentModel = {
  extraIncomes: [          // 额外收入数组
    {
      id: 0,               // 从0开始的递增ID
      type: "销售提成",     // 用户自定义类型
      desc: "完成大单",     // 用户自定义描述（可空）
      amount: 100.50       // 金额
    }
  ],
  deductions: [            // 扣款数组
    {
      id: 0,               // 从0开始的递增ID
      type: "迟到扣款",     // 用户自定义类型
      desc: "堵车迟到了",   // 用户自定义描述（可空）
      amount: 12.34        // 金额
    }
  ]
}

// 动态计算字段（不存储）
const IncomeAdjustmentDisplayModel = {
  ...IncomeAdjustmentModel,
  extraIncome: 'number',       // 动态计算：sum(extraIncomes.amount)
  deductions: 'number',        // 动态计算：sum(deductions.amount)
  netAdjustment: 'number',     // 动态计算：extraIncome - deductions
  adjustmentCount: 'number'    // 动态计算：extraIncomes.length + deductions.length
}
```

#### 工作履历数据模型
```javascript
// 工作履历数据模型
const WorkModel = {
  id: 'string',              // 唯一标识
  company: 'string',         // 公司名称
  position: 'string',        // 职位
  startDate: 'Date',         // 入职日期
  endDate: 'Date|null',      // 离职日期
  formalSalary: 'number',    // 正式薪资
  probationSalary: 'number', // 试用期薪资
  probationEndDate: 'Date|null', // 试用期结束日期
  notes: 'string',           // 备注
  timeTracking: 'Object<string, DayDataModel>', // 时间追踪数据
  createTime: 'Date',        // 创建时间
  updateTime: 'Date'         // 更新时间
}
```

### 2. 数据结构优化原则

#### 自动数据压缩
应用使用Deflate算法自动压缩所有存储数据：

```javascript
// 数据自动压缩（开发者无需关心）
dataManager.saveData()

// 控制台会显示压缩信息：
// 开始Deflate压缩，原始大小: 6885 字符
// Deflate压缩后大小: 4231 字符
// 压缩率: 38.54%
```

**压缩特点：**
- 使用经典的Deflate算法（LZ77 + Huffman编码）
- 所有数据都进行压缩，无论大小
- 完全自动化，开发者无需手动处理
- 向后兼容未压缩数据
- 显著减少存储空间使用

#### 存储最小化原则
```javascript
// ✅ 只存储无法计算的核心数据
const segment = {
  id: 0,
  start: 540,
  end: 720,
  type: "work",
  income: 150
}

// ❌ 不要存储可计算的冗余数据
const redundantSegment = {
  id: 0,
  start: 540,
  end: 720,
  type: "work",
  income: 150,
  hourlyRate: 50,        // 冗余：可通过 income / hours 计算
  startTime: "09:00",    // 冗余：可通过 minutesToTimeDisplay(start) 计算
  endTime: "12:00",      // 冗余：可通过 minutesToTimeDisplay(end) 计算
  duration: "3小时",      // 冗余：可通过 formatDuration(end - start) 计算
  createTime: "2025-07-18T17:14:33.893Z", // 冗余：对业务无价值
  updateTime: "2025-07-18T17:14:33.893Z"  // 冗余：对业务无价值
}
```

#### 动态计算策略
```javascript
// 在需要显示时动态计算
function convertSegmentForDisplay(segment) {
  const duration = segment.end - segment.start
  const hourlyRate = (segment.type !== 'rest' && segment.income > 0)
    ? segment.income / (duration / 60)
    : 0

  return {
    ...segment,
    hourlyRate: hourlyRate,
    startTime: minutesToTimeDisplay(segment.start),
    endTime: minutesToTimeDisplay(segment.end),
    duration: formatDuration(duration),
    durationMinutes: duration,
    typeText: getTypeText(segment.type)
  }
}
```

#### ID简化策略
```javascript
// ✅ 使用简单递增数字ID
const segments = [
  { id: 0, start: 540, end: 720, type: "work", income: 150 },
  { id: 1, start: 720, end: 780, type: "rest", income: 0 },
  { id: 2, start: 780, end: 1080, type: "work", income: 250 }
]

// ❌ 避免复杂的长ID
const complexSegments = [
  { id: 1752858873897.2405, start: 540, end: 720, type: "work", income: 150 },
  { id: 1752858873897.3405, start: 720, end: 780, type: "rest", income: 0 }
]
```

### 3. 数据验证规则
```javascript
// 时间段数据验证规则
const TimeSegmentValidationRules = {
  id: {
    required: true,
    type: 'number',
    min: 0
  },
  start: {
    required: true,
    type: 'number',
    min: 0,
    max: 2880  // 最大48小时（2天）
  },
  end: {
    required: true,
    type: 'number',
    min: 0,
    max: 2880,
    validator: (value, data) => value > data.start  // end 必须大于 start
  },
  type: {
    required: true,
    type: 'string',
    enum: ['work', 'rest', 'overtime']
  },
  income: {
    required: true,
    type: 'number',
    min: 0,
    max: 100000
  }
}

// 工作履历验证规则
const WorkValidationRules = {
  company: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  position: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 50
  },
  startDate: {
    required: true,
    type: 'date'
  },
  formalSalary: {
    required: false,
    type: 'number',
    min: 0,
    max: 1000000
  }
}
```

## API设计规范

### 1. 管理器API设计
```javascript
class FeatureManager {
  // 查询方法 - 使用 get 前缀
  getItem(id) { }
  getItems(filter) { }
  getItemsByDate(date) { }

  // 创建方法 - 使用 create 前缀
  createItem(data) { }
  createItems(dataArray) { }

  // 更新方法 - 使用 update 前缀
  updateItem(id, data) { }
  updateItems(updates) { }

  // 删除方法 - 使用 delete 前缀
  deleteItem(id) { }
  deleteItems(ids) { }

  // 验证方法 - 使用 validate 前缀
  validateItem(data) { }
  validateItems(dataArray) { }

  // 计算方法 - 使用 calculate 前缀
  calculateTotal(items) { }
  calculateAverage(items) { }
}
```

### 2. 服务API设计
```javascript
class FeatureService {
  // 业务操作方法 - 使用动词开头
  processData(data) { }
  generateReport(options) { }
  exportData(format) { }
  importData(data) { }

  // 查询方法 - 返回业务对象
  getFeatureData(params) { }
  searchFeatures(query) { }

  // 状态管理方法
  activateFeature(id) { }
  deactivateFeature(id) { }
  resetFeature(id) { }
}
```

## 调试和问题排查

### 1. 调试工具使用
```javascript
// 开发环境调试
if (__DEV__) {
  console.group('数据管理器调试')
  console.log('当前数据:', this.userData)
  console.log('操作类型:', operation)
  console.log('操作参数:', params)
  console.groupEnd()
}

// 性能监控
const startTime = Date.now()
// 执行操作
const endTime = Date.now()
console.log(`操作耗时: ${endTime - startTime}ms`)
```

### 2. 常见问题排查

#### 数据不更新问题
```javascript
// 检查点1: 是否调用了保存方法
dataManager.updateWork(workId, data)
dataManager.saveData() // 确保调用保存

// 检查点2: 是否触发了变化通知
dataManager.notifyChange() // 确保通知UI更新

// 检查点3: 页面是否监听了数据变化
dataManager.addChangeListener(() => {
  this.loadData() // 重新加载数据
})
```

#### 性能问题排查
```javascript
// 检查缓存命中率
console.log('缓存命中率:', this.cacheHitCount / this.totalRequests)

// 检查数据大小
console.log('数据大小:', JSON.stringify(data).length, '字符')

// 检查操作频率
console.log('操作频率:', this.operationCount, '次/秒')
```

## 代码质量保证

### 1. 代码审查清单
- [ ] 是否遵循命名规范
- [ ] 是否有适当的错误处理
- [ ] 是否有必要的注释
- [ ] 是否符合性能要求
- [ ] 是否有单元测试
- [ ] 是否符合微信小程序规范

### 2. 性能检查清单
- [ ] 是否使用了缓存机制
- [ ] 是否避免了不必要的计算
- [ ] 是否使用了防抖/节流
- [ ] 是否优化了数据结构
- [ ] 是否减少了存储操作

### 3. 安全检查清单
- [ ] 是否验证了用户输入
- [ ] 是否处理了敏感数据
- [ ] 是否有适当的权限控制
- [ ] 是否防止了数据泄露

## 导航栏架构设计

### 设计原则

项目采用完全自主的导航栏架构，每个页面/组件都拥有完全的导航栏控制权：

1. **完全自主**：每个页面/组件直接在自己的 wxml 和 wxss 中实现导航栏
2. **个性化定制**：每个仪表盘可以有独特的颜色、按钮、布局
3. **共享切换逻辑**：仪表盘切换功能通过共用组件统一管理
4. **事件驱动**：通过事件机制实现组件间通信

### 架构组成

#### 1. Index 页面导航栏
```javascript
// pages/index/index.wxml
<view class="index-navbar loading-theme">
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  <view class="navbar-content">
    <text class="navbar-title">时间跟踪器</text>
  </view>
</view>
```

**特点**：
- 引导页面：蓝色渐变主题，只显示标题
- 加载页面：紫色渐变主题，只显示标题
- 无任何按钮，符合引导页面的简洁需求

#### 2. Dashboard1 导航栏
```javascript
// components/dashboard1/index.wxml
<view class="dashboard1-navbar">
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  <view class="navbar-content">
    <view class="navbar-left">
      <view class="navbar-button" bindtap="onShowDashboardSwitcher">
        <text class="button-icon">🔄</text>
      </view>
      <view class="navbar-button" bindtap="onShowSettings">
        <text class="button-icon">⚙️</text>
      </view>
    </view>
    <view class="navbar-center">
      <text class="navbar-title">时间跟踪器</text>
    </view>
    <view class="navbar-right">
      <!-- 右侧留空，避免被微信菜单覆盖 -->
    </view>
  </view>
</view>
```

**特点**：
- 蓝紫色渐变主题
- 左侧按钮组：仪表盘切换按钮 + 设置按钮
- 右侧留空，避免被微信小程序菜单覆盖
- 独立的设置模态框，包含收入小数位数设置

#### 3. Dashboard2 导航栏
```javascript
// components/dashboard2/index.wxml
<view class="dashboard2-navbar">
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  <view class="navbar-content">
    <view class="navbar-left">
      <view class="navbar-button" bindtap="onShowDashboardSwitcher">
        <text class="button-icon">🔄</text>
      </view>
      <view class="navbar-button" bindtap="onShowSettings">
        <text class="button-icon">⚙️</text>
      </view>
    </view>
    <view class="navbar-center">
      <text class="navbar-title">时间跟踪器</text>
    </view>
    <view class="navbar-right">
      <!-- 右侧留空，避免被微信菜单覆盖 -->
    </view>
  </view>
</view>
```

**特点**：
- 橙黄色渐变主题
- 左侧按钮组：仪表盘切换按钮 + 设置按钮
- 深色按钮样式，适配浅色背景
- 右侧留空，避免被微信小程序菜单覆盖
- 独立的设置模态框，包含收入小数位数设置

#### 4. 共用仪表盘切换器
```javascript
// components/dashboard-switcher/index.js
// 位置：pages/index 中统一管理
<dashboard-switcher
  show="{{showDashboardSwitcher}}"
  current-dashboard="{{currentDashboard}}"
  bind:dashboardChange="onDashboardChange"
  bind:hide="onHideDashboardSwitcher">
</dashboard-switcher>
```

**特点**：
- 只在 index 页面中存在一个实例
- 通过属性控制显示/隐藏
- 统一的切换逻辑和样式

### 事件流程

#### 仪表盘切换流程
```
用户点击切换按钮
       ↓
Dashboard 组件触发 showDashboardSwitcher 事件
       ↓
Index 页面接收事件，设置 showDashboardSwitcher: true
       ↓
Dashboard-switcher 组件显示，加载仪表盘列表
       ↓
用户选择仪表盘，触发 dashboardChange 事件
       ↓
Index 页面处理切换逻辑，隐藏切换器
```

#### 设置管理流程
```
用户点击设置按钮
       ↓
Dashboard 组件显示自己的设置模态框
       ↓
用户修改设置（包括收入小数位数），点击保存
       ↓
Dashboard 组件保存设置，重新加载数据，隐藏模态框
```

#### 仪表盘独立设置
每个仪表盘都有自己的设置模态框，包含：

**Dashboard1 设置**：
- 显示实时收入：控制是否显示当前收入
- 显示快捷操作：控制是否显示快捷按钮
- 图表高度：调整时间图表的高度
- 显示当前工作：控制是否显示当前工作状态
- **收入小数位数**：设置该仪表盘的收入显示精度（0-3位）

**Dashboard2 设置**：
- 显示倒计时：控制是否显示工作倒计时
- 显示所有统计：控制是否显示详细统计
- 进度条大小：调整圆形进度条的大小
- 显示当前工作：控制是否显示当前工作状态
- **收入小数位数**：设置该仪表盘的收入显示精度（0-3位）

### 开发规范

#### 1. 新增仪表盘组件
```javascript
// 1. 创建组件目录结构
components/dashboard-new/
├── index.js      # 组件逻辑
├── index.wxml    # 组件结构（包含导航栏）
├── index.wxss    # 组件样式（包含导航栏样式）
└── index.json    # 组件配置

// 2. 实现导航栏
// index.wxml
<view class="dashboard-new-navbar">
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  <view class="navbar-content">
    <!-- 自定义导航栏内容 -->
  </view>
</view>

// 3. 添加事件处理
// index.js
onShowDashboardSwitcher: function() {
  this.triggerEvent('showDashboardSwitcher')
}

// 4. 在 index 页面中引用
// pages/index/index.wxml
<dashboard-new
  wx:elif="{{currentDashboard === 'dashboard-new'}}"
  bind:showDashboardSwitcher="onShowDashboardSwitcher">
</dashboard-new>
```

#### 2. 模态框开发规范
```javascript
// 模态框结构
<view class="modal-overlay" wx:if="{{showModal}}" bindtap="onHideModal">
  <view class="modal-content" catchtap="onModalContentTap">
    <!-- 模态框内容 -->
  </view>
</view>

// JavaScript 处理
onModalContentTap: function() {
  // 阻止事件冒泡，防止关闭模态框
}
```

**重要规范**：
- 模态框遮罩层使用 `bindtap` 监听点击关闭
- 模态框内容区使用 `catchtap="onModalContentTap"` 阻止事件冒泡
- 必须实现 `onModalContentTap` 方法来阻止意外关闭
- 设置模态框应包含收入小数位数等仪表盘专属设置

#### 3. 导航栏样式规范
```css
/* 导航栏容器 */
.dashboard-xxx-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: /* 自定义渐变 */;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 状态栏占位 */
.dashboard-xxx-navbar .status-bar {
  width: 100%;
  background: transparent;
}

/* 导航栏内容 */
.dashboard-xxx-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  position: relative;
}

/* 左侧按钮组 */
.dashboard-xxx-navbar .navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16rpx;
  min-width: 144rpx; /* 容纳两个按钮 */
  height: 100%;
}

/* 右侧区域（留空避免被微信菜单覆盖） */
.dashboard-xxx-navbar .navbar-right {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  height: 100%;
}

/* 按钮样式 */
.dashboard-xxx-navbar .navbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: /* 适配主题的半透明背景 */;
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
```

#### 3. 状态栏高度适配
```javascript
// 在组件的 attached 生命周期中
attached: function() {
  // 获取窗口信息
  const windowInfo = wx.getWindowInfo()
  this.setData({
    statusBarHeight: windowInfo.statusBarHeight || 20
  })
}
```

#### 4. 内容区域适配
```css
/* 主要内容区域需要为导航栏留出空间 */
.main-content {
  margin-top: 132rpx; /* 状态栏高度 + 导航栏高度 */
  height: calc(100vh - 132rpx);
}
```

### 扩展性设计

这种架构支持以下扩展：

1. **完全隐藏导航栏**：某些仪表盘可以不显示导航栏
2. **自定义按钮**：可以添加任意数量和类型的按钮
3. **动态主题**：可以根据时间、状态等动态改变导航栏主题
4. **复杂布局**：可以实现多行、分组等复杂导航栏布局

## 核心功能特性

### 收入调整功能

#### 功能特性
1. **灵活的收入管理**
   - 支持额外收入和扣款两大类型
   - 用户完全自定义类型名称，不受预设限制
   - 可选的详细描述，支持空描述
   - 精确的金额记录，支持小数

2. **智能的用户界面**
   - 统一的模态框组件，支持添加和编辑
   - 常见类型快捷选择，提升输入效率
   - 响应式设计，适配各种屏幕尺寸
   - 实时表单验证，确保数据质量

3. **高效的数据管理**
   - 最小化存储原则，只保存核心数据
   - 动态计算所有统计数据，确保实时性
   - 简化ID系统，从0开始递增
   - 自动缓存清除，保持数据一致性

4. **完整的统计分析**
   - 按类型自动分组统计
   - 多维度数据分析
   - 实时净收入计算
   - 历史趋势分析

#### 开发实践要点

**数据结构设计原则**：
```javascript
// ✅ 正确：只保存核心数据
{
  extraIncomes: [
    { id: 0, type: "销售提成", desc: "完成大单", amount: 100.50 }
  ],
  deductions: [
    { id: 0, type: "迟到扣款", desc: "堵车迟到了", amount: 12.34 }
  ]
}

// ❌ 错误：保存可计算的数据
{
  extraIncomes: [...],
  extraIncome: 100.50,    // 冗余：可通过 sum(extraIncomes.amount) 计算
  deductions: [...],
  deductionsTotal: 12.34, // 冗余：可通过 sum(deductions.amount) 计算
  netIncome: 88.16        // 冗余：可通过计算得出
}
```

**组件通信模式**：
```javascript
// 页面触发模态框
onAddIncomeAdjustment(mode) {
  this.selectComponent('#incomeAdjustmentModal').show({
    mode: mode, // 'income' | 'deduction'
    isEdit: false
  })
}

// 模态框事件处理
onIncomeAdjustmentSuccess(e) {
  const { mode, type, amount, description, isEdit, editItem } = e.detail
  if (isEdit) {
    this.handleEditIncomeAdjustment(mode, type, amount, description, editItem)
  } else {
    this.handleAddIncomeAdjustment(mode, type, amount, description)
  }
}
```

**缓存管理策略**：
```javascript
// 数据变更后自动清除相关缓存
const { StatisticsService } = require('./statistics-service.js')
const statisticsService = new StatisticsService()
statisticsService.clearStatisticsCache()

// 刷新相关UI
this.loadSelectedDateData()
```

### 摸鱼备注编辑功能

#### 功能特性
1. **简化的摸鱼开始流程**
   - 一键开始摸鱼，无需预填备注
   - 默认备注为空，降低操作门槛
   - 优化的用户操作体验

2. **灵活的备注编辑**
   - 摸鱼状态显示旁的编辑图标（✏️）
   - 摸鱼过程中可随时修改备注
   - 实时保存备注更改

3. **智能快捷备注系统**
   - 基于历史数据的备注使用频次统计
   - 前10个最常用备注的快捷选项
   - 一键选择快捷备注，自动保存
   - 自定义备注输入（最多100字符）

4. **友好的默认显示**
   - 空备注在日历中显示为"日常摸鱼"
   - 简洁的数据存储策略

#### 技术架构

**核心组件**：
```
fishing-remark-editor (摸鱼备注编辑器)
├── 快捷备注网格显示
├── 自定义备注输入
├── 历史备注频次统计
└── 一键保存功能
```

**数据管理**：
```javascript
// 数据管理器新增方法
updateCurrentFishingRemark(remark) // 更新当前摸鱼备注

// 备注编辑器核心方法
getQuickRemarksFromHistory(dataManager) // 获取快捷备注
createTestFishingData(dataManager) // 创建测试数据
```

**组件交互流程**：
```
fishing-control → fishing-remark-editor → data-manager
     ↓                    ↓                    ↓
  编辑图标          快捷备注选择         备注状态更新
```

#### 开发实践案例

**组件开发**：
```javascript
// fishing-remark-editor/index.js
Component({
  properties: {
    show: { type: Boolean, value: false },
    currentRemark: { type: String, value: '' }
  },

  methods: {
    // 快捷备注选择
    onSelectQuickRemark(e) {
      const remark = e.currentTarget.dataset.remark
      this.saveRemark(remark) // 一键保存
    },

    // 历史备注统计
    getQuickRemarksFromHistory(dataManager) {
      const remarkFrequency = new Map()
      // 遍历所有摸鱼记录，统计备注频次
      // 返回按频次排序的前10个备注
    }
  }
})
```

**数据管理**：
```javascript
// data-manager.js
updateCurrentFishingRemark(remark) {
  const fishingState = this.fishingManager.getFishingState()
  fishingState.remark = remark || ''
  this.fishingManager.saveFishingState(fishingState)
  return { success: true, fishingState }
}
```

#### 用户体验优化
- **操作流程简化**：从"开始→填写备注→确认"简化为"开始→随时编辑"
- **智能化选择**：常用备注一键选择，提升输入效率
- **视觉反馈**：编辑图标动画效果，操作状态清晰
- **数据一致性**：备注修改后所有相关UI同步更新

## 总结

这个开发指南为新功能开发提供了完整的规范和流程。由于这是一个新项目，开发者可以专注于实现新功能，而不需要考虑数据兼容性问题。

### 核心原则
1. **模块化设计** - 每个模块职责单一，便于维护
2. **分层架构** - 清晰的依赖关系，易于扩展
3. **规范统一** - 统一的命名和代码风格
4. **质量优先** - 完善的测试和错误处理
5. **性能导向** - 合理的缓存和优化策略

### 开发流程
1. 需求分析 → 2. 架构设计 → 3. 编码实现 → 4. 测试验证 → 5. 代码审查 → 6. 部署发布

遵循这些规范可以确保项目的长期可维护性和扩展性。在开发过程中如有疑问，请参考项目文档或咨询团队成员。
