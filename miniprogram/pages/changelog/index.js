// 更新日志页面
import { api } from '../../core/api/index.js'

Page({
  data: {
    // 更新日志列表
    changelogList: [],
    // 加载状态
    loading: true,
    // 错误信息
    errorMessage: '',
    // 是否有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 20
  },

  /**
   * 页面加载时
   */
  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '更新日志'
    })
    
    // 加载更新日志数据
    this.loadChangelogData()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData()
    }
  },

  /**
   * 加载更新日志数据
   */
  async loadChangelogData() {
    try {
      this.setData({
        loading: true,
        errorMessage: ''
      })

      console.log('[Changelog] 开始加载更新日志数据')

      // 使用封装的API方法
      const result = await api.general.getChangelogList({
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
      })

      if (result.success) {
        const { list, hasMore } = result.data

        // 处理数据格式
        const processedList = this.processChangelogData(list)

        this.setData({
          changelogList: processedList,
          hasMore: hasMore || false,
          loading: false
        })

        console.log(`[Changelog] 更新日志加载成功: ${processedList.length}条记录`)
      } else {
        throw new Error(result.message || '获取更新日志失败')
      }
    } catch (error) {
      console.error('[Changelog] 加载更新日志失败:', error)
      this.setData({
        loading: false,
        errorMessage: error.message || '加载失败，请重试'
      })

      wx.showToast({
        title: error.message || '加载失败，请重试',
        icon: 'none'
      })

      // 显示错误提示
      wx.showToast({
        title: '加载失败',
        icon: 'error',
        duration: 2000
      })
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      this.setData({
        currentPage: 1,
        hasMore: true
      })
      
      await this.loadChangelogData()
      
      // 停止下拉刷新
      wx.stopPullDownRefresh()
      
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      })
    } catch (error) {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    try {
      const nextPage = this.data.currentPage + 1

      this.setData({
        loading: true
      })

      console.log(`[Changelog] 加载更多数据: 第${nextPage}页`)

      // 使用封装的API方法
      const result = await api.general.getChangelogList({
        page: nextPage,
        pageSize: this.data.pageSize
      })

      if (result.success) {
        const { list, hasMore } = result.data

        // 处理数据格式
        const processedList = this.processChangelogData(list)

        this.setData({
          changelogList: [...this.data.changelogList, ...processedList],
          hasMore: hasMore || false,
          currentPage: nextPage,
          loading: false
        })

        console.log(`[Changelog] 加载更多成功: 第${nextPage}页，${processedList.length}条记录`)
      } else {
        throw new Error(result.message || '加载更多数据失败')
      }
    } catch (error) {
      console.error('[Changelog] 加载更多数据失败:', error)
      this.setData({
        loading: false
      })

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
    }
  },

  /**
   * 处理更新日志数据格式
   * @param {Array} list - 原始数据列表
   * @returns {Array} 处理后的数据列表
   */
  processChangelogData(list) {
    if (!Array.isArray(list)) {
      return []
    }

    return list.map(item => {
      // 格式化日期
      const releaseDate = new Date(item.releaseDate)
      const formattedDate = this.formatDate(releaseDate)
      
      // 处理更新详情，确保是数组格式
      let updateDetails = item.updateDetails || []
      if (typeof updateDetails === 'string') {
        updateDetails = updateDetails.split('\n').filter(detail => detail.trim())
      }
      
      return {
        ...item,
        formattedDate,
        updateDetails,
        // 添加版本类型标识
        versionType: this.getVersionType(item.version)
      }
    })
  },

  /**
   * 格式化日期
   * @param {Date} date - 日期对象
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date || isNaN(date.getTime())) {
      return '未知日期'
    }
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  },

  /**
   * 获取版本类型
   * @param {string} version - 版本号
   * @returns {string} 版本类型
   */
  getVersionType(version) {
    if (!version) return 'patch'
    
    // 简单的版本类型判断
    if (version.includes('beta') || version.includes('alpha')) {
      return 'beta'
    }
    
    const parts = version.split('.')
    if (parts.length >= 2) {
      // 如果是 x.0.0 格式，认为是主版本
      if (parts[1] === '0' && (parts[2] === '0' || !parts[2])) {
        return 'major'
      }
      // 如果是 x.y.0 格式，认为是次版本
      if (parts[2] === '0' || !parts[2]) {
        return 'minor'
      }
    }
    
    return 'patch'
  },

  /**
   * 重试加载数据
   */
  onRetry() {
    this.loadChangelogData()
  },

  /**
   * 复制版本号
   */
  onCopyVersion(e) {
    const version = e.currentTarget.dataset.version
    if (version) {
      wx.setClipboardData({
        data: version,
        success: () => {
          wx.showToast({
            title: '版本号已复制',
            icon: 'success',
            duration: 1000
          })
        }
      })
    }
  }
}) 