<!-- 更新日志页面 -->
<view class="changelog-container">
  <!-- 页面头部 -->
  <view class="changelog-header">
    <view class="header-content">
      <text class="header-title">更新日志</text>
      <text class="header-subtitle">查看应用的版本更新记录</text>
    </view>
    <view class="header-icon">📋</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && changelogList.length === 0}}">
    <view class="loading-icon">⏳</view>
    <text class="loading-text">正在加载更新日志...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{errorMessage && changelogList.length === 0}}">
    <view class="error-icon">❌</view>
    <text class="error-text">{{errorMessage}}</text>
    <button class="retry-button" bindtap="onRetry">重试</button>
  </view>

  <!-- 更新日志列表 -->
  <view wx:for="{{changelogList}}" wx:key="_id" wx:for-item="changelog">
    <!-- 更新内容 -->
    <view class="changelog-content">
      <!-- 版本信息头部 -->
      <view class="version-header {{changelog.versionType}}">
        <view class="version-info">
          <text class="version-number" bindtap="onCopyVersion" data-version="{{changelog.version}}">{{changelog.version}}</text>
          <view class="version-badge {{changelog.versionType}}">
            <text class="badge-text">{{changelog.versionType === 'major' ? '主版本' : 
                                       changelog.versionType === 'minor' ? '次版本' : 
                                       changelog.versionType === 'beta' ? '测试版' : '修复版'}}</text>
          </view>
        </view>
        <text class="release-date">{{changelog.formattedDate}}</text>
      </view>

      <!-- 版本信息正文 -->
      <view class="version-body">
        <!-- 版本标题 -->
        <view class="version-title" wx:if="{{changelog.title}}">
          <text class="title-text">{{changelog.title}}</text>
        </view>

        <!-- 更新详情 -->
        <view class="update-details" wx:if="{{changelog.updateDetails.length > 0}}">
          <view class="detail-item" wx:for="{{changelog.updateDetails}}" wx:key="*this" wx:for-item="detail">
            <view class="detail-bullet">•</view>
            <text class="detail-text">{{detail}}</text>
          </view>
        </view>

        <!-- 版本描述 -->
        <view class="version-description" wx:if="{{changelog.description}}">
          <text class="description-text">{{changelog.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && changelogList.length === 0 && !errorMessage}}">
    <view class="empty-icon">📝</view>
    <text class="empty-text">暂无更新日志</text>
    <text class="empty-desc">开发者还没有发布任何更新记录</text>
  </view>

  <!-- 加载更多状态 -->
  <view class="load-more-container" wx:if="{{changelogList.length > 0}}">
    <view class="loading-more" wx:if="{{loading}}">
      <view class="loading-more-icon">⏳</view>
      <text class="loading-more-text">正在加载更多...</text>
    </view>
    <view class="no-more" wx:if="{{!hasMore && !loading}}">
      <view class="no-more-line"></view>
      <text class="no-more-text">没有更多内容了</text>
      <view class="no-more-line"></view>
    </view>
  </view>
</view> 