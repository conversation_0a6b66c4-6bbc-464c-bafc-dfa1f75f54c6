/* 更新日志页面样式 */

page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 容器 */
.changelog-container {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #f4fff7 0%, #d8e8f8 100%); */
  padding: 20rpx;
}

/* 页面头部 */
.changelog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 0.5rpx;
}

.header-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.header-icon {
  font-size: 80rpx;
  opacity: 0.8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  animation: spin 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.error-text {
  font-size: 28rpx;
  color: #ef4444;
  margin-bottom: 32rpx;
  text-align: center;
}

.retry-button {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.retry-button:active {
  background: #5a6fd8;
}

/* 更新内容 */
.changelog-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  border: 1rpx solid #e2e8f0;
  overflow: hidden;
  margin: 16rpx 0 16rpx 0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 版本信息头部 */
.version-header {
  display: flex;
  padding: 24rpx;
  align-items: center;
  justify-content: space-between;
}

.version-header.major {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.version-header.minor {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.version-header.beta {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.version-header.patch {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.version-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.version-number:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.version-badge {
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.version-badge.major {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.version-badge.minor {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.version-badge.beta {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.version-badge.patch {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.badge-text {
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.release-date {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 版本标题 */
.version-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.5;
}

/* 版本信息正文 */
.version-body {
  padding: 24rpx;
}

/* 更新详情 */
.update-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-bullet {
  color: #667eea;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 4rpx;
}

.detail-text {
  flex: 1;
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.6;
}

/* 版本描述 */
.version-description {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid rgba(102, 126, 234, 0.1);
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  font-style: italic;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 加载更多状态 */
.load-more-container {
  margin-top: 32rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.loading-more-icon {
  font-size: 32rpx;
  animation: spin 2s linear infinite;
}

.loading-more-text {
  font-size: 28rpx;
  color: #666;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
  padding: 32rpx;
}

.no-more-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(to right, transparent, rgba(102, 126, 234, 0.3), transparent);
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

/* 动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .changelog-container {
    padding: 16rpx;
  }
  
  .changelog-header {
    padding: 32rpx 24rpx;
  }
  
  .header-title {
    font-size: 40rpx;
  }
  
  .changelog-content {
    padding: 24rpx;
  }
  
  .version-number {
    font-size: 32rpx;
  }
} 