// 签到页面
import { api } from '../../core/api/index.js'
import { formatRefreshTime } from '../../utils/time-utils.js'

Page({
  data: {
    // 签到状态
    checkInStatus: {
      hasCheckedInToday: false,
      consecutiveDays: 0,
      totalDays: 0,
      longestStreak: 0,
      nextReward: 1
    },

    // 签到日历
    calendar: {
      currentMonth: '',
      checkIns: [],
      days: []
    },

    // 签到历史
    history: [],

    // 加载状态
    loading: {
      checkIn: false,
      status: true, // 初始状态为加载中
      calendar: false,
      history: false
    },

    // 当前显示的月份
    currentYearMonth: '',

    // 新增：缓存和调试信息
    cacheStats: null,
    lastRefreshTime: null,
    lastRefreshTimeText: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('[CheckIn] 签到页面加载')

    // 设置当前月份
    const now = new Date()
    const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    this.setData({
      currentYearMonth: yearMonth
    })

    // 加载数据
    this.loadAllData()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[CheckIn] 签到页面显示')

    // 检查是否需要刷新数据
    const now = Date.now()
    const lastRefresh = this.data.lastRefreshTime
    const shouldRefresh = !lastRefresh || (now - lastRefresh > 2 * 60 * 1000) // 2分钟

    if (shouldRefresh) {
      console.log('[CheckIn] 需要刷新签到状态')
      this.loadCheckInStatus()
    } else {
      console.log('[CheckIn] 使用缓存的签到状态')
    }
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    console.log('[CheckIn] 开始加载所有数据')

    // 并行加载数据以提升性能
    await Promise.all([
      this.loadCheckInStatus(),
      this.loadCheckInCalendar(this.data.currentYearMonth),
      this.loadCheckInHistory()
    ])

    console.log('[CheckIn] 所有数据加载完成')
  },

  /**
   * 加载签到状态（使用新API系统）
   */
  async loadCheckInStatus() {
    try {
      this.setData({
        'loading.status': true
      })

      console.log('[CheckIn] 开始加载签到状态')

      // 使用新的API系统，带缓存
      const result = await api.checkIn.getCheckInStatus({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const now = Date.now()
        this.setData({
          checkInStatus: result.data,
          lastRefreshTime: now,
          lastRefreshTimeText: formatRefreshTime(now)
        })
        console.log('[CheckIn] 签到状态加载成功:', result.data)
      } else {
        throw new Error(result.message || '获取签到状态失败')
      }
    } catch (error) {
      console.error('[CheckIn] 加载签到状态失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.status': false
      })
    }
  },

  /**
   * 加载签到日历（使用新API系统）
   */
  async loadCheckInCalendar(yearMonth) {
    try {
      this.setData({
        'loading.calendar': true
      })

      console.log(`[CheckIn] 开始加载签到日历: ${yearMonth}`)

      // 使用新的API系统，每个月份独立缓存
      const result = await api.checkIn.getCheckInCalendar(yearMonth, {
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const checkIns = result.data.checkIns
        const calendarDays = this.generateCalendarData(yearMonth, checkIns)

        this.setData({
          'calendar.currentMonth': yearMonth,
          'calendar.checkIns': checkIns,
          'calendar.days': calendarDays
        })
        console.log(`[CheckIn] 签到日历加载成功: ${yearMonth}, 签到记录: ${checkIns.length}条`)
      } else {
        throw new Error(result.message || '获取签到日历失败')
      }
    } catch (error) {
      console.error('[CheckIn] 加载签到日历失败:', error)
      wx.showToast({
        title: error.message || '加载日历失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.calendar': false
      })
    }
  },

  /**
   * 加载签到历史（使用新API系统）
   */
  async loadCheckInHistory() {
    try {
      this.setData({
        'loading.history': true
      })

      console.log('[CheckIn] 开始加载签到历史')

      // 使用新的API系统，带缓存
      const result = await api.checkIn.getCheckInHistory({ limit: 10 }, {
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        this.setData({
          history: result.data.history
        })
        console.log(`[CheckIn] 签到历史加载成功: ${result.data.history.length}条记录`)
      } else {
        throw new Error(result.message || '获取签到历史失败')
      }
    } catch (error) {
      console.error('[CheckIn] 加载签到历史失败:', error)
      wx.showToast({
        title: error.message || '加载历史失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.history': false
      })
    }
  },

  /**
   * 执行签到（使用新API系统）
   */
  async onCheckIn() {
    if (this.data.checkInStatus.hasCheckedInToday) {
      wx.showToast({
        title: '今日已签到',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({
        'loading.checkIn': true
      })

      console.log('[CheckIn] 开始执行签到')

      // 使用新的API系统，自动处理重试和缓存清理
      const result = await api.checkIn.checkIn({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const data = result.data
        console.log('[CheckIn] 签到成功:', data)

        // 显示签到成功动画
        this.showCheckInSuccess(data)

        // 刷新数据（缓存已自动清理）
        await this.refreshAfterCheckIn()

      } else {
        throw new Error(result.message || '签到失败')
      }
    } catch (error) {
      console.error('[CheckIn] 签到失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.checkIn': false
      })
    }
  },

  /**
   * 签到后刷新数据
   */
  async refreshAfterCheckIn() {
    console.log('[CheckIn] 签到后刷新数据')

    // 并行刷新所有相关数据
    await Promise.all([
      this.loadCheckInStatus(),
      this.loadCheckInCalendar(this.data.currentYearMonth),
      this.loadCheckInHistory()
    ])

    console.log('[CheckIn] 签到后数据刷新完成')
  },

  /**
   * 显示签到成功动画
   */
  showCheckInSuccess(data) {
    const { reward, consecutiveDays } = data

    wx.showModal({
      title: '签到成功！',
      content: `连续签到 ${consecutiveDays} 天\n获得奖励：${reward} 积分`,
      showCancel: false,
      confirmText: '太棒了',
      success: () => {
        // 可以在这里添加更多的成功反馈
        console.log('[CheckIn] 用户确认签到成功')
      }
    })
  },

  /**
   * 刷新所有数据（清除缓存）
   */
  async onRefreshAll() {
    try {
      console.log('[CheckIn] 手动刷新所有数据')

      // 清除签到相关的所有缓存
      api.checkIn.clearCheckInCache('all')

      // 重新加载所有数据
      await this.loadAllData()

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('[CheckIn] 刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 获取缓存统计信息（调试用）
   */
  onShowCacheStats() {
    const stats = api.getStats()

    const message = `缓存统计:
命中率: ${stats.cache.hitRate}
缓存数量: ${stats.cache.size}
命中次数: ${stats.cache.hits}
未命中次数: ${stats.cache.misses}`

    wx.showModal({
      title: '缓存统计信息',
      content: message,
      showCancel: false
    })
  },

  /**
   * 切换月份
   */
  onMonthChange(event) {
    const { type } = event.currentTarget.dataset
    const currentDate = new Date(this.data.currentYearMonth + '-01')
    
    if (type === 'prev') {
      currentDate.setMonth(currentDate.getMonth() - 1)
    } else if (type === 'next') {
      currentDate.setMonth(currentDate.getMonth() + 1)
    }
    
    const newYearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`
    
    this.setData({
      currentYearMonth: newYearMonth
    })
    
    this.loadCheckInCalendar(newYearMonth)
  },

  /**
   * 查看奖励规则
   */
  onViewRewardRules() {
    const content = `签到奖励规则：

基础奖励：
• 每日签到：1 积分
• 连续7天：5 积分

里程碑奖励：
• 连续30天：额外10积分
• 连续100天：额外30积分
• 连续365天：额外100积分

VIP特权：
• VIP用户奖励1.5倍

积分可用于兑换VIP会员时长等奖励！`

    wx.showModal({
      title: '奖励规则',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 检查日期是否已签到
   */
  isDateCheckedIn(date) {
    return this.data.calendar.checkIns.some(checkIn => checkIn.date === date)
  },

  /**
   * 格式化日期显示
   */
  formatDate(dateStr) {
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  },

  /**
   * 格式化时间显示
   */
  formatTime(checkInAt) {
    const date = new Date(checkInAt)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 生成日历数据
   */
  generateCalendarData(yearMonth, checkIns) {
    const [year, month] = yearMonth.split('-').map(Number)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const daysInMonth = lastDay.getDate()
    const startWeekday = firstDay.getDay()

    const calendarDays = []

    // 添加上个月的日期（填充）
    for (let i = 0; i < startWeekday; i++) {
      calendarDays.push({
        day: '',
        date: '',
        isCurrentMonth: false,
        isCheckedIn: false
      })
    }

    // 添加当月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      const isCheckedIn = checkIns.some(checkIn => checkIn.date === dateStr)

      calendarDays.push({
        day: day,
        date: dateStr,
        isCurrentMonth: true,
        isCheckedIn: isCheckedIn
      })
    }

    return calendarDays
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    console.log('[CheckIn] 下拉刷新')

    try {
      await this.onRefreshAll()
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    console.log('[CheckIn] 页面卸载')
    // 可以在这里做一些清理工作
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    const { consecutiveDays, totalDays } = this.data.checkInStatus

    return {
      title: `我已连续签到${consecutiveDays}天，累计${totalDays}天！`,
      path: '/pages/check-in/index',
      imageUrl: '' // 可以设置分享图片
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { consecutiveDays, totalDays } = this.data.checkInStatus

    return {
      title: `坚持签到第${totalDays}天，连续${consecutiveDays}天！`,
      query: '',
      imageUrl: '' // 可以设置分享图片
    }
  }
})
