// 反馈记录列表页面

import { api } from '../../core/api/index.js'

Page({
  data: {
    // 反馈列表
    feedbacks: [],
    
    // 分页信息
    page: 1,
    limit: 20,
    hasMore: true,
    loading: false,
    
    // 空状态
    isEmpty: false,
    
    // 分类图标映射
    categoryIcons: {
      '功能建议': '💡',
      '问题反馈': '🐛',
      '账号问题': '👤',
      '其他': '📝'
    },
    
    // 状态颜色映射
    statusColors: {
      'pending': '#f39c12',
      'replied': '#27ae60',
      'closed': '#95a5a6'
    }
  },

  onLoad(options) {
    console.log('反馈记录页面加载')
    this.loadFeedbacks()
  },

  onShow() {
    // 页面显示时刷新数据（从反馈提交页面返回时）
    if (this.data.feedbacks.length > 0) {
      this.refreshFeedbacks()
    }
  },

  /**
   * 加载反馈列表
   */
  async loadFeedbacks() {
    if (this.data.loading || !this.data.hasMore) {
      return
    }

    this.setData({ loading: true })

    try {
      // 使用反馈API模块获取用户反馈列表
      const result = await api.feedback.getUserFeedbacks({
        page: this.data.page,
        limit: this.data.limit
      }, {
        showLoading: false,
        showError: false
      })

      console.log('获取反馈列表结果:', result)

      if (result.success) {
        const { feedbacks, hasMore } = result.data

        this.setData({
          feedbacks: this.data.page === 1 ? feedbacks : [...this.data.feedbacks, ...feedbacks],
          hasMore: hasMore,
          isEmpty: this.data.page === 1 && feedbacks.length === 0,
          page: this.data.page + 1
        })
      } else {
        const message = result.message || '获取反馈列表失败'
        wx.showToast({
          title: message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取反馈列表失败:', error)
      wx.showToast({
        title: error.message || '获取数据失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新反馈列表
   */
  async refreshFeedbacks() {
    this.setData({
      page: 1,
      hasMore: true,
      feedbacks: []
    })
    await this.loadFeedbacks()
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.refreshFeedbacks()
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.loadFeedbacks()
  },

  /**
   * 删除反馈
   */
  onDeleteFeedback(e) {
    const { feedbackId, index } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条反馈吗？',
      confirmText: '删除',
      confirmColor: '#e74c3c',
      success: (res) => {
        if (res.confirm) {
          this.deleteFeedback(feedbackId, index)
        }
      }
    })
  },

  /**
   * 执行删除操作
   */
  async deleteFeedback(feedbackId, index) {
    try {
      // 使用反馈API模块删除反馈
      const result = await api.feedback.deleteFeedback({
        feedbackId: feedbackId
      }, {
        showLoading: true,
        showError: false
      })

      console.log('删除反馈结果:', result)

      if (result.success) {
        // 从列表中移除已删除的反馈
        const feedbacks = [...this.data.feedbacks]
        feedbacks.splice(index, 1)

        this.setData({
          feedbacks: feedbacks,
          isEmpty: feedbacks.length === 0
        })

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        const message = result.result?.message || '删除失败'
        wx.showToast({
          title: message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('删除反馈失败:', error)
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  /**
   * 跳转到反馈提交页面
   */
  onGoToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    })
  },

  /**
   * 展开/收起回复内容
   */
  onToggleReply(e) {
    const { index } = e.currentTarget.dataset
    const feedbacks = [...this.data.feedbacks]
    feedbacks[index].showFullReply = !feedbacks[index].showFullReply
    
    this.setData({
      feedbacks: feedbacks
    })
  }
})
