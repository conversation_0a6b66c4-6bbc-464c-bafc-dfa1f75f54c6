<!-- 反馈记录列表页面 -->
<view class="feedback-list-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-icon">📋</view>
    <view class="header-title">我的反馈</view>
    <view class="header-desc">查看反馈状态和回复</view>
  </view>

  <!-- 反馈列表 -->
  <view class="feedback-list" wx:if="{{!isEmpty}}">
    <view 
      class="feedback-item" 
      wx:for="{{feedbacks}}" 
      wx:key="_id"
      wx:for-index="index"
    >
      <!-- 反馈卡片 -->
      <view class="feedback-card">
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="category-info">
            <text class="category-icon">{{categoryIcons[item.category] || '📝'}}</text>
            <text class="category-text">{{item.category}}</text>
          </view>
          <view class="status-badge" style="background-color: {{statusColors[item.status]}}">
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>

        <!-- 反馈内容 -->
        <view class="card-content">
          <text class="content-text">{{item.contentSummary}}</text>
        </view>

        <!-- 时间信息 -->
        <view class="card-time">
          <text class="time-text">{{item.createTimeText}}</text>
        </view>

        <!-- 回复内容 -->
        <view class="reply-section" wx:if="{{item.reply}}">
          <view class="reply-header">
            <text class="reply-label">管理员回复：</text>
            <text class="reply-time">{{item.replyTimeText}}</text>
          </view>
          <view class="reply-content">
            <text class="reply-text">{{item.reply}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions">
          <button 
            class="delete-btn"
            data-feedback-id="{{item._id}}"
            data-index="{{index}}"
            bindtap="onDeleteFeedback"
          >
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <view class="load-more-text" wx:if="{{loading}}">加载中...</view>
      <view class="load-more-text" wx:else>上拉加载更多</view>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && feedbacks.length > 0}}">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <view class="empty-icon">📝</view>
    <view class="empty-title">暂无反馈记录</view>
    <view class="empty-desc">您还没有提交过任何反馈</view>
    <button class="empty-action-btn" bindtap="onGoToFeedback">
      提交反馈
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading && feedbacks.length === 0}}">
    <view class="loading-icon">⏳</view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="bottom-actions" wx:if="{{!isEmpty}}">
    <button class="submit-feedback-btn" bindtap="onGoToFeedback">
      <text class="btn-icon">✏️</text>
      <text class="btn-text">提交新反馈</text>
    </button>
  </view>
</view>
