/* 反馈记录列表页面样式 */

.feedback-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 反馈列表 */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 反馈卡片 */
.feedback-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.feedback-card {
  padding: 30rpx;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.category-icon {
  font-size: 32rpx;
}

.category-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f39c12;
}

.status-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #34495e;
  line-height: 1.6;
}

/* 时间信息 */
.card-time {
  margin-bottom: 20rpx;
}

.time-text {
  font-size: 24rpx;
  color: #95a5a6;
}

/* 回复区域 */
.reply-section {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #3498db;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.reply-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #3498db;
}

.reply-time {
  font-size: 22rpx;
  color: #95a5a6;
}

.reply-content {
  margin-top: 8rpx;
}

.reply-text {
  font-size: 26rpx;
  color: #2c3e50;
  line-height: 1.6;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.delete-btn {
  padding: 12rpx 24rpx;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.delete-btn:active {
  background: #c0392b;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 20rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #95a5a6;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 20rpx;
}

.no-more-text {
  font-size: 28rpx;
  color: #95a5a6;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 50rpx;
}

.empty-action-btn {
  width: 300rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);
}

.empty-action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(52, 152, 219, 0.3);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}

.loading-text {
  font-size: 30rpx;
  color: #7f8c8d;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #e9ecef;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.submit-feedback-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);
}

.submit-feedback-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(52, 152, 219, 0.3);
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 30rpx;
}
