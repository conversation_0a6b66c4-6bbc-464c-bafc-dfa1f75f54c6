// 意见反馈页面
import { api } from '../../core/api/index.js'

Page({
  data: {
    // 反馈分类选项
    categories: ['功能建议', '问题反馈', '账号问题', '其他'],
    selectedCategoryIndex: 0,

    // 表单数据
    formData: {
      category: '功能建议',
      email: '',
      content: ''
    },

    // 字数统计
    contentLength: 0,
    maxLength: 1000,

    // 提交状态
    submitting: false,
    canSubmit: false
  },

  onLoad(options) {
    console.log('反馈页面加载')
  },

  /**
   * 分类选择器变化
   */
  onCategoryChange(e) {
    const index = parseInt(e.detail.value)
    const category = this.data.categories[index]
    
    this.setData({
      selectedCategoryIndex: index,
      'formData.category': category
    })
    
    console.log('选择分类:', category)
  },

  /**
   * 邮箱输入
   */
  onEmailInput(e) {
    const email = e.detail.value
    this.setData({
      'formData.email': email
    })
  },

  /**
   * 内容输入
   */
  onContentInput(e) {
    const content = e.detail.value
    const length = content.length
    
    this.setData({
      'formData.content': content,
      contentLength: length,
      canSubmit: length > 0 && length <= this.data.maxLength
    })
  },

  /**
   * 提交反馈
   */
  async onSubmitFeedback() {
    if (this.data.submitting) {
      return
    }

    const { formData, contentLength, maxLength } = this.data

    // 前端验证
    if (!formData.content || formData.content.trim().length === 0) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    if (contentLength > maxLength) {
      wx.showToast({
        title: `反馈内容不能超过${maxLength}字`,
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      console.log('[Feedback] 开始提交反馈')

      // 使用封装的API方法提交反馈
      const result = await api.general.submitFeedback({
        category: formData.category,
        email: formData.email.trim(),
        content: formData.content.trim()
      })

      console.log('[Feedback] 提交反馈结果:', result)

      if (result.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })

        // 清空表单
        this.setData({
          'formData.email': '',
          'formData.content': '',
          contentLength: 0,
          canSubmit: false
        })

        console.log('[Feedback] 反馈提交成功，准备跳转')

        // 延迟跳转到反馈记录页面
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/feedback-list/index'
          })
        }, 1500)

      } else {
        const message = result.message || '提交失败'
        wx.showToast({
          title: message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('[Feedback] 提交反馈失败:', error)
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 查看反馈记录
   */
  onViewFeedbackList() {
    wx.navigateTo({
      url: '/pages/feedback-list/index'
    })
  }
})
