<!-- 意见反馈页面 -->
<view class="feedback-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">💬</view>
    <view class="header-title">意见反馈</view>
    <view class="header-desc">您的建议是我们改进的动力</view>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-form">
    <!-- 分类选择 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">反馈分类</text>
        <text class="label-required">*</text>
      </view>
      <picker 
        mode="selector" 
        range="{{categories}}" 
        value="{{selectedCategoryIndex}}" 
        bindchange="onCategoryChange"
        class="category-picker"
      >
        <view class="picker-display">
          <text class="picker-text">{{formData.category}}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 邮箱输入 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">邮箱</text>
        <text class="label-optional">（可选）</text>
      </view>
      <input
        type="text"
        class="form-input"
        placeholder="请输入您的邮箱，便于我们回复"
        value="{{formData.email}}"
        bindinput="onEmailInput"
        maxlength="100"
      />
    </view>

    <!-- 内容输入 -->
    <view class="form-section">
      <view class="form-label">
        <text class="label-text">反馈内容</text>
        <text class="label-required">*</text>
      </view>
      <textarea
        class="form-textarea"
        placeholder="请详细描述您的问题或建议，我们会认真对待每一条反馈"
        value="{{formData.content}}"
        bindinput="onContentInput"
        maxlength="{{maxLength}}"
        auto-height
        show-confirm-bar="{{false}}"
      />
      <view class="char-count">
        <text class="count-text {{contentLength > maxLength ? 'over-limit' : ''}}">
          {{contentLength}}/{{maxLength}}
        </text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <button 
        class="submit-btn {{canSubmit && !submitting ? 'active' : 'disabled'}}"
        disabled="{{!canSubmit || submitting}}"
        bindtap="onSubmitFeedback"
      >
        <text wx:if="{{submitting}}">提交中...</text>
        <text wx:else>提交反馈</text>
      </button>
    </view>
  </view>

  <!-- 查看记录链接 -->
  <view class="view-records">
    <view class="records-link" bindtap="onViewFeedbackList">
      <text class="link-text">查看我的反馈记录</text>
      <text class="link-arrow">›</text>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-section">
    <view class="tips-title">💡 温馨提示</view>
    <view class="tips-content">
      <text class="tip-item">• 我们会在1-3个工作日内回复您的反馈</text>
      <text class="tip-item">• 提供邮箱可以让我们更好地与您沟通</text>
      <text class="tip-item">• 详细的描述有助于我们快速定位问题</text>
    </view>
  </view>
</view>
