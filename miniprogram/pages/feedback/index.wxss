/* 意见反馈页面样式 */

.feedback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
}

.header-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 反馈表单 */
.feedback-form {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 40rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.label-required {
  color: #e74c3c;
  margin-left: 8rpx;
  font-size: 28rpx;
}

.label-optional {
  color: #95a5a6;
  margin-left: 8rpx;
  font-size: 24rpx;
}

/* 分类选择器 */
.category-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 30rpx;
  color: #2c3e50;
}

.picker-arrow {
  font-size: 24rpx;
  color: #6c757d;
}

/* 表单输入框 */
.form-input {
  width: auto;
  padding: 24rpx 30rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3498db;
  background: white;
}

/* 文本域 */
.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx 30rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  box-sizing: border-box;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #3498db;
  background: white;
}

/* 字数统计 */
.char-count {
  text-align: right;
  margin-top: 10rpx;
}

.count-text {
  font-size: 24rpx;
  color: #6c757d;
}

.count-text.over-limit {
  color: #e74c3c;
}

/* 提交按钮 */
.form-actions {
  margin-top: 50rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);
}

.submit-btn.disabled {
  background: #bdc3c7;
  color: #7f8c8d;
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(52, 152, 219, 0.3);
}

/* 查看记录链接 */
.view-records {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.records-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
}

.link-text {
  font-size: 30rpx;
  color: #3498db;
  font-weight: 500;
}

.link-arrow {
  font-size: 32rpx;
  color: #3498db;
}

.records-link:active {
  background: #f8f9fa;
}

/* 温馨提示 */
.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tips-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.5;
}
