// 友情应用页面
import { api } from '../../core/api/index.js'
import { formatRefreshTime } from '../../utils/time-utils.js'

Page({
  data: {
    apps: [], // 友情应用列表
    loading: true, // 加载状态
    error: false, // 错误状态
    errorMessage: '', // 错误信息
    isEmpty: false, // 是否为空

    // 新增：缓存和统计信息（可选，用于调试）
    cacheStats: null,
    lastRefreshTime: null,
    lastRefreshTimeText: ''
  },

  onLoad() {
    console.log('[FriendApps] 页面加载')
    this.loadFriendApps()
  },

  onShow() {
    // 页面显示时，如果数据为空或者距离上次刷新超过5分钟，则刷新数据
    const now = Date.now()
    const lastRefresh = this.data.lastRefreshTime
    const shouldRefresh = !this.data.apps.length ||
                         !lastRefresh ||
                         (now - lastRefresh > 5 * 60 * 1000) // 5分钟

    if (shouldRefresh) {
      console.log('[FriendApps] 页面显示，需要刷新数据')
      this.loadFriendApps()
    } else {
      console.log('[FriendApps] 页面显示，使用缓存数据')
      // 更新显示的时间文本（可能跨天了）
      if (lastRefresh) {
        this.setData({
          lastRefreshTimeText: formatRefreshTime(lastRefresh)
        })
      }
    }
  },

  onPullDownRefresh() {
    // 下拉刷新：清除缓存并重新加载
    console.log('[FriendApps] 下拉刷新')
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载友情应用列表（使用缓存）
   */
  async loadFriendApps() {
    try {
      this.setData({
        loading: true,
        error: false,
        errorMessage: ''
      })

      console.log('[FriendApps] 开始加载友情应用列表')

      // 使用新的API系统，自动处理缓存
      const result = await api.friendApps.getFriendAppsWithCache({}, {
        showLoading: false,  // 使用页面自定义的loading状态
        showError: false     // 使用页面自定义的错误处理
      })

      if (result.success) {
        const apps = result.data.apps || []
        console.log(`[FriendApps] 加载成功，获取到 ${apps.length} 个应用`)

        const now = Date.now()
        this.setData({
          apps,
          isEmpty: apps.length === 0,
          loading: false,
          lastRefreshTime: now,
          lastRefreshTimeText: formatRefreshTime(now)
        })

        // 可选：显示缓存统计信息（调试用）
        if (wx.getStorageSync('debug_mode')) {
          this.updateCacheStats()
        }
      } else {
        throw new Error(result.message || '获取友情应用失败')
      }
    } catch (error) {
      console.error('[FriendApps] 加载友情应用失败:', error)
      this.setData({
        loading: false,
        error: true,
        errorMessage: error.message || '加载失败，请重试',
        apps: [],
        isEmpty: false
      })

      // 显示错误提示
      wx.showToast({
        title: error.message || '加载失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 刷新数据（清除缓存）
   */
  async refreshData() {
    try {
      console.log('[FriendApps] 刷新数据，清除缓存')

      // 清除友情应用相关的所有缓存
      api.friendApps.clearFriendAppsCache('all')

      // 重新加载数据
      await this.loadFriendApps()

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('[FriendApps] 刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 更新缓存统计信息（调试用）
   */
  updateCacheStats() {
    const stats = api.getStats()
    this.setData({
      cacheStats: stats
    })
    console.log('[FriendApps] 缓存统计:', stats)
  },



  /**
   * 点击应用卡片
   */
  onAppTap(e) {
    const { app } = e.currentTarget.dataset
    if (!app || !app.navigateParams) {
      wx.showToast({
        title: '应用配置错误',
        icon: 'none'
      })
      return
    }

    this.navigateToMiniProgram(app)
  },

  /**
   * 跳转到小程序
   */
  navigateToMiniProgram(app) {
    const { navigateParams } = app
    
    // 验证必填参数
    if (!navigateParams.appId) {
      wx.showToast({
        title: '应用ID不能为空',
        icon: 'none'
      })
      return
    }

    // 构建跳转参数
    const params = {
      appId: navigateParams.appId,
      success: (res) => {
        console.log('跳转成功:', res)
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        let errorMsg = '跳转失败'
        
        if (err.message) {
          if (err.message.includes('cancel')) {
            errorMsg = '用户取消跳转'
          } else if (err.message.includes('not exist')) {
            errorMsg = '目标小程序不存在'
          } else if (err.message.includes('permission')) {
            errorMsg = '没有跳转权限'
          }
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    }

    // 添加可选参数
    if (navigateParams.path) {
      params.path = navigateParams.path
    }
    
    if (navigateParams.extraData && Object.keys(navigateParams.extraData).length > 0) {
      params.extraData = navigateParams.extraData
    }
    
    if (navigateParams.envVersion) {
      params.envVersion = navigateParams.envVersion
    }

    // 执行跳转
    wx.navigateToMiniProgram(params)
  },

  /**
   * 重新加载（重试）
   */
  onRetry() {
    console.log('[FriendApps] 用户点击重试')
    this.loadFriendApps()
  },

  /**
   * 清除缓存并重新加载
   */
  onClearCache() {
    console.log('[FriendApps] 清除缓存')
    this.refreshData()
  },

  /**
   * 预热缓存（可选功能）
   */
  async onWarmupCache() {
    try {
      console.log('[FriendApps] 开始预热缓存')
      wx.showLoading({ title: '预热缓存中...', mask: true })

      await api.friendApps.warmupCache()

      wx.hideLoading()
      wx.showToast({
        title: '缓存预热完成',
        icon: 'success'
      })

      this.updateCacheStats()
    } catch (error) {
      wx.hideLoading()
      console.error('[FriendApps] 缓存预热失败:', error)
      wx.showToast({
        title: '预热失败',
        icon: 'none'
      })
    }
  },

  /**
   * 显示缓存统计信息（调试功能）
   */
  onShowCacheStats() {
    const stats = api.getStats()
    const cacheInfo = api.friendApps.getCacheStats()

    const message = `缓存统计:
命中率: ${stats.cache.hitRate}
缓存数量: ${stats.cache.size}
命中次数: ${stats.cache.hits}
未命中次数: ${stats.cache.misses}`

    wx.showModal({
      title: '缓存统计信息',
      content: message,
      showCancel: false
    })
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '发现更多好用的小程序',
      path: '/pages/friend-apps/index'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '发现更多好用的小程序'
    }
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    console.log('[FriendApps] 页面卸载')
    // 可以在这里做一些清理工作，比如清理过期缓存
    // api.cleanupExpiredCache()
  },

  /**
   * 长按应用卡片（可扩展功能）
   */
  onAppLongPress(e) {
    const { app } = e.currentTarget.dataset
    if (!app) return

    // 显示更多操作选项
    wx.showActionSheet({
      itemList: ['查看详情', '复制应用ID', '举报应用'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showAppDetail(app)
            break
          case 1:
            this.copyAppId(app)
            break
          case 2:
            this.reportApp(app)
            break
        }
      }
    })
  },

  /**
   * 显示应用详情
   */
  showAppDetail(app) {
    const detail = `应用名称: ${app.name}
应用ID: ${app.navigateParams?.appId || '未知'}
描述: ${app.description || '暂无描述'}
分类: ${app.category || '未分类'}`

    wx.showModal({
      title: '应用详情',
      content: detail,
      showCancel: false
    })
  },

  /**
   * 复制应用ID
   */
  copyAppId(app) {
    const appId = app.navigateParams?.appId
    if (appId) {
      wx.setClipboardData({
        data: appId,
        success: () => {
          wx.showToast({
            title: '应用ID已复制',
            icon: 'success'
          })
        }
      })
    } else {
      wx.showToast({
        title: '应用ID不存在',
        icon: 'none'
      })
    }
  },

  /**
   * 举报应用
   */
  reportApp(app) {
    wx.showModal({
      title: '举报应用',
      content: '确定要举报这个应用吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以调用举报API
          wx.showToast({
            title: '举报已提交',
            icon: 'success'
          })
        }
      }
    })
  }
})
