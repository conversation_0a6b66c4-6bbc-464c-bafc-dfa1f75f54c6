<!-- 友情应用页面 -->
<view class="friend-apps-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">😕</view>
    <text class="error-message">{{errorMessage}}</text>
    <button class="retry-btn" bindtap="onRetry">重新加载</button>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{isEmpty}}" class="empty-container">
    <view class="empty-icon">📱</view>
    <text class="empty-title">暂无友情应用</text>
    <text class="empty-desc">敬请期待更多精彩应用</text>
  </view>

  <!-- 应用列表 -->
  <view wx:else class="apps-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">友情应用</text>
      <text class="page-subtitle">发现更多好用的小程序</text>
    </view>

    <!-- 功能按钮区域（调试模式下显示） -->
    <view wx:if="{{cacheStats}}" class="debug-controls">
      <view class="debug-title">调试功能</view>
      <view class="debug-buttons">
        <button class="debug-btn" size="mini" bindtap="onClearCache">清除缓存</button>
        <button class="debug-btn" size="mini" bindtap="onWarmupCache">预热缓存</button>
        <button class="debug-btn" size="mini" bindtap="onShowCacheStats">缓存统计</button>
      </view>
      <view wx:if="{{cacheStats}}" class="cache-info">
        <text class="cache-text">缓存命中率: {{cacheStats.cache.hitRate}}</text>
        <text class="cache-text">缓存数量: {{cacheStats.cache.size}}</text>
      </view>
    </view>

    <!-- 应用网格 -->
    <view class="apps-grid">
      <view
        wx:for="{{apps}}"
        wx:key="_id"
        class="app-card"
        data-app="{{item}}"
        bindtap="onAppTap"
        bindlongpress="onAppLongPress"
      >
        <!-- 应用图标 -->
        <view class="app-icon-container">
          <image
            wx:if="{{item.iconUrl}}"
            src="{{item.iconUrl}}"
            class="app-icon"
            mode="aspectFill"
          />
          <view wx:else class="app-icon-placeholder">
            <text class="placeholder-text">{{item.appName.charAt(0)}}</text>
          </view>
        </view>

        <!-- 应用信息 -->
        <view class="app-info">
          <text class="app-name">{{item.appName}}</text>
          <text wx:if="{{item.description}}" class="app-description">{{item.description}}</text>
        </view>

        <!-- 跳转箭头 -->
        <view class="app-arrow">
          <text class="arrow-icon">›</text>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="footer-tip">
      <text class="tip-text">点击应用卡片即可跳转，长按查看更多选项</text>
      <text wx:if="{{lastRefreshTimeText}}" class="refresh-time">
        上次刷新: {{lastRefreshTimeText}}
      </text>
    </view>
  </view>
</view>
