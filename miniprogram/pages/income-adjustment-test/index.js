const incomeAdjustmentService = getApp().getIncomeAdjustmentService()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 测试结果
    testResult: '',

    // 模态框状态
    showModal: false,
    modalMode: 'income',
    testDate: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('收入调整功能测试页面加载')
    this.setData({
      testDate: new Date()
    })
  },

  /**
   * 测试添加额外收入
   */
  onTestAddIncome() {
    try {
      console.log('测试添加额外收入，日期类型:', typeof new Date(), new Date())

      const result = incomeAdjustmentService.addExtraIncome(
        new Date(),
        'commission',
        500,
        '测试销售提成'
      )

      this.setData({
        testResult: `添加额外收入成功！\nID: ${result}\n类型: 销售提成\n金额: ¥500\n描述: 测试销售提成`
      })

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('测试添加额外收入失败:', error)
      this.setData({
        testResult: `添加额外收入失败：${error.message}`
      })

      wx.showToast({
        title: '添加失败',
        icon: 'error'
      })
    }
  },

  /**
   * 测试添加扣款
   */
  onTestAddDeduction() {
    try {
      const result = incomeAdjustmentService.addDeduction(
        new Date(),
        'late',
        50,
        '测试迟到扣款'
      )

      this.setData({
        testResult: `添加扣款成功！\nID: ${result}\n类型: 迟到\n金额: ¥50\n描述: 测试迟到扣款`
      })

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })

    } catch (error) {
      console.error('测试添加扣款失败:', error)
      this.setData({
        testResult: `添加扣款失败：${error.message}`
      })

      wx.showToast({
        title: '添加失败',
        icon: 'error'
      })
    }
  },

  /**
   * 测试获取汇总数据
   */
  onTestGetSummary() {
    try {
      const testDate = new Date()
      console.log('测试获取汇总数据，日期:', testDate)

      const summary = incomeAdjustmentService.getDayAdjustmentSummary(testDate)
      console.log('获取到的汇总数据:', summary)

      // 检查项目数据结构
      let itemsDetail = ''
      if (summary.extraIncomeItems.length > 0) {
        itemsDetail += '\n额外收入项目:\n'
        summary.extraIncomeItems.forEach((item, index) => {
          itemsDetail += `  ${index + 1}. ID:${item.id}, 类型:${item.type}, 金额:${item.amount}, 描述:${item.description}\n`
        })
      }
      if (summary.deductionItems.length > 0) {
        itemsDetail += '\n扣款项目:\n'
        summary.deductionItems.forEach((item, index) => {
          itemsDetail += `  ${index + 1}. ID:${item.id}, 类型:${item.type}, 金额:${item.amount}, 描述:${item.description}\n`
        })
      }

      this.setData({
        testResult: `当日收入调整汇总：\n额外收入: ¥${summary.extraIncome}\n扣款: ¥${summary.deductions}\n净调整: ¥${summary.netAdjustment}\n额外收入项目数: ${summary.extraIncomeItems.length}\n扣款项目数: ${summary.deductionItems.length}${itemsDetail}\n\n完整数据:\n${JSON.stringify(summary, null, 2)}`
      })

    } catch (error) {
      console.error('测试获取汇总数据失败:', error)
      this.setData({
        testResult: `获取汇总数据失败：${error.message}`
      })
    }
  },

  /**
   * 测试统计功能
   */
  onTestStatistics() {
    try {
      const statisticsService = getApp().getStatisticsService()
      const stats = statisticsService.calculateIncomeAdjustmentStatistics()

      this.setData({
        testResult: `收入调整统计：\n总额外收入: ¥${stats.totalExtraIncome}\n总扣款: ¥${stats.totalDeductions}\n净调整: ¥${stats.netAdjustment}\n调整天数: ${stats.adjustmentDays}\n平均调整: ¥${stats.averageAdjustment}`
      })

    } catch (error) {
      console.error('测试统计功能失败:', error)
      this.setData({
        testResult: `统计功能测试失败：${error.message}`
      })
    }
  },

  /**
   * 清除测试数据
   */
  onTestClearData() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除今日的所有收入调整测试数据吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 获取今日的收入调整数据
            const summary = incomeAdjustmentService.getDayAdjustmentSummary(new Date())
            
            // 删除所有额外收入项目
            summary.extraIncomeItems.forEach(item => {
              incomeAdjustmentService.removeExtraIncome(new Date(), item.id)
            })

            // 删除所有扣款项目
            summary.deductionItems.forEach(item => {
              incomeAdjustmentService.removeDeduction(new Date(), item.id)
            })

            this.setData({
              testResult: `清除完成！\n删除了 ${summary.extraIncomeItems.length} 个额外收入项目\n删除了 ${summary.deductionItems.length} 个扣款项目`
            })

            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })

          } catch (error) {
            console.error('清除测试数据失败:', error)
            this.setData({
              testResult: `清除数据失败：${error.message}`
            })

            wx.showToast({
              title: '清除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 显示额外收入模态框
   */
  onShowIncomeModal() {
    this.setData({
      showModal: true,
      modalMode: 'income'
    })
  },

  /**
   * 显示扣款模态框
   */
  onShowDeductionModal() {
    this.setData({
      showModal: true,
      modalMode: 'deduction'
    })
  },

  /**
   * 模态框成功回调
   */
  onModalSuccess(e) {
    const { mode, type, amount, description, id } = e.detail
    console.log('模态框操作成功:', e.detail)

    const modeText = mode === 'income' ? '额外收入' : '扣款'
    this.setData({
      testResult: `模态框${modeText}添加成功！\nID: ${id}\n类型: ${type}\n金额: ¥${amount}\n描述: ${description}`
    })

    wx.showToast({
      title: `${modeText}添加成功`,
      icon: 'success'
    })
  },

  /**
   * 模态框错误回调
   */
  onModalError(e) {
    const { message } = e.detail
    console.error('模态框操作失败:', message)

    this.setData({
      testResult: `模态框操作失败：${message}`
    })

    wx.showToast({
      title: '操作失败',
      icon: 'error'
    })
  },

  /**
   * 关闭模态框
   */
  onModalClose() {
    this.setData({
      showModal: false
    })
  }
})
