<!--收入调整功能测试页面-->
<view class="container">
  <view class="header">
    <text class="title">收入调整功能测试</text>
    <text class="subtitle">测试额外收入和扣款功能</text>
  </view>

  <view class="test-section">
    <view class="section-title">基础功能测试</view>
    
    <view class="test-buttons">
      <button class="test-btn primary" bindtap="onTestAddIncome">
        测试添加额外收入
      </button>
      
      <button class="test-btn secondary" bindtap="onTestAddDeduction">
        测试添加扣款
      </button>
      
      <button class="test-btn info" bindtap="onTestGetSummary">
        测试获取汇总数据
      </button>
      
      <button class="test-btn warning" bindtap="onTestStatistics">
        测试统计功能
      </button>
      
      <button class="test-btn danger" bindtap="onTestClearData">
        清除测试数据
      </button>
    </view>
  </view>

  <view class="result-section" wx:if="{{testResult}}">
    <view class="section-title">测试结果</view>
    <view class="result-content">
      <text class="result-text">{{testResult}}</text>
    </view>
  </view>

  <view class="modal-test-section">
    <view class="section-title">模态框测试</view>
    
    <view class="test-buttons">
      <button class="test-btn primary" bindtap="onShowIncomeModal">
        显示额外收入模态框
      </button>
      
      <button class="test-btn secondary" bindtap="onShowDeductionModal">
        显示扣款模态框
      </button>
    </view>
  </view>

  <!-- 收入调整模态框 -->
  <income-adjustment-modal
    visible="{{showModal}}"
    mode="{{modalMode}}"
    date="{{testDate}}"
    bind:success="onModalSuccess"
    bind:error="onModalError"
    bind:close="onModalClose">
  </income-adjustment-modal>
</view>
