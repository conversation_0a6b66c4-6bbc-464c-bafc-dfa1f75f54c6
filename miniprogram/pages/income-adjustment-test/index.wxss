/* 收入调整功能测试页面样式 */

page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  color: #ffffff;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

.test-section,
.modal-test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
  text-align: center;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-btn {
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.test-btn:active {
  transform: scale(0.98);
}

.test-btn.primary {
  background-color: #3b82f6;
  color: #ffffff;
}

.test-btn.primary:active {
  background-color: #2563eb;
}

.test-btn.secondary {
  background-color: #6b7280;
  color: #ffffff;
}

.test-btn.secondary:active {
  background-color: #4b5563;
}

.test-btn.info {
  background-color: #06b6d4;
  color: #ffffff;
}

.test-btn.info:active {
  background-color: #0891b2;
}

.test-btn.warning {
  background-color: #f59e0b;
  color: #ffffff;
}

.test-btn.warning:active {
  background-color: #d97706;
}

.test-btn.danger {
  background-color: #ef4444;
  color: #ffffff;
}

.test-btn.danger:active {
  background-color: #dc2626;
}

.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.result-content {
  background-color: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e2e8f0;
}

.result-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
  word-break: break-all;
}
