<view class="invite-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">👥</view>
    <view class="header-title">邀请好友</view>
    <view class="header-subtitle">邀请好友使用，双方都有积分奖励</view>
  </view>

  <!-- 邀请奖励说明 -->
  <view class="reward-card">
    <view class="reward-header">
      <view class="reward-title">邀请奖励</view>
      <view class="reward-desc">成功邀请好友，双方都有奖励</view>
    </view>
    
    <view class="reward-content">
      <view class="reward-item">
        <view class="reward-icon">🎁</view>
        <view class="reward-info">
          <view class="reward-label">您的奖励</view>
          <view class="reward-amount">+{{invitationStats.rewards.perInvitation}}积分</view>
        </view>
      </view>
      <view class="reward-divider">+</view>
      <view class="reward-item">
        <view class="reward-icon">🎉</view>
        <view class="reward-info">
          <view class="reward-label">好友奖励</view>
          <view class="reward-amount">+5积分</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 邀请统计 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-title">我的邀请</view>
      <view class="stats-action" bindtap="onViewRules">
        <text class="action-text">邀请规则</text>
        <text class="action-arrow">›</text>
      </view>
    </view>
    
    <view wx:if="{{loading.stats}}" class="loading-state">
      <view class="loading-icon">⏳</view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <view wx:else class="stats-content">
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-value">{{invitationStats.totalInvited}}</view>
          <view class="stat-label">邀请人数</view>
        </view>
        <view class="stat-item">
          <view class="stat-value">{{invitationStats.rewards.totalEarned}}</view>
          <view class="stat-label">获得积分</view>
        </view>
      </view>
      
      <!-- 被邀请信息 -->
      <view wx:if="{{invitationStats.invitedBy}}" class="invited-by-info">
        <view class="invited-by-label">您被以下用户邀请：</view>
        <view class="invited-by-user">
          <image class="user-avatar"
                 src="{{invitationStats.invitedBy.avatar || '/images/avatar.png'}}"
                 mode="aspectFill"
                 lazy-load="true"></image>
          <view class="user-name">{{invitationStats.invitedBy.nickname}}</view>
        </view>
      </view>
      
      <!-- 邀请的用户列表 -->
      <view wx:if="{{invitationStats.invitedUsers.length > 0}}" class="invited-users">
        <view class="invited-users-title">您邀请的好友：</view>
        <view class="invited-users-list">
          <view wx:for="{{invitationStats.invitedUsers}}" wx:key="id" class="invited-user-item">
            <image class="user-avatar"
                   src="{{item.avatar || '/images/avatar.png'}}"
                   mode="aspectFill"
                   lazy-load="true"></image>
            <view class="user-info">
              <view class="user-name">{{item.nickname}}</view>
              <view class="invite-time">{{item.invitedAt}}</view>
            </view>
            <view class="user-reward">+{{invitationStats.rewards.perInvitation}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 邀请操作 -->
  <view class="action-card">
    <view class="action-header">
      <view class="action-title">开始邀请</view>
      <view class="action-subtitle">分享给好友，立即获得积分奖励</view>
    </view>
    
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="onInviteFriends">
        <view class="btn-icon">📤</view>
        <view class="btn-text">分享邀请</view>
      </button>
      
      <button class="action-btn secondary" bindtap="onCopyInviteLink">
        <view class="btn-icon">📋</view>
        <view class="btn-text">复制链接</view>
      </button>
    </view>
    
    <view class="action-tip">
      点击分享邀请，选择好友发送。好友通过您的分享进入小程序即可获得奖励！
    </view>
  </view>

  <!-- 底部导航 -->
  <view class="bottom-nav">
    <button class="nav-btn" bindtap="onGoToPoints">
      <view class="nav-icon">💰</view>
      <view class="nav-text">查看积分</view>
    </button>
  </view>
</view>
