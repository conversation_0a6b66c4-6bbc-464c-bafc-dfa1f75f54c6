// 积分记录页面
import { api } from '../../core/api/index.js'
import { formatRefreshTime } from '../../utils/time-utils.js'

Page({
  data: {
    // 积分统计
    stats: {
      totalEarned: 0,
      totalSpent: 0,
      currentBalance: 0,
      totalRecords: 0
    },

    // 积分记录
    records: [],

    // 筛选条件
    filter: {
      type: '', // '' | 'earn' | 'spend'
    },

    // 加载状态
    loading: {
      records: false,
      more: false,
      sourceStats: false
    },

    // 来源统计数据
    sourceStatsData: [],
    totalSourcePoints: 0,
    pieChartGradient: '',

    // 任务数据
    tasks: [],
    checkInStatus: null,

    // 分页
    pagination: {
      page: 1,        // 改用页码模式
      limit: 20,
      hasMore: true
    },

    // 新增：缓存和调试信息
    cacheStats: null,
    lastRefreshTime: null,
    lastRefreshTimeText: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('[Points] 积分记录页面加载')
    this.loadAllData()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('[Points] 积分记录页面显示')

    // 检查是否需要刷新数据
    const now = Date.now()
    const lastRefresh = this.data.lastRefreshTime
    const shouldRefresh = !lastRefresh || (now - lastRefresh > 2 * 60 * 1000) // 2分钟

    if (shouldRefresh) {
      console.log('[Points] 需要刷新积分数据')
      this.refreshData()
    } else {
      console.log('[Points] 使用缓存的积分数据')
      // 更新显示的时间文本
      if (lastRefresh) {
        this.setData({
          lastRefreshTimeText: formatRefreshTime(lastRefresh)
        })
      }
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    console.log('[Points] 下拉刷新')
    try {
      await this.refreshData()
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.pagination.hasMore && !this.data.loading.more) {
      this.loadMoreRecords()
    }
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    console.log('[Points] 开始加载所有数据')

    // 并行加载数据以提升性能
    await Promise.all([
      this.loadPointsRecords(),
      this.loadSourceStats(),
      this.loadTasks()
    ])

    console.log('[Points] 所有数据加载完成')
  },

  /**
   * 刷新数据（清除缓存）
   */
  async refreshData() {
    try {
      console.log('[Points] 刷新数据，清除缓存')

      // 清除积分相关的所有缓存
      api.points.clearPointsCache('all')

      // 重置分页
      this.setData({
        'pagination.page': 1,
        'pagination.hasMore': true,
        records: []
      })

      // 重新加载所有数据
      await this.loadAllData()

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('[Points] 刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 加载积分记录（使用新API系统）
   */
  async loadPointsRecords() {
    try {
      this.setData({
        'loading.records': true
      })

      console.log('[Points] 开始加载积分记录')

      // 使用新的API系统，支持分页和筛选缓存
      const result = await api.points.getPointsRecordsByPage(
        this.data.pagination.page,
        this.data.pagination.limit,
        { type: this.data.filter.type || undefined },
        {
          showLoading: false,  // 使用页面自定义loading
          showError: false     // 使用页面自定义错误处理
        }
      )

      if (result.success) {
        const data = result.data
        const newRecords = this.data.pagination.page === 1
          ? data.records
          : [...this.data.records, ...data.records]

        const now = Date.now()
        this.setData({
          records: newRecords,
          stats: data.stats || this.data.stats,
          'pagination.hasMore': data.records.length === this.data.pagination.limit,
          lastRefreshTime: now,
          lastRefreshTimeText: formatRefreshTime(now)
        })

        console.log(`[Points] 积分记录加载成功: 第${this.data.pagination.page}页，${data.records.length}条记录`)
      } else {
        throw new Error(result.message || '加载失败')
      }
    } catch (error) {
      console.error('[Points] 加载积分记录失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.records': false
      })
    }
  },

  /**
   * 加载更多记录
   */
  async loadMoreRecords() {
    console.log(`[Points] 加载更多记录: 第${this.data.pagination.page + 1}页`)

    this.setData({
      'loading.more': true,
      'pagination.page': this.data.pagination.page + 1
    })

    await this.loadPointsRecords()

    this.setData({
      'loading.more': false
    })
  },

  /**
   * 筛选类型变化
   */
  onFilterChange(event) {
    const { type } = event.currentTarget.dataset
    const newType = type === this.data.filter.type ? '' : type

    console.log(`[Points] 筛选类型变化: ${this.data.filter.type} -> ${newType}`)

    this.setData({
      'filter.type': newType,
      'pagination.page': 1,
      'pagination.hasMore': true,
      records: []
    })

    // 使用新的按类型筛选API
    this.loadPointsRecordsByType(newType)
  },

  /**
   * 按类型加载积分记录
   */
  async loadPointsRecordsByType(type) {
    try {
      this.setData({
        'loading.records': true
      })

      console.log(`[Points] 按类型加载积分记录: ${type || '全部'}`)

      let result
      if (type) {
        // 使用按类型筛选的API（独立缓存）
        result = await api.points.getPointsRecordsByType(type, {}, {
          showLoading: false,
          showError: false
        })
      } else {
        // 使用分页API获取全部记录
        result = await api.points.getPointsRecordsByPage(1, this.data.pagination.limit, {}, {
          showLoading: false,
          showError: false
        })
      }

      if (result.success) {
        const data = result.data
        const now = Date.now()

        this.setData({
          records: data.records,
          stats: data.stats || this.data.stats,
          'pagination.hasMore': data.records.length === this.data.pagination.limit,
          lastRefreshTime: now,
          lastRefreshTimeText: formatRefreshTime(now)
        })

        console.log(`[Points] 按类型筛选成功: ${data.records.length}条记录`)
      } else {
        throw new Error(result.message || '筛选失败')
      }
    } catch (error) {
      console.error('[Points] 按类型筛选失败:', error)
      wx.showToast({
        title: error.message || '筛选失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.records': false
      })
    }
  },

  /**
   * 加载来源统计数据（使用新API系统）
   */
  async loadSourceStats() {
    try {
      this.setData({
        'loading.sourceStats': true
      })

      console.log('[Points] 开始加载积分统计')

      // 使用新的API系统，带缓存
      const result = await api.points.getPointsStats({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const sourceStats = result.data.sourceStats
        this.processSourceStats(sourceStats)
        console.log('[Points] 积分统计加载成功')
      } else {
        throw new Error(result.message || '获取来源统计失败')
      }
    } catch (error) {
      console.error('[Points] 获取积分统计失败:', error)
      wx.showToast({
        title: error.message || '统计加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.sourceStats': false
      })
    }
  },

  /**
   * 处理来源统计数据
   */
  processSourceStats(sourceStats) {
    const sourceNames = {
      'check_in': '签到获得',
      'purchase': '商店消费',
      'admin': '管理员操作',
      'redeem': '兑换码使用',
      'invite_friend': '邀请好友',
      'invited_by_friend': '被邀请奖励'
    }

    const colors = [
      '#667eea',
      '#764ba2',
      '#f093fb',
      '#f5576c',
      '#4facfe',
      '#00f2fe'
    ]

    let totalPoints = 0
    const statsArray = []

    // 转换数据格式并计算总积分
    Object.keys(sourceStats).forEach((source, index) => {
      const stats = sourceStats[source]
      totalPoints += stats.totalPoints

      statsArray.push({
        source,
        name: sourceNames[source] || source,
        points: stats.totalPoints,
        count: stats.count,
        color: colors[index % colors.length]
      })
    })

    // 计算百分比
    statsArray.forEach(item => {
      item.percentage = totalPoints > 0 ? Math.round((item.points / totalPoints) * 100) : 0
    })

    // 按积分数量排序
    statsArray.sort((a, b) => b.points - a.points)

    // 生成饼图渐变
    const gradient = this.generatePieChartGradient(statsArray, totalPoints)

    this.setData({
      sourceStatsData: statsArray,
      totalSourcePoints: totalPoints,
      pieChartGradient: gradient
    })
  },

  /**
   * 生成饼图渐变
   */
  generatePieChartGradient(statsArray, totalPoints) {
    if (totalPoints === 0) return '#f5f5f5'

    let gradientParts = []
    let currentAngle = 0

    statsArray.forEach(item => {
      const percentage = (item.points / totalPoints) * 100
      const angle = (percentage / 100) * 360

      if (percentage > 0) {
        gradientParts.push(`${item.color} ${currentAngle}deg ${currentAngle + angle}deg`)
        currentAngle += angle
      }
    })

    return gradientParts.join(', ')
  },



  /**
   * 跳转到积分商店
   */
  onGoToStore() {
    wx.navigateTo({
      url: '/pages/store/index'
    })
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60))
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60))
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
      }
      return `${hours}小时前`
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  /**
   * 获取来源图标
   */
  getSourceIcon(source) {
    const iconMap = {
      'check_in': '📅',
      'purchase': '🛒',
      'admin': '⚙️',
      'redeem': '🎫',
      'invite_friend': '👥',
      'invited_by_friend': '🎁'
    }
    return iconMap[source] || '💰'
  },

  /**
   * 获取来源名称
   */
  getSourceName(source) {
    const nameMap = {
      'check_in': '签到获得',
      'purchase': '商店消费',
      'admin': '管理员操作',
      'redeem': '兑换码使用',
      'invite_friend': '邀请好友',
      'invited_by_friend': '被邀请奖励'
    }
    return nameMap[source] || source
  },

  /**
   * 加载任务数据
   */
  async loadTasks() {
    try {
      // 获取签到状态
      await this.loadCheckInStatus()

      // 生成任务列表
      this.generateTasks()
    } catch (error) {
      console.error('加载任务失败:', error)
    }
  },

  /**
   * 获取签到状态（使用新API系统）
   */
  async loadCheckInStatus() {
    try {
      console.log('[Points] 开始获取签到状态')

      // 使用新的API系统，带缓存
      const result = await api.checkIn.getCheckInStatus({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        this.setData({
          checkInStatus: result.data
        })
        console.log('[Points] 签到状态获取成功')
      } else {
        throw new Error(result.message || '获取签到状态失败')
      }
    } catch (error) {
      console.error('[Points] 获取签到状态失败:', error)
      // 任务模块的签到状态获取失败不影响主要功能，只记录错误
    }
  },

  /**
   * 生成任务列表
   */
  generateTasks() {
    const checkInStatus = this.data.checkInStatus
    const tasks = [
      {
        id: 'daily_checkin',
        name: '每日签到',
        description: '每日签到获得积分，连续签到奖励更多',
        icon: '📅',
        iconColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        reward: checkInStatus ? checkInStatus.nextReward : 1,
        status: checkInStatus && checkInStatus.hasCheckedInToday ? 'completed' : 'available',
        actionText: checkInStatus && checkInStatus.hasCheckedInToday ? '已签到' : '去签到',
        progress: checkInStatus ? `连续签到${checkInStatus.consecutiveDays}天` : null
      },
      {
        id: 'invite_friends',
        name: '邀请好友',
        description: '邀请好友注册使用，双方都可获得积分',
        icon: '👥',
        iconColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        reward: 10,
        status: 'available',
        actionText: '邀请好友',
        progress: '今日已邀请0人'
      },
      {
        id: 'watch_ads',
        name: '观看广告',
        description: '观看完整广告视频获得积分奖励',
        icon: '📺',
        iconColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        reward: 2,
        status: 'available',
        actionText: '观看广告',
        progress: '今日已观看0/5次'
      },
      {
        id: 'share_app',
        name: '分享应用',
        description: '分享应用给好友，传播正能量',
        icon: '📤',
        iconColor: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        reward: 5,
        status: 'available',
        actionText: '分享应用',
        progress: '今日未分享'
      },
      {
        id: 'complete_profile',
        name: '完善资料',
        description: '完善个人资料信息，提升使用体验',
        icon: '👤',
        iconColor: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        reward: 20,
        status: 'available',
        actionText: '完善资料',
        progress: null
      }
    ]

    this.setData({
      tasks
    })
  },

  /**
   * 任务操作处理
   */
  async onTaskAction(event) {
    const taskId = event.currentTarget.dataset.task
    console.log('执行任务:', taskId)

    switch (taskId) {
      case 'daily_checkin':
        this.handleCheckInTask()
        break
      case 'invite_friends':
        this.handleInviteTask()
        break
      case 'watch_ads':
        this.handleWatchAdsTask()
        break
      case 'share_app':
        this.handleShareTask()
        break
      case 'complete_profile':
        this.handleProfileTask()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  /**
   * 处理签到任务
   */
  handleCheckInTask() {
    wx.navigateTo({
      url: '/pages/check-in/index'
    })
  },

  /**
   * 处理邀请好友任务
   */
  handleInviteTask() {
    console.log('[INFO] 跳转到邀请好友页面')
    wx.navigateTo({
      url: '/pages/invite/index'
    })
  },

  /**
   * 处理观看广告任务
   */
  handleWatchAdsTask() {
    wx.showModal({
      title: '观看广告',
      content: '广告功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 处理分享任务
   */
  handleShareTask() {
    wx.showModal({
      title: '分享应用',
      content: '点击右上角菜单分享给好友，完成分享任务获得积分奖励！',
      showCancel: false,
      confirmText: '知道了',
      success: () => {
        // 显示分享菜单
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        })
      }
    })
  },

  /**
   * 处理完善资料任务
   */
  handleProfileTask() {
    wx.navigateTo({
      url: '/pages/profile/index'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      path: '/pages/points/index',
      imageUrl: ''
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '积分记录 - 查看我的积分收支明细',
      query: '',
      imageUrl: ''
    }
  },

  /**
   * 获取类型样式类
   */
  getTypeClass(type) {
    return type === 'earn' ? 'type-earn' : 'type-spend'
  },



  /**
   * 清除积分缓存并刷新
   */
  async onClearCache() {
    console.log('[Points] 清除缓存')
    await this.refreshData()
  },

  /**
   * 获取缓存统计信息（调试用）
   */
  onShowCacheStats() {
    const stats = api.getStats()

    const message = `缓存统计:
命中率: ${stats.cache.hitRate}
缓存数量: ${stats.cache.size}
命中次数: ${stats.cache.hits}
未命中次数: ${stats.cache.misses}`

    wx.showModal({
      title: '缓存统计信息',
      content: message,
      showCancel: false
    })
  },

  /**
   * 积分变动后的处理
   */
  onPointsChanged() {
    console.log('[Points] 积分发生变化，清除相关缓存')
    api.points.onPointsChanged()
    this.refreshData()
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    console.log('[Points] 页面卸载')
    // 可以在这里做一些清理工作
  }
})
