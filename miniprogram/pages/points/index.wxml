<view class="points-page">
  <!-- 调试功能区域（调试模式下显示） -->
  <view wx:if="{{cacheStats}}" class="debug-controls">
    <view class="debug-title">调试功能</view>
    <view class="debug-buttons">
      <button class="debug-btn" size="mini" bindtap="onClearCache">刷新数据</button>
      <button class="debug-btn" size="mini" bindtap="onShowCacheStats">缓存统计</button>
    </view>
    <view wx:if="{{cacheStats}}" class="cache-info">
      <text class="cache-text">缓存命中率: {{cacheStats.cache.hitRate}}</text>
      <text class="cache-text">缓存数量: {{cacheStats.cache.size}}</text>
    </view>
  </view>

  <!-- 积分统计卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-title">积分统计</view>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-value">{{stats.currentBalance}}</view>
        <view class="stat-label">当前余额</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{stats.totalEarned}}</view>
        <view class="stat-label">累计获得</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{stats.totalSpent}}</view>
        <view class="stat-label">累计消费</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{stats.totalRecords}}</view>
        <view class="stat-label">记录总数</view>
      </view>
    </view>
    
    <view class="stats-action-bar">
      <button class="action-btn store-btn" bindtap="onGoToStore">
        <text class="btn-icon">🛒</text>
        <text class="btn-text">积分商店</text>
      </button>
    </view>
  </view>

  <!-- 积分记录卡片 -->
  <view class="records-card">
    <!-- 卡片头部 -->
    <view class="records-header">
      <view class="records-title">积分记录</view>
      <view class="filter-options">
        <view class="filter-option {{filter.type === '' ? 'active' : ''}}"
              bindtap="onFilterChange" data-type="">
          全部
        </view>
        <view class="filter-option {{filter.type === 'earn' ? 'active' : ''}}"
              bindtap="onFilterChange" data-type="earn">
          收入
        </view>
        <view class="filter-option {{filter.type === 'spend' ? 'active' : ''}}"
              bindtap="onFilterChange" data-type="spend">
          支出
        </view>
      </view>
    </view>

    <!-- 记录内容 -->
    <view class="records-content">
      <view wx:if="{{loading.records}}" class="loading-state">
        <view class="loading-icon">⏳</view>
        <view class="loading-text">加载中...</view>
      </view>

      <view wx:elif="{{records.length === 0}}" class="empty-state">
        <view class="empty-icon">📝</view>
        <view class="empty-text">暂无积分记录</view>
        <view class="empty-desc">签到或购买商品后记录将显示在这里</view>
      </view>

      <view wx:else class="records-list">
        <view wx:for="{{records}}" wx:key="_id" class="record-item {{getTypeClass(item.type)}}">
          <view class="record-header">
            <view class="record-source">
              <text class="source-icon">{{getSourceIcon(item.source)}}</text>
              <text class="source-name">{{getSourceName(item.source)}}</text>
            </view>
            <view class="record-amount {{getTypeClass(item.type)}}">
              <text class="amount-sign">{{item.type === 'earn' ? '+' : '-'}}</text>
              <text class="amount-value">{{item.amount >= 0 ? item.amount : -item.amount}}</text>
            </view>
          </view>

          <view class="record-body">
            <view class="record-description">{{item.description}}</view>
            <view class="record-time">{{formatTime(item.timestamp)}}</view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{pagination.hasMore}}" class="load-more">
          <view wx:if="{{loading.more}}" class="loading-more">
            <view class="loading-icon">⏳</view>
            <view class="loading-text">加载更多...</view>
          </view>
          <view wx:else class="load-more-tip">上拉加载更多</view>
        </view>

        <view wx:else class="no-more">
          <view class="no-more-text">没有更多记录了</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 获取积分任务卡片 -->
  <view class="tasks-card">
    <view class="tasks-header">
      <view class="tasks-title">获取积分</view>
      <view class="tasks-subtitle">完成任务赚取积分</view>
    </view>

    <view class="tasks-content">
      <view wx:for="{{tasks}}" wx:key="id" class="task-item {{item.status}}">
        <view class="task-icon" style="background: {{item.iconColor}};">{{item.icon}}</view>
        <view class="task-info">
          <view class="task-name">{{item.name}}</view>
          <view class="task-description">{{item.description}}</view>
          <view class="task-progress" wx:if="{{item.progress}}">{{item.progress}}</view>
        </view>
        <view class="task-reward">
          <view class="reward-amount">+{{item.reward}}</view>
          <view class="reward-label">积分</view>
        </view>
        <view class="task-action">
          <button wx:if="{{item.status === 'available'}}"
                  class="action-btn primary"
                  bindtap="onTaskAction"
                  data-task="{{item.id}}">
            {{item.actionText}}
          </button>
          <button wx:elif="{{item.status === 'completed'}}"
                  class="action-btn completed"
                  disabled>
            已完成
          </button>
          <button wx:else
                  class="action-btn disabled"
                  disabled>
            {{item.actionText}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 积分来源统计图表 -->
  <view class="source-stats-card">
    <view class="stats-card-header">
      <view class="stats-card-title">积分来源统计</view>
    </view>

    <view wx:if="{{loading.sourceStats}}" class="loading-state">
      <view class="loading-icon">⏳</view>
      <view class="loading-text">加载统计中...</view>
    </view>

    <view wx:elif="{{sourceStatsData.length === 0}}" class="empty-state">
      <view class="empty-icon">📊</view>
      <view class="empty-text">暂无统计数据</view>
    </view>

    <view wx:else class="source-stats-content">
      <!-- 饼图区域 -->
      <view class="pie-chart-container">
        <view class="pie-chart" style="background: conic-gradient({{pieChartGradient}});">
          <view class="pie-chart-center">
            <view class="total-points">{{totalSourcePoints}}</view>
            <view class="total-label">总积分</view>
          </view>
        </view>
      </view>

      <!-- 图例 -->
      <view class="chart-legend">
        <view wx:for="{{sourceStatsData}}" wx:key="source" class="legend-item">
          <view class="legend-color" style="background-color: {{item.color}};"></view>
          <view class="legend-info">
            <view class="legend-name">{{item.name}}</view>
            <view class="legend-value">{{item.points}}积分 ({{item.percentage}}%)</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text wx:if="{{lastRefreshTimeText}}" class="refresh-time">
      上次刷新: {{lastRefreshTimeText}}
    </text>
    <text class="tip-text">下拉可刷新数据，上拉加载更多</text>
  </view>
</view>
