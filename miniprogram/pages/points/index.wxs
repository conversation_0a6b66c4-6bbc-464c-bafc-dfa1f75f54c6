/**
 * 积分页面工具函数 WXS
 */

/**
 * 获取来源图标
 */
function getSourceIcon(source) {
  var iconMap = {
    'check_in': '📅',
    'purchase': '🛒',
    'admin': '⚙️',
    'redeem': '🎫',
    'invite_friend': '👥',
    'invited_by_friend': '🎁'
  }
  return iconMap[source] || '💰'
}

/**
 * 获取来源名称
 */
function getSourceName(source) {
  var nameMap = {
    'check_in': '签到获得',
    'purchase': '商店消费',
    'admin': '管理员操作',
    'redeem': '兑换码使用',
    'invite_friend': '邀请好友',
    'invited_by_friend': '被邀请奖励'
  }
  return nameMap[source] || source
}

/**
 * 获取类型样式类
 */
function getTypeClass(type) {
  return type === 'earn' ? 'type-earn' : 'type-spend'
}

/**
 * 格式化时间为 YYYY-MM-DD HH:MM:SS 格式
 */
function formatTime(timestamp) {
  var date = getDate(timestamp)

  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var hours = date.getHours()
  var minutes = date.getMinutes()
  var seconds = date.getSeconds()

  // 补零函数
  function padZero(num) {
    return num < 10 ? '0' + num : num
  }

  return year + '-' + padZero(month) + '-' + padZero(day) + ' ' +
         padZero(hours) + ':' + padZero(minutes) + ':' + padZero(seconds)
}

module.exports = {
  getSourceIcon: getSourceIcon,
  getSourceName: getSourceName,
  getTypeClass: getTypeClass,
  formatTime: formatTime
}
