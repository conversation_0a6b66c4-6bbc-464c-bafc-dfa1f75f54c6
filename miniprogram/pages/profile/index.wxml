<!-- 个人页面 -->
<view class="profile-container">
  <!-- 用户核心信息区 -->
  <view class="section user-core-section">
    <!-- 用户基本信息 -->
    <view class="user-header" bindtap="onEditUserInfo">
      <view class="user-avatar">
        <image wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" class="avatar-image" />
        <view wx:else class="avatar-placeholder">{{userInfo.nickname.charAt(0)}}</view>
      </view>

      <view class="user-details">
        <view class="user-nickname">{{userInfo.nickname || '未设置昵称'}}</view>
        <view class="user-subtitle">ID: {{userInfo.no || '?'}}</view>
      </view>

      <view class="edit-icon">✏️</view>
    </view>

    <!-- 签到状态 -->
    <view class="check-in-section">
      <view wx:if="{{checkInStatus.loading}}" class="check-in-card loading">
        <view class="check-in-icon">⏳</view>
        <view class="check-in-info">
          <text class="check-in-title">加载中...</text>
        </view>
      </view>
      <view wx:elif="{{!checkInStatus.hasCheckedInToday}}" class="check-in-card unchecked" bindtap="onQuickCheckIn">
        <view class="check-in-icon">📅</view>
        <view class="check-in-info">
          <text class="check-in-title">今日未签到</text>
          <text class="check-in-desc">点击签到获取积分</text>
        </view>
        <view class="check-in-action">签到</view>
      </view>
      <view wx:else class="check-in-card checked" bindtap="onGoToCheckIn">
        <view class="check-in-icon">✅</view>
        <view class="check-in-info">
          <text class="check-in-title">已连续签到 {{checkInStatus.consecutiveDays}} 天</text>
          <text class="check-in-desc">点击查看签到详情</text>
        </view>
        <view class="setting-arrow">›</view>
      </view>
    </view>

    <!-- 工作统计概览 -->
    <view class="work-stats-overview">
      <view class="stats-title">今日工作概览</view>
      <view class="stats-grid">
        <view class="stat-card">
          <view class="stat-value">{{workStats.todayWorkHours}}h</view>
          <view class="stat-label">工作时长</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">¥{{workStats.todayIncome}}</view>
          <view class="stat-label">今日收入</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{workStats.weekWorkDays}}</view>
          <view class="stat-label">本周工作日</view>
        </view>
        <view class="stat-card">
          <view class="stat-value">{{workStats.currentStreak}}</view>
          <view class="stat-label">连续工作日</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据与成就区 -->
  <view class="section data-achievement-section">
    <view class="section-title">数据与成就</view>
    <view class="achievement-grid">
      <view class="achievement-item primary" bindtap="onViewPoints">
        <view class="achievement-icon">💎</view>
        <view class="achievement-info">
          <view class="achievement-value">{{userInfo.points || 0}}</view>
          <view class="achievement-label">积分余额</view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="achievement-item" bindtap="onShowRegistrationDays">
        <view class="achievement-icon">📅</view>
        <view class="achievement-info">
          <view class="achievement-value">{{userStats.registrationDays || 0}}天</view>
          <view class="achievement-label">已加入</view>
        </view>
      </view>

      <view class="achievement-item" bindtap="onViewRedemptionCodes">
        <view class="achievement-icon">🎫</view>
        <view class="achievement-info">
          <view class="achievement-value">{{userStats.redemptionCodes || 0}}</view>
          <view class="achievement-label">兑换码</view>
        </view>
        <view class="setting-arrow">›</view>
      </view>
    </view>

    <!-- 工作成就展示 -->
    <view wx:if="{{workStats.currentStreak > 0}}" class="work-achievement">
      <view class="achievement-badge">
        <view class="badge-icon">🔥</view>
        <view class="badge-text">连续工作 {{workStats.currentStreak}} 天</view>
      </view>
    </view>
  </view>

  <!-- 会员与权益区 -->
  <view class="section membership-section">
    <view class="section-title">会员与权益</view>

    <!-- VIP状态卡片 -->
    <view class="vip-status-card {{userInfo.vip.status ? 'vip' : 'free'}}" bindtap="onGoToMembershipPage">
      <view class="vip-icon">{{userInfo.vip.status ? '👑' : '🆓'}}</view>
      <view class="vip-info">
        <view class="vip-title">{{userInfo.vip.status ? 'VIP会员' : '免费用户'}}</view>
        <view class="vip-subtitle">
          <text wx:if="{{userInfo.vip.status}}">{{vipExpireText}}</text>
          <text wx:else>升级VIP享受更多权益</text>
        </view>
      </view>
      <view class="vip-action">
        <text wx:if="{{userInfo.vip.status}}">管理</text>
        <text wx:else>升级</text>
      </view>
    </view>

    <!-- 权益功能入口 -->
    <view class="benefits-grid">
      <view class="benefit-item" bindtap="onViewMembershipBenefits">
        <view class="benefit-icon">💎</view>
        <view class="benefit-label">会员权益</view>
      </view>

      <view class="benefit-item" bindtap="onGoToStore">
        <view class="benefit-icon">🛒</view>
        <view class="benefit-label">积分商店</view>
      </view>

      <view class="benefit-item" bindtap="onShowRedeemModal">
        <view class="benefit-icon">🎫</view>
        <view class="benefit-label">兑换码</view>
      </view>

      <view wx:if="{{!userInfo.vip.status}}" class="benefit-item upgrade" bindtap="onUpgradeMembership">
        <view class="benefit-icon">⭐</view>
        <view class="benefit-label">获取VIP</view>
      </view>
    </view>
  </view>

  <!-- 工具与设置区 -->
  <view class="section tools-settings-section">
    <view class="section-title">工具与设置</view>

    <!-- 常用工具 -->
    <view class="tools-grid">
      <view class="tool-item" bindtap="onManualSync">
        <view class="tool-icon">🔄</view>
        <view class="tool-label">数据同步</view>
      </view>

      <view class="tool-item" bindtap="onGoToFriendApps">
        <view class="tool-icon">📱</view>
        <view class="tool-label">友情应用</view>
      </view>
    </view>

    <!-- 设置选项 -->
    <view class="settings-list">
      <view class="setting-item">
        <view class="setting-left">
          <view class="setting-icon">💱</view>
          <view class="setting-info">
            <text class="setting-title">显示货币</text>
            <text class="setting-desc">收入金额显示的货币符号</text>
          </view>
        </view>
        <view class="setting-right">
          <picker mode="selector" range="{{currencyOptions}}" range-key="label" value="{{selectedCurrencyIndex}}" bindchange="onCurrencyChange">
            <view class="picker-value">{{currencyOptions[selectedCurrencyIndex].label}}</view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 应用信息 -->
    <view class="app-info-list">
      <view class="setting-item" bindtap="onViewChangelog">
        <view class="setting-left">
          <view class="setting-icon">📋</view>
          <view class="setting-info">
            <text class="setting-title">更新日志</text>
            <text class="setting-desc">查看应用版本更新记录</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onGoToFeedback">
        <view class="setting-left">
          <view class="setting-icon">💬</view>
          <view class="setting-info">
            <text class="setting-title">意见反馈</text>
            <text class="setting-desc">提交建议和问题反馈</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>

      <view class="setting-item" bindtap="onAbout">
        <view class="setting-left">
          <view class="setting-icon">📱</view>
          <view class="setting-info">
            <text class="setting-title">关于应用</text>
            <text class="setting-desc">版本信息和开发者信息</text>
          </view>
        </view>
        <view class="setting-arrow">›</view>
      </view>
    </view>
  </view>

  <!-- 用户信息编辑模态框 -->
  <view class="user-edit-modal {{showUserEditModal ? 'show' : ''}}" bindtap="onCloseUserEditModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">编辑个人信息</text>
        <view class="modal-close" bindtap="onCloseUserEditModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 头像编辑 -->
        <view class="section">
          <button class="modal-avatar" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image wx:if="{{tempUserInfo.avatar}}" src="{{tempUserInfo.avatar}}" class="modal-avatar-image" />
            <view wx:else class="modal-avatar-placeholder">{{tempUserInfo.nickname.charAt(0)}}</view>
            <view class="modal-avatar-overlay">
              <text class="modal-avatar-text">点击更换头像</text>
            </view>
          </button>
        </view>

        <!-- 昵称编辑 -->
        <view class="section">
          <input
            type="nickname"
            class="modal-nickname-input"
            placeholder="请输入昵称"
            value="{{tempUserInfo.nickname}}"
            bindinput="onTempNicknameInput"
            maxlength="20"
            focus="{{focusNickname}}"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="onCloseUserEditModal">取消</button>
        <button class="modal-btn confirm" bindtap="onSaveUserInfo">保存</button>
      </view>
    </view>
  </view>

  <!-- VIP记录模态框 -->
  <view class="vip-records-modal {{showVipRecordsModal ? 'show' : ''}}" bindtap="onCloseVipRecordsModal">
    <view class="modal-content" catchtap="onModalContentTap">
      <view class="modal-header">
        <text class="modal-title">VIP获取记录</text>
        <view class="modal-close" bindtap="onCloseVipRecordsModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 统计信息 -->
        <view wx:if="{{vipRecordsStats}}" class="records-stats">
          <view class="stats-item">
            <text class="stats-label">总获取天数</text>
            <text class="stats-value">{{vipRecordsStats.totalDays}}天</text>
          </view>
          <view class="stats-item">
            <text class="stats-label">获取次数</text>
            <text class="stats-value">{{vipRecordsStats.totalRecords}}次</text>
          </view>
        </view>

        <!-- 记录列表 -->
        <view class="records-list">
          <view wx:if="{{vipRecords.length === 0}}" class="empty-records">
            <text class="empty-text">暂无VIP获取记录</text>
          </view>

          <view wx:for="{{vipRecords}}" wx:key="_id" class="record-item">
            <view class="record-icon">
              <text class="icon-text">{{item.type === 'new_user' ? '🎁' : item.type === 'activation_code' ? '🔑' : item.type === 'watch_ad' ? '📺' : item.type === 'invite_friend' ? '👥' : '💎'}}</text>
            </view>

            <view class="record-content">
              <view class="record-title">{{item.description}}</view>
              <view class="record-details">
                <text class="record-date">{{item.createTimeText}}</text>
                <text class="record-days">+{{item.days}}天</text>
              </view>
              <view wx:if="{{item.source && item.source !== 'system' && item.source !== 'ad_reward'}}" class="record-source">
                <text class="source-text">来源：{{item.source}}</text>
              </view>
            </view>

            <view class="record-status">
              <text class="status-text {{item.status === 'active' ? 'active' : 'expired'}}">
                {{item.status === 'active' ? '有效' : '已过期'}}
              </text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{hasMoreVipRecords}}" class="load-more" bindtap="onLoadMoreVipRecords">
          <text class="load-more-text">加载更多</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换码使用模态框 -->
  <redeem-code-modal
    show="{{showRedeemModal}}"
    default-code="{{defaultRedeemCode}}"
    bind:success="onRedeemSuccess"
    bind:close="onCloseRedeemModal"
  />
</view>