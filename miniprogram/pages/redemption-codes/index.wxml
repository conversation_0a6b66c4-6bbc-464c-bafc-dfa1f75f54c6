<view class="redemption-codes-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">我的兑换码</view>
    <view class="header-subtitle">查看您创建和使用的兑换码记录</view>
  </view>

  <!-- 统计卡片 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-value">{{stats.totalCodes || 0}}</view>
        <view class="stat-label">总兑换码</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{stats.usedCodes || 0}}</view>
        <view class="stat-label">已使用</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{stats.activeCodes || 0}}</view>
        <view class="stat-label">可用</view>
      </view>
    </view>
  </view>

  <!-- 兑换码列表 -->
  <view class="codes-section">
    <view class="section-header">
      <view class="section-title">兑换码记录</view>
      <view class="filter-tabs">
        <view 
          class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="all">
          全部
        </view>
        <view 
          class="filter-tab {{currentFilter === 'active' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="active">
          可用
        </view>
        <view 
          class="filter-tab {{currentFilter === 'used' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="used">
          已使用
        </view>
        <view 
          class="filter-tab {{currentFilter === 'expired' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="expired">
          已过期
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 兑换码列表 -->
    <view wx:elif="{{filteredCodes.length > 0}}" class="codes-list">
      <view wx:for="{{filteredCodes}}" wx:key="_id" class="code-item">
        <view class="code-header">
          <view class="code-value">{{item.code}}</view>
          <view class="code-status {{item.status}}">
            <text wx:if="{{item.status === 'active'}}">可用</text>
            <text wx:elif="{{item.status === 'used'}}">已使用</text>
            <text wx:else>已过期</text>
          </view>
        </view>
        
        <view class="code-details">
          <view class="code-info">
            <text class="info-label">VIP天数：</text>
            <text class="info-value">{{item.days}}天</text>
          </view>
          <view class="code-info">
            <text class="info-label">创建时间：</text>
            <text class="info-value">{{item.createTimeText}}</text>
          </view>
          <view wx:if="{{item.expireDate}}" class="code-info">
            <text class="info-label">过期时间：</text>
            <text class="info-value">{{item.expireDateText}}</text>
          </view>
          <view wx:if="{{item.usedAt}}" class="code-info">
            <text class="info-label">使用时间：</text>
            <text class="info-value">{{item.usedAtText}}</text>
          </view>
          <view wx:if="{{item.usedBy && item.usedBy !== item.createdBy}}" class="code-info">
            <text class="info-label">使用者：</text>
            <text class="info-value">{{item.usedByText}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="code-actions">
          <view 
            wx:if="{{item.status === 'active'}}"
            class="action-btn copy-btn"
            bindtap="onCopyCode"
            data-code="{{item.code}}">
            复制兑换码
          </view>
          <view 
            wx:if="{{item.status === 'active'}}"
            class="action-btn use-btn"
            bindtap="onUseCode"
            data-code="{{item.code}}">
            立即使用
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-icon">📋</view>
      <view class="empty-title">暂无兑换码记录</view>
      <view class="empty-subtitle">
        <text wx:if="{{currentFilter === 'all'}}">您还没有创建或使用过兑换码</text>
        <text wx:elif="{{currentFilter === 'active'}}">暂无可用的兑换码</text>
        <text wx:elif="{{currentFilter === 'used'}}">暂无已使用的兑换码</text>
        <text wx:else>暂无已过期的兑换码</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && !loading}}" class="load-more" bindtap="onLoadMore">
      <text class="load-more-text">加载更多</text>
    </view>
  </view>
</view>
