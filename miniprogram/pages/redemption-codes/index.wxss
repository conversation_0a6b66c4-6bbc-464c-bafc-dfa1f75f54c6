page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 兑换码页面样式 */
.redemption-codes-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4fff7 0%, #d8e8f8 100%);
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 32rpx;
}

.stats-grid {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1rpx;
  background: rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

/* 兑换码列表区域 */
.codes-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 32rpx 32rpx 0;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.filter-tab:active {
  transform: scale(0.95);
}

/* 兑换码列表 */
.codes-list {
  padding: 0 32rpx 32rpx;
}

.code-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 32rpx 0;
}

.code-item:last-child {
  border-bottom: none;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.code-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.code-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.code-status.active {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.code-status.used {
  background: #6c757d;
  color: white;
}

.code-status.expired {
  background: #dc3545;
  color: white;
}

/* 兑换码详情 */
.code-details {
  margin-bottom: 16rpx;
}

.code-info {
  display: flex;
  margin-bottom: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 26rpx;
  color: #1a1a1a;
  flex: 1;
}

/* 操作按钮 */
.code-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.copy-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.use-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 加载状态 */
.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 加载更多 */
.load-more {
  padding: 32rpx;
  text-align: center;
  cursor: pointer;
}

.load-more-text {
  font-size: 28rpx;
  color: #667eea;
  font-weight: 600;
}
