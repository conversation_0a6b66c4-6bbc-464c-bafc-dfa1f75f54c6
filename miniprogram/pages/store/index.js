// 积分商店页面
import { api } from '../../core/api/index.js'
import { formatRefreshTime } from '../../utils/time-utils.js'

Page({
  data: {
    // 当前积分
    currentPoints: 0,

    // 商店商品
    storeItems: [],

    // 是否为测试用户
    isTestUser: false,

    // 加载状态
    loading: {
      items: false,
      purchase: false
    },

    // 我的兑换码
    myCodes: [],

    // 当前选中的标签页
    activeTab: 'store', // 'store' | 'codes'

    // 兑换码模态框
    showRedeemModal: false,
    defaultRedeemCode: '',

    // 新增：缓存和调试信息
    cacheStats: null,
    lastRefreshTime: null,
    lastRefreshTimeText: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('积分商店页面加载')
    this.loadStoreItems()
    this.loadMyCodes()
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('积分商店页面显示')
    // 刷新数据
    this.loadStoreItems()
    this.loadMyCodes()
  },

  /**
   * 加载商店商品（使用新API系统）
   */
  async loadStoreItems() {
    try {
      this.setData({
        'loading.items': true
      })

      console.log('[Store] 开始加载商店商品')

      // 使用新的API系统，带缓存
      const result = await api.store.getStoreItems({
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const now = Date.now()
        this.setData({
          storeItems: result.data.items,
          currentPoints: result.data.currentPoints,
          isTestUser: result.data.isTestUser || false,
          lastRefreshTime: now,
          lastRefreshTimeText: formatRefreshTime(now)
        })
        console.log(`[Store] 商店商品加载成功: ${result.data.items.length}个商品`)
      } else {
        throw new Error(result.message || '加载商品失败')
      }
    } catch (error) {
      console.error('[Store] 加载商店商品失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.items': false
      })
    }
  },

  /**
   * 加载我的兑换码（使用新API系统）
   */
  async loadMyCodes() {
    try {
      console.log('[Store] 开始加载我的兑换码')

      // 使用新的API系统，带缓存
      const result = await api.store.getMyRedemptionCodes({ limit: 20 }, {
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        this.setData({
          myCodes: result.data.codes
        })
        console.log(`[Store] 兑换码加载成功: ${result.data.codes.length}个兑换码`)
      } else {
        throw new Error(result.message || '加载兑换码失败')
      }
    } catch (error) {
      console.error('[Store] 加载兑换码失败:', error)
      // 兑换码加载失败不影响主要功能，只记录错误
    }
  },



  /**
   * 购买商品
   */
  async onPurchaseItem(event) {
    const dataset = event.currentTarget.dataset
    const itemId = dataset.itemId

    if (!itemId) {
      wx.showToast({
        title: '商品ID获取失败',
        icon: 'none'
      })
      return
    }

    const item = this.data.storeItems.find(item => item.id === itemId)

    if (!item) {
      wx.showToast({
        title: '商品不存在',
        icon: 'none'
      })
      return
    }

    // 检查积分余额
    if (this.data.currentPoints < item.pointsCost) {
      wx.showToast({
        title: '积分余额不足',
        icon: 'none'
      })
      return
    }

    // 确认购买
    const confirmResult = await this.showPurchaseConfirm(item)
    if (!confirmResult) {
      return
    }

    try {
      this.setData({
        'loading.purchase': true
      })

      console.log(`[Store] 开始购买商品: ${itemId}`)

      // 使用新的API系统，自动处理重试和缓存清理
      const result = await api.store.purchaseItem(itemId, {
        showLoading: false,  // 使用页面自定义loading
        showError: false     // 使用页面自定义错误处理
      })

      if (result.success) {
        const data = result.data
        console.log(`[Store] 购买成功: ${data.redemptionCode}`)

        // 显示购买成功
        this.showPurchaseSuccess(data)

        // 刷新数据（缓存已自动清理）
        this.loadStoreItems()
        this.loadMyCodes()

      } else {
        throw new Error(result.message || '购买失败')
      }
    } catch (error) {
      console.error('[Store] 购买失败:', error)
      wx.showToast({
        title: error.message || '网络异常，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        'loading.purchase': false
      })
    }
  },

  /**
   * 显示购买确认对话框
   */
  showPurchaseConfirm(item) {
    return new Promise((resolve) => {
      wx.showModal({
        title: '确认购买',
        content: `确定要花费 ${item.pointsCost} 积分购买 ${item.name} 吗？`,
        confirmText: '确认购买',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  /**
   * 显示购买成功
   */
  showPurchaseSuccess(data) {
    const { item, redemptionCode } = data
    
    wx.showModal({
      title: '购买成功！',
      content: `已获得 ${item.name}\n兑换码：${redemptionCode.code}\n\n兑换码已保存到"我的兑换码"中`,
      showCancel: false,
      confirmText: '查看兑换码',
      success: () => {
        this.setData({
          activeTab: 'codes'
        })
      }
    })
  },

  /**
   * 切换标签页
   */
  onTabChange(event) {
    const { tab } = event.currentTarget.dataset
    this.setData({
      activeTab: tab
    })
  },

  /**
   * 复制兑换码
   */
  onCopyCode(event) {
    const { code } = event.currentTarget.dataset
    
    wx.setClipboardData({
      data: code,
      success: () => {
        wx.showToast({
          title: '兑换码已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 使用兑换码
   */
  onUseCode(event) {
    const { code } = event.currentTarget.dataset

    this.setData({
      showRedeemModal: true,
      defaultRedeemCode: code || ''
    })
  },

  /**
   * 查看积分记录
   */
  onViewPointsRecords() {
    wx.navigateTo({
      url: '/pages/points/index'
    })
  },



  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'active': '未使用',
      'used': '已使用',
      'expired': '已过期'
    }
    return statusMap[status] || '未知'
  },

  /**
   * 获取状态样式类
   */
  getStatusClass(status) {
    const classMap = {
      'active': 'status-active',
      'used': 'status-used',
      'expired': 'status-expired'
    }
    return classMap[status] || 'status-unknown'
  },

  /**
   * 关闭兑换码模态框
   */
  onCloseRedeemModal() {
    this.setData({
      showRedeemModal: false,
      defaultRedeemCode: ''
    })
  },

  /**
   * 兑换成功回调
   */
  onRedeemSuccess(event) {
    const { code, data } = event.detail
    console.log('兑换成功:', code, data)

    // 刷新商店数据
    this.loadStoreItems()
    this.loadMyCodes()

    // 关闭模态框
    this.onCloseRedeemModal()
  },

  /**
   * 刷新所有数据（清除缓存）
   */
  async onRefreshAll() {
    try {
      console.log('[Store] 手动刷新所有数据')

      // 清除商店相关的所有缓存
      api.store.clearStoreCache('all')

      // 重新加载所有数据
      await Promise.all([
        this.loadStoreItems(),
        this.loadMyCodes()
      ])

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('[Store] 刷新失败:', error)
      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 获取缓存统计信息（调试用）
   */
  onShowCacheStats() {
    const stats = api.getStats()

    const message = `缓存统计:
命中率: ${stats.cache.hitRate}
缓存数量: ${stats.cache.size}
命中次数: ${stats.cache.hits}
未命中次数: ${stats.cache.misses}`

    wx.showModal({
      title: '缓存统计信息',
      content: message,
      showCancel: false
    })
  },

  /**
   * 页面卸载时清理资源
   */
  onUnload() {
    console.log('[Store] 页面卸载')
    // 可以在这里做一些清理工作
  }
})
