// 工作履历页面逻辑

Page({
  data: {
    // 工作履历列表
    workHistoryList: [],
    currentWorkId: null,

    // 折叠面板状态
    expandedItems: {},

    // 模态框状态
    showModal: false,
    editingWorkId: ''
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('工作履历页面加载开始')
    
    // 初始化服务
    this.workHistoryService = getApp().getWorkHistoryService()
    
    // 获取全局数据管理器
    this.dataManager = getApp().getDataManager()
    
    // 加载工作履历列表
    this.loadWorkHistoryList()
    
    // 注册数据变化监听器
    this.registerDataChangeListener()
    
    console.log('工作履历页面加载完成')
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('工作履历页面显示')
    
    // 刷新数据
    this.loadWorkHistoryList()
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('工作履历页面卸载')
    
    // 移除数据变化监听器
    this.unregisterDataChangeListener()
  },

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = (userData) => {
      console.log('收到数据变化通知，刷新工作履历列表')
      this.loadWorkHistoryList()
    }
    
    this.dataManager.addChangeListener(this.dataChangeListener)
  },

  /**
   * 移除数据变化监听器
   */
  unregisterDataChangeListener() {
    if (this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }
  },

  /**
   * 加载工作履历列表
   */
  loadWorkHistoryList() {
    try {
      const workHistoryList = this.workHistoryService.getAllWorkHistory()
      const currentWorkId = this.workHistoryService.getCurrentWorkId()

      // 为列表数据添加显示信息
      const processedList = workHistoryList.map(work => {
        return Object.assign({}, work, {
          displayName: this.workHistoryService.getWorkDisplayName(work),
          status: this.workHistoryService.getWorkStatus(work),
          timeRangeText: this.workHistoryService.getWorkTimeRangeText(work),
          isCurrent: work.id === currentWorkId,
          // 新增字段
          workDurationText: this.calculateWorkDuration(work),
          probationEndDateText: this.formatDateText(work.probationEndDate),
          createTimeText: this.formatDateText(work.createTime),
          updateTimeText: this.formatDateText(work.updateTime)
        })
      })

      this.setData({
        workHistoryList: processedList,
        currentWorkId
      })

      console.log(`工作履历列表加载完成，共${processedList.length}条记录`)
    } catch (error) {
      console.error('加载工作履历失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  /**
   * 计算工作时长
   */
  calculateWorkDuration(work) {
    if (!work || !work.startDate) {
      return '未知'
    }

    const startDate = new Date(work.startDate)
    const endDate = work.endDate ? new Date(work.endDate) : new Date()

    const diffTime = endDate.getTime() - startDate.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 30) {
      return `${diffDays}天`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      const remainingDays = diffDays % 30
      return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingDays = diffDays % 365
      const months = Math.floor(remainingDays / 30)

      let result = `${years}年`
      if (months > 0) {
        result += `${months}个月`
      }
      return result
    }
  },

  /**
   * 格式化日期文本
   */
  formatDateText(date) {
    if (!date) {
      return ''
    }

    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')

    return `${year}-${month}-${day}`
  },

  /**
   * 切换折叠面板
   */
  toggleExpanded(e) {
    const workId = e.currentTarget.dataset.workId
    const expandedItems = { ...this.data.expandedItems }
    expandedItems[workId] = !expandedItems[workId]
    
    this.setData({ expandedItems })
  },

  /**
   * 显示添加工作履历模态框
   */
  showAddModal() {
    this.setData({
      showModal: true,
      editingWorkId: ''
    })
  },

  /**
   * 显示编辑工作履历模态框
   */
  showEditWorkModal(e) {
    const workId = e.currentTarget.dataset.workId
    console.log('[work-history-page] showEditWorkModal called with workId:', workId)

    this.setData({
      showModal: true,
      editingWorkId: workId
    })
  },

  /**
   * 模态框关闭事件
   */
  onModalClose() {
    this.setData({
      showModal: false,
      editingWorkId: ''
    })
  },

  /**
   * 设置为当前工作
   */
  setCurrentWork(e) {
    const workId = e.currentTarget.dataset.workId
    
    try {
      this.workHistoryService.setCurrentWork(workId)
      wx.showToast({
        title: '设置成功',
        icon: 'success'
      })
      // 数据变化监听器会自动刷新列表
    } catch (error) {
      console.error('设置当前工作失败:', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  /**
   * 删除工作履历
   */
  deleteWorkHistory(e) {
    const workId = e.currentTarget.dataset.workId
    const work = this.workHistoryService.getWorkHistory(workId)
    
    if (!work) {
      wx.showToast({
        title: '工作履历不存在',
        icon: 'none'
      })
      return
    }
    
    const workDisplayName = this.workHistoryService.getWorkDisplayName(work)
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${workDisplayName}"的工作履历吗？\n\n删除后该工作的所有时间追踪数据也将被清除，此操作无法撤销。`,
      confirmText: '删除',
      confirmColor: '#FF3B30',
      success: (res) => {
        if (res.confirm) {
          try {
            this.workHistoryService.deleteWorkHistory(workId)
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
            // 数据变化监听器会自动刷新列表
          } catch (error) {
            console.error('删除工作履历失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },
})