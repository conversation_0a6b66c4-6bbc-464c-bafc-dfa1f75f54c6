<!-- 工作履历页面 -->
<view class="work-history-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">工作履历管理</text>
      <text class="header-subtitle">管理您的工作经历，设置当前工作</text>
    </view>
    <view class="header-actions">
      <button class="add-btn" bindtap="showAddModal">
        <text class="add-icon">+</text>
        <text>添加履历</text>
      </button>
    </view>
  </view>

  <!-- 工作履历列表 -->
  <view class="work-list">
    <view wx:if="{{workHistoryList.length === 0}}" class="empty-state">
      <view class="empty-icon">💼</view>
      <text class="empty-text">暂无工作履历</text>
      <text class="empty-subtitle">点击上方按钮添加您的第一份工作履历</text>
    </view>
    
    <view wx:else>
      <view
        wx:for="{{workHistoryList}}"
        wx:key="id"
        class="modern-work-card {{item.isCurrent ? 'current-work' : ''}}"
      >
        <!-- 卡片头部 -->
        <view class="card-header" data-work-id="{{item.id}}" bindtap="toggleExpanded">
          <view class="header-left">
            <!-- 公司信息 -->
            <view class="company-section">
              <view class="company-name-row">
                <text class="company-name">{{item.company}}</text>
                <view wx:if="{{item.isCurrent}}" class="current-indicator">
                  <view class="current-dot"></view>
                  <text class="current-label">当前</text>
                </view>
              </view>
              <text class="position-title">{{item.position}}</text>
            </view>

            <!-- 快速信息 -->
            <view class="quick-info">
              <view class="info-chip status-chip status-{{item.status}}">
                <view class="status-indicator"></view>
                <text class="chip-text">{{item.status === 'active' ? '在职' : '离职'}}</text>
              </view>
              <view class="info-chip">
                <view class="chip-icon">📅</view>
                <text class="chip-text">{{item.timeRangeText}}</text>
              </view>
            </view>
          </view>

          <!-- 展开按钮 -->
          <view class="expand-button {{expandedItems[item.id] ? 'expanded' : ''}}">
            <text class="expand-icon-text">▶</text>
          </view>
        </view>

        <!-- 可折叠的详细内容 -->
        <view class="card-content {{expandedItems[item.id] ? 'expanded' : 'collapsed'}}">
          <view class="content-inner">

            <!-- 工作时长统计 -->
            <view class="duration-section">
              <view class="section-header">
                <view class="section-icon">⏱️</view>
                <text class="section-title">工作时长</text>
              </view>
              <view class="duration-content">
                <text class="duration-text">{{item.workDurationText || '计算中...'}}</text>
              </view>
            </view>

            <!-- 薪资信息 -->
            <view wx:if="{{item.probationSalary > 0 || item.formalSalary > 0}}" class="salary-section">
              <view class="section-header">
                <view class="section-icon">💰</view>
                <text class="section-title">薪资信息</text>
              </view>
              <view class="salary-grid">
                <view wx:if="{{item.probationSalary > 0}}" class="salary-card probation">
                  <view class="salary-header">
                    <text class="salary-label">试用期薪资</text>
                    <view wx:if="{{item.probationEndDate}}" class="salary-period">
                      <text class="period-text">至 {{item.probationEndDateText}}</text>
                    </view>
                  </view>
                  <text class="salary-amount">¥{{item.probationSalary}}</text>
                </view>

                <view wx:if="{{item.formalSalary > 0}}" class="salary-card formal">
                  <view class="salary-header">
                    <text class="salary-label">正式薪资</text>
                    <view wx:if="{{item.probationEndDate}}" class="salary-period">
                      <text class="period-text">{{item.probationEndDateText}} 起</text>
                    </view>
                  </view>
                  <text class="salary-amount">¥{{item.formalSalary}}</text>
                </view>
              </view>
            </view>

            <!-- 发薪日设置 -->
            <view wx:if="{{item.payDays && item.payDays.length > 0}}" class="paydays-section">
              <view class="section-header">
                <view class="section-icon">📅</view>
                <text class="section-title">发薪日设置</text>
              </view>
              <view class="paydays-list">
                <view wx:for="{{item.payDays}}" wx:key="day" class="payday-item">
                  <view class="payday-day">{{item.day}}日</view>
                  <text class="payday-name">{{item.name}}</text>
                </view>
              </view>
            </view>

            <!-- 备注信息 -->
            <view wx:if="{{item.notes}}" class="notes-section">
              <view class="section-header">
                <view class="section-icon">📝</view>
                <text class="section-title">备注</text>
              </view>
              <view class="notes-content">{{item.notes}}</view>
            </view>

            <!-- 元数据信息 -->
            <view class="metadata-section">
              <view class="metadata-row">
                <view class="metadata-item">
                  <view class="metadata-icon">📅</view>
                  <view class="metadata-content">
                    <text class="metadata-label">创建时间</text>
                    <text class="metadata-value">{{item.createTimeText}}</text>
                  </view>
                </view>
                <view wx:if="{{item.updateTimeText !== item.createTimeText}}" class="metadata-item">
                  <view class="metadata-icon">🔄</view>
                  <view class="metadata-content">
                    <text class="metadata-label">更新时间</text>
                    <text class="metadata-value">{{item.updateTimeText}}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 操作按钮组 -->
            <view class="action-group">
              <button
                wx:if="{{!item.isCurrent}}"
                class="action-btn primary-btn"
                data-work-id="{{item.id}}"
                bindtap="setCurrentWork"
              >
                <view class="btn-icon">⭐</view>
                <text class="btn-text">设为当前</text>
              </button>

              <button
                class="action-btn secondary-btn"
                data-work-id="{{item.id}}"
                bindtap="showEditWorkModal"
              >
                <view class="btn-icon">✏️</view>
                <text class="btn-text">编辑</text>
              </button>

              <button
                class="action-btn danger-btn"
                data-work-id="{{item.id}}"
                bindtap="deleteWorkHistory"
              >
                <view class="btn-icon">🗑️</view>
                <text class="btn-text">删除</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 工作履历模态框组件 -->
<work-history-modal
  visible="{{showModal}}"
  editingWorkId="{{editingWorkId}}"
  bind:close="onModalClose"
/>