<!-- 工作履历页面 -->
<view class="work-history-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="header-title">工作履历管理</text>
      <text class="header-subtitle">管理您的工作经历，设置当前工作</text>
    </view>
    <view class="header-actions">
      <button class="add-btn" bindtap="showAddModal">
        <text class="add-icon">+</text>
        <text>添加履历</text>
      </button>
    </view>
  </view>

  <!-- 工作履历列表 -->
  <view class="work-list">
    <view wx:if="{{workHistoryList.length === 0}}" class="empty-state">
      <view class="empty-icon">💼</view>
      <text class="empty-text">暂无工作履历</text>
      <text class="empty-subtitle">点击上方按钮添加您的第一份工作履历</text>
    </view>
    
    <view wx:else>
      <view
        wx:for="{{workHistoryList}}"
        wx:key="id"
        class="modern-work-card {{item.isCurrent ? 'current-work' : ''}}"
      >
        <!-- 卡片头部 -->
        <view class="card-header" data-work-id="{{item.id}}" bindtap="toggleExpanded">
          <view class="header-left">
            <!-- 公司信息 -->
            <view class="company-section">
              <view class="company-name-row">
                <text class="company-name">{{item.company}}</text>
                <view wx:if="{{item.isCurrent}}" class="current-indicator">
                  <view class="current-dot"></view>
                  <text class="current-label">当前</text>
                </view>
              </view>
              <text class="position-title">{{item.position}}</text>
            </view>

            <!-- 快速信息 -->
            <view class="quick-info">
              <view class="info-chip status-chip status-{{item.status}}">
                <view class="status-indicator"></view>
                <text class="chip-text">{{item.status === 'active' ? '在职' : '离职'}}</text>
              </view>
              <view class="info-chip">
                <view class="chip-icon">📅</view>
                <text class="chip-text">{{item.timeRangeText}}</text>
              </view>
            </view>
          </view>

          <!-- 展开按钮 -->
          <view class="expand-button {{expandedItems[item.id] ? 'expanded' : ''}}">
            <text class="expand-icon-text">▶</text>
          </view>
        </view>

        <!-- 展开详情内容 -->
        <view class="details-panel {{expandedItems[item.id] ? 'show' : 'hide'}}">
          <view class="panel-content">

            <!-- 工作时长展示 -->
            <view class="duration-block">
              <view class="block-title">
                <text class="title-icon">⏱️</text>
                <text class="title-text">工作时长</text>
              </view>
              <view class="duration-display">
                <text class="duration-value">{{item.workDurationText || '计算中...'}}</text>
              </view>
            </view>

            <!-- 薪资详情 -->
            <view wx:if="{{item.probationSalary > 0 || item.formalSalary > 0}}" class="salary-block">
              <view class="block-title">
                <text class="title-icon">💰</text>
                <text class="title-text">薪资详情</text>
              </view>
              <view class="salary-container">
                <view wx:if="{{item.probationSalary > 0}}" class="salary-item trial">
                  <view class="salary-info">
                    <text class="salary-type">试用期</text>
                    <view wx:if="{{item.probationEndDate}}" class="salary-duration">
                      <text class="duration-label">至 {{item.probationEndDateText}}</text>
                    </view>
                  </view>
                  <text class="salary-number">¥{{item.probationSalary}}</text>
                </view>

                <view wx:if="{{item.formalSalary > 0}}" class="salary-item official">
                  <view class="salary-info">
                    <text class="salary-type">正式</text>
                    <view wx:if="{{item.probationEndDate}}" class="salary-duration">
                      <text class="duration-label">{{item.probationEndDateText}} 起</text>
                    </view>
                  </view>
                  <text class="salary-number">¥{{item.formalSalary}}</text>
                </view>
              </view>
            </view>

            <!-- 发薪日配置 -->
            <view wx:if="{{item.payDays && item.payDays.length > 0}}" class="paydays-block">
              <view class="block-title">
                <text class="title-icon">📅</text>
                <text class="title-text">发薪日</text>
              </view>
              <view class="paydays-container">
                <view wx:for="{{item.payDays}}" wx:key="day" class="payday-tag">
                  <text class="payday-date">{{item.day}}日</text>
                  <text class="payday-label">{{item.name}}</text>
                </view>
              </view>
            </view>

            <!-- 工作备注 -->
            <view wx:if="{{item.notes}}" class="notes-block">
              <view class="block-title">
                <text class="title-icon">📝</text>
                <text class="title-text">备注</text>
              </view>
              <view class="notes-text">{{item.notes}}</view>
            </view>

            <!-- 记录信息 -->
            <view class="record-block">
              <view class="record-items">
                <view class="record-item">
                  <text class="record-icon">📅</text>
                  <view class="record-info">
                    <text class="record-label">创建</text>
                    <text class="record-time">{{item.createTimeText}}</text>
                  </view>
                </view>
                <view wx:if="{{item.updateTimeText !== item.createTimeText}}" class="record-item">
                  <text class="record-icon">🔄</text>
                  <view class="record-info">
                    <text class="record-label">更新</text>
                    <text class="record-time">{{item.updateTimeText}}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="operations-block">
              <button
                wx:if="{{!item.isCurrent}}"
                class="operation-btn primary"
                data-work-id="{{item.id}}"
                bindtap="setCurrentWork"
              >
                <text class="btn-icon">⭐</text>
                <text class="btn-label">设为当前</text>
              </button>

              <button
                class="operation-btn secondary"
                data-work-id="{{item.id}}"
                bindtap="showEditWorkModal"
              >
                <text class="btn-icon">✏️</text>
                <text class="btn-label">编辑</text>
              </button>

              <button
                class="operation-btn danger"
                data-work-id="{{item.id}}"
                bindtap="deleteWorkHistory"
              >
                <text class="btn-icon">🗑️</text>
                <text class="btn-label">删除</text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 工作履历模态框组件 -->
<work-history-modal
  visible="{{showModal}}"
  editingWorkId="{{editingWorkId}}"
  bind:close="onModalClose"
/>