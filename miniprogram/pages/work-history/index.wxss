page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 工作履历页面样式 */
.work-history-page {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.header-actions {
  margin-left: 20rpx;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.add-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
  font-weight: 300;
}

/* 工作履历列表 */
.work-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 现代化工作履历卡片 */
.modern-work-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.modern-work-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border-color: #e0e0e0;
}

.modern-work-card.current-work {
  border-left: 4rpx solid #4f46e5;
  background: linear-gradient(135deg, #fafaff 0%, #f8faff 100%);
}

.modern-work-card.current-work:hover {
  box-shadow: 0 8rpx 32rpx rgba(79, 70, 229, 0.12);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.card-header:active {
  background-color: #fafafa;
}

.header-left {
  flex: 1;
  min-width: 0;
}

/* 公司信息区域 */
.company-section {
  margin-bottom: 16rpx;
}

.company-name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 6rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
}

.current-indicator {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 4rpx 12rpx;
  background: #4f46e5;
  border-radius: 12rpx;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4rpx rgba(79, 70, 229, 0.1);
  }
}

.current-dot {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: pulse-dot 1.5s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.current-label {
  font-size: 20rpx;
  font-weight: 500;
  color: #ffffff;
}

.position-title {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.4;
}

/* 快速信息标签 */
.quick-info {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.info-chip {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 20rpx;
  transition: all 0.2s ease;
}

.info-chip:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.chip-icon {
  font-size: 16rpx;
  line-height: 1;
}

.chip-text {
  font-size: 22rpx;
  color: #475569;
  font-weight: 500;
  line-height: 1;
}

.salary-chip {
  background: #fef3e2;
  border-color: #fed7aa;
}

.salary-chip .chip-text {
  color: #ea580c;
}

.status-chip {
  position: relative;
}

.status-chip.status-active {
  background: #f0fdf4;
  border-color: #bbf7d0;
}

.status-chip.status-active .chip-text {
  color: #16a34a;
}

.status-chip.status-inactive {
  background: #fef2f2;
  border-color: #fecaca;
}

.status-chip.status-inactive .chip-text {
  color: #dc2626;
}

.status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 4rpx;
}

.status-chip.status-active .status-indicator {
  background: #16a34a;
  animation: pulse-green 2s infinite;
}

.status-chip.status-inactive .status-indicator {
  background: #dc2626;
}

@keyframes pulse-green {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 现代化展开按钮 */
.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.expand-button:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: scale(1.05);
}

.expand-button.expanded {
  background: #4f46e5;
  border-color: #4f46e5;
}

.expand-button.expanded:hover {
  background: #4338ca;
  transform: scale(1.05);
}

/* 使用Unicode字符的展开图标 */
.expand-icon-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: normal;
  line-height: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* 展开时旋转90度：▶ 变成 ▼ */
.expand-button.expanded .expand-icon-text {
  transform: rotate(90deg);
  color: #ffffff;
}

/* 卡片内容区域 */
.card-content {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #fafbff 0%, #f8faff 100%);
  border-top: 1rpx solid #e8ecf7;
}

.card-content.collapsed {
  max-height: 0;
  opacity: 0;
}

.card-content.expanded {
  max-height: 2000rpx;
  opacity: 1;
}

.content-inner {
  padding: 32rpx 24rpx;
}

/* 通用区域样式 */
.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.section-icon {
  font-size: 24rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8rpx;
  color: white;
  font-size: 18rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: 0.5rpx;
}

/* 工作时长区域 */
.duration-section {
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bae6fd;
}

.duration-content {
  padding-left: 44rpx;
}

.duration-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #0369a1;
  text-shadow: 0 1rpx 2rpx rgba(3, 105, 161, 0.1);
}

/* 薪资信息区域 */
.salary-section {
  margin-bottom: 32rpx;
}

.salary-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-left: 44rpx;
}

.salary-card {
  padding: 20rpx;
  border-radius: 16rpx;
  border: 2rpx solid;
  position: relative;
  overflow: hidden;
}

.salary-card.probation {
  background: linear-gradient(135deg, #fef3e2 0%, #fffbeb 100%);
  border-color: #fed7aa;
}

.salary-card.formal {
  background: linear-gradient(135deg, #f0fdf4 0%, #f7fee7 100%);
  border-color: #bbf7d0;
}

.salary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.salary-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
}

.salary-period {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
}

.period-text {
  font-size: 20rpx;
  color: #6b7280;
  font-weight: 500;
}

.salary-amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #059669;
  text-shadow: 0 1rpx 2rpx rgba(5, 150, 105, 0.1);
}

/* 发薪日设置区域 */
.paydays-section {
  margin-bottom: 32rpx;
}

.paydays-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding-left: 44rpx;
}

.payday-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #fef7ff 0%, #fdf4ff 100%);
  border: 2rpx solid #e9d5ff;
  border-radius: 20rpx;
  min-width: 120rpx;
}

.payday-day {
  font-size: 24rpx;
  font-weight: 700;
  color: #7c3aed;
  background: rgba(124, 58, 237, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.payday-name {
  font-size: 22rpx;
  color: #6b46c1;
  font-weight: 500;
}

/* 备注区域 */
.notes-section {
  margin-bottom: 32rpx;
}

.notes-content {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  padding: 20rpx;
  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  margin-left: 44rpx;
  position: relative;
}

.notes-content::before {
  content: '';
  position: absolute;
  left: -2rpx;
  top: 20rpx;
  width: 4rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

/* 元数据区域 */
.metadata-section {
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
}

.metadata-row {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  min-width: 200rpx;
}

.metadata-icon {
  font-size: 20rpx;
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e2e8f0;
  border-radius: 6rpx;
  color: #64748b;
}

.metadata-content {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.metadata-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.metadata-value {
  font-size: 22rpx;
  color: #374151;
  font-weight: 600;
}

/* 现代化操作按钮组 */
.action-group {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  padding: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 20rpx;
  border: 2rpx solid #e8ecf7;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  flex: 1;
  min-width: 120rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.action-btn:active::before {
  left: 100%;
}

.btn-icon {
  font-size: 20rpx;
  line-height: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.btn-text {
  font-size: 24rpx;
  line-height: 1;
  letter-spacing: 0.5rpx;
}

/* 主要按钮 */
.primary-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(79, 70, 229, 0.3);
}

.primary-btn:active {
  box-shadow: 0 8rpx 24rpx rgba(79, 70, 229, 0.4);
  transform: scale(0.95) translateY(-1rpx);
}

/* 次要按钮 */
.secondary-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  color: #4f46e5;
  border: 2rpx solid #e0e7ff;
  box-shadow: 0 4rpx 12rpx rgba(79, 70, 229, 0.15);
}

.secondary-btn:active {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-color: #c7d2fe;
  box-shadow: 0 6rpx 16rpx rgba(79, 70, 229, 0.2);
  transform: scale(0.95) translateY(-1rpx);
}

/* 危险按钮 */
.danger-btn {
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
  color: #dc2626;
  border: 2rpx solid #fecaca;
  box-shadow: 0 4rpx 12rpx rgba(220, 38, 38, 0.15);
}

.danger-btn:active {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-color: #fca5a5;
  box-shadow: 0 6rpx 16rpx rgba(220, 38, 38, 0.2);
  transform: scale(0.95) translateY(-1rpx);
}
