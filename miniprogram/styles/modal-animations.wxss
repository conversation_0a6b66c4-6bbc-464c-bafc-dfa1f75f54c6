/* 统一的模态框动画样式 */

/* 基础动画关键帧 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8rpx);
  }
}

@keyframes modalFadeOut {
  from {
    opacity: 1;
    backdrop-filter: blur(8rpx);
  }
  to {
    opacity: 0;
    backdrop-filter: blur(0);
  }
}

@keyframes modalSlideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes modalSlideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100rpx);
    opacity: 0;
  }
}

@keyframes modalScaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modalScaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.9);
    opacity: 0;
  }
}

/* 通用模态框遮罩层样式 */
.modal-overlay-animated {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* 入场动画 */
  animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 出场动画类 */
.modal-overlay-animated.closing {
  animation: modalFadeOut 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 通用模态框内容样式 - 滑入效果 */
.modal-content-slide {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  
  /* 入场动画 */
  animation: modalSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 出场动画类 - 滑入效果 */
.modal-content-slide.closing {
  animation: modalSlideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 通用模态框内容样式 - 缩放效果 */
.modal-content-scale {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  
  /* 入场动画 */
  animation: modalScaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 出场动画类 - 缩放效果 */
.modal-content-scale.closing {
  animation: modalScaleOut 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 推荐的transition方式（统一风格） */
.modal-overlay-unified {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;

  /* 初始状态 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay-unified.show {
  opacity: 1;
  visibility: visible;
}

.modal-content-unified {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;

  /* 初始状态 */
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay-unified.show .modal-content-unified {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .modal-content-slide,
  .modal-content-scale,
  .modal-content-transition {
    margin: 20rpx;
    max-width: calc(100vw - 40rpx);
  }
}
