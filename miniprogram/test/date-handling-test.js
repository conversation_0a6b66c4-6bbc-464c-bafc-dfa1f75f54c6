/**
 * 日期处理测试
 * 验证收入调整服务的日期处理是否正确
 */

const incomeAdjustmentService = require('../core/services/income-adjustment-service.js')

/**
 * 测试日期处理功能
 */
function testDateHandling() {
  console.log('=== 开始测试日期处理功能 ===')

  try {
    // 测试各种日期格式
    const testDates = [
      new Date(),                           // 正常的 Date 对象
      new Date().toISOString(),            // ISO 字符串
      new Date().getTime(),                // 时间戳
      '2024-01-15',                        // 日期字符串
      { getTime: () => new Date().getTime() }, // 模拟序列化的 Date 对象
      null,                                // null 值
      undefined,                           // undefined 值
      'invalid-date',                      // 无效日期字符串
      {}                                   // 空对象
    ]

    testDates.forEach((testDate, index) => {
      console.log(`\n--- 测试日期格式 ${index + 1}: ${typeof testDate} ---`)
      console.log('输入:', testDate)

      try {
        const safeDate = incomeAdjustmentService.getSafeDate(testDate)
        console.log('输出:', safeDate)
        console.log('是否为有效日期:', safeDate instanceof Date && !isNaN(safeDate.getTime()))

        // 尝试使用这个日期添加收入调整
        const result = incomeAdjustmentService.addExtraIncome(
          testDate,
          'commission',
          100,
          `测试日期格式${index + 1}`
        )
        console.log('添加收入调整成功，ID:', result)

      } catch (error) {
        console.error('测试失败:', error.message)
      }
    })

    console.log('\n=== 日期处理功能测试完成 ===')

  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
}

/**
 * 测试模态框日期传递
 */
function testModalDatePassing() {
  console.log('\n=== 开始测试模态框日期传递 ===')

  // 模拟小程序组件属性传递
  const mockComponentData = {
    date: new Date()
  }

  // 模拟序列化过程（小程序中可能发生的情况）
  const serialized = JSON.stringify(mockComponentData)
  const deserialized = JSON.parse(serialized)

  console.log('原始日期:', mockComponentData.date)
  console.log('序列化后:', deserialized.date)
  console.log('类型变化:', typeof mockComponentData.date, '->', typeof deserialized.date)

  // 测试处理序列化后的日期
  try {
    const safeDate = incomeAdjustmentService.getSafeDate(deserialized.date)
    console.log('安全处理后:', safeDate)
    console.log('是否为有效日期:', safeDate instanceof Date && !isNaN(safeDate.getTime()))
  } catch (error) {
    console.error('处理序列化日期失败:', error)
  }

  console.log('\n=== 模态框日期传递测试完成 ===')
}

// 导出测试函数
module.exports = {
  testDateHandling,
  testModalDatePassing
}
