/**
 * 收入调整功能测试
 * 用于验证额外收入和扣款功能的正确性
 */

// 引入服务
const incomeAdjustmentService = require('../core/services/income-adjustment-service.js')
const dataManager = require('../core/managers/data-manager.js')

/**
 * 测试收入调整功能
 */
function testIncomeAdjustment() {
  console.log('=== 开始测试收入调整功能 ===')

  try {
    // 确保数据已加载
    dataManager.ensureLoaded().then(() => {
      console.log('数据管理器已加载')

      // 获取当前工作
      const currentWork = dataManager.getCurrentWork()
      if (!currentWork) {
        console.error('没有当前工作，无法进行测试')
        return
      }

      console.log('当前工作:', currentWork.companyName)

      // 测试日期
      const testDate = new Date()
      console.log('测试日期:', testDate.toDateString())

      // 测试1：添加额外收入
      console.log('\n--- 测试1：添加额外收入 ---')
      const incomeId = incomeAdjustmentService.addExtraIncome(
        testDate,
        'commission',
        500,
        '销售提成 - 客户A订单'
      )
      console.log('添加额外收入成功，ID:', incomeId)

      // 测试2：添加扣款
      console.log('\n--- 测试2：添加扣款 ---')
      const deductionId = incomeAdjustmentService.addDeduction(
        testDate,
        'late',
        50,
        '迟到30分钟'
      )
      console.log('添加扣款成功，ID:', deductionId)

      // 测试3：查询当日调整汇总
      console.log('\n--- 测试3：查询当日调整汇总 ---')
      const summary = incomeAdjustmentService.getDayAdjustmentSummary(testDate)
      console.log('当日调整汇总:', summary)

      // 测试4：查询类型定义
      console.log('\n--- 测试4：查询类型定义 ---')
      const incomeTypes = incomeAdjustmentService.getExtraIncomeTypes()
      const deductionTypes = incomeAdjustmentService.getDeductionTypes()
      console.log('额外收入类型:', incomeTypes)
      console.log('扣款类型:', deductionTypes)

      // 测试5：获取当日完整数据
      console.log('\n--- 测试5：获取当日完整数据 ---')
      const dayData = dataManager.getDayData(currentWork.id, testDate)
      console.log('当日完整数据:', {
        dailyIncome: dayData.dailyIncome,
        extraIncome: dayData.extraIncome,
        deductions: dayData.deductions,
        netIncome: dayData.netIncome,
        extraIncomeItems: dayData.extraIncomeItems,
        deductionItems: dayData.deductionItems
      })

      // 测试6：计算统计
      console.log('\n--- 测试6：计算统计 ---')
      const stats = incomeAdjustmentService.calculateAdjustmentStatistics()
      console.log('收入调整统计:', stats)

      console.log('\n=== 收入调整功能测试完成 ===')

    }).catch(error => {
      console.error('数据加载失败:', error)
    })

  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
}

/**
 * 测试数据迁移功能
 */
function testDataMigration() {
  console.log('\n=== 开始测试数据迁移功能 ===')

  try {
    // 模拟旧版本的dayData
    const oldDayData = {
      workDate: new Date(),
      dailyIncome: 200,
      segments: [],
      fishes: [],
      status: 'work',
      createTime: new Date(),
      updateTime: new Date()
      // 注意：没有新的字段
    }

    console.log('旧版本数据:', oldDayData)

    // 测试迁移
    const migratedData = dataManager.migrateDayData(oldDayData, new Date())
    console.log('迁移后数据:', migratedData)

    // 验证迁移结果
    const hasNewFields = (
      migratedData.extraIncome !== undefined &&
      migratedData.deductions !== undefined &&
      migratedData.netIncome !== undefined &&
      Array.isArray(migratedData.extraIncomeItems) &&
      Array.isArray(migratedData.deductionItems)
    )

    console.log('迁移是否成功:', hasNewFields)
    console.log('净收入计算是否正确:', migratedData.netIncome === migratedData.dailyIncome)

    console.log('\n=== 数据迁移功能测试完成 ===')

  } catch (error) {
    console.error('数据迁移测试失败:', error)
  }
}

// 导出测试函数
module.exports = {
  testIncomeAdjustment,
  testDataMigration
}
