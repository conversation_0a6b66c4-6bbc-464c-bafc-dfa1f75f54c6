/**
 * StatisticsService 测试文件
 * 用于验证统计服务的基本功能
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: () => null,
  setStorageSync: () => {},
  removeStorageSync: () => {}
}

// 引入需要测试的模块
const { StatisticsService } = require('../core/services/statistics-service.js')

/**
 * 创建测试数据
 */
function createTestData() {
  const today = new Date()
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  
  return {
    '2024-01-15': {
      workDate: new Date('2024-01-15'),
      segments: [
        {
          id: 1,
          start: 540, // 09:00
          end: 720,   // 12:00
          type: 'work',
          income: 150
        },
        {
          id: 2,
          start: 780, // 13:00
          end: 1080,  // 18:00
          type: 'work',
          income: 250
        }
      ],
      fishes: [
        {
          id: 1,
          start: 600, // 10:00
          end: 630,   // 10:30
          remark: '喝咖啡'
        }
      ]
    },
    '2024-01-16': {
      workDate: new Date('2024-01-16'),
      segments: [
        {
          id: 1,
          start: 540, // 09:00
          end: 720,   // 12:00
          type: 'work',
          income: 150
        },
        {
          id: 2,
          start: 780, // 13:00
          end: 900,   // 15:00
          type: 'work',
          income: 100
        },
        {
          id: 3,
          start: 1080, // 18:00
          end: 1200,   // 20:00
          type: 'overtime',
          income: 120
        }
      ],
      fishes: []
    }
  }
}

/**
 * 测试时间截止功能
 */
function testTimeCutoff() {
  console.log('=== 测试时间截止功能 ===')
  
  const service = new StatisticsService()
  const testDate = new Date('2024-01-15T10:30:00') // 10:30
  const segment = {
    id: 1,
    start: 540, // 09:00
    end: 720,   // 12:00
    type: 'work',
    income: 150
  }
  
  // 测试时间段是否在截止时间之前
  const isBeforeCutoff = service.isSegmentBeforeCutoff(testDate, segment, new Date('2024-01-15T11:00:00'))
  console.log('时间段是否在截止时间之前:', isBeforeCutoff) // 应该为 true
  
  // 测试计算有效时长
  const validDuration = service.calculateValidDuration(testDate, segment, new Date('2024-01-15T11:00:00'))
  console.log('有效时长（分钟）:', validDuration) // 应该为 120 (09:00-11:00)
  
  console.log('时间截止功能测试完成\n')
}

/**
 * 测试概览统计
 */
function testOverviewStatistics() {
  console.log('=== 测试概览统计 ===')
  
  const service = new StatisticsService()
  
  // 模拟 getFilteredDailyData 方法
  service.getFilteredDailyData = function() {
    return createTestData()
  }
  
  const overview = service.calculateOverviewStatistics()
  
  console.log('概览统计结果:')
  console.log('- 总工作时长（小时）:', overview.totalWorkHours)
  console.log('- 总收入:', overview.totalIncome)
  console.log('- 工作天数:', overview.workDays)
  console.log('- 平均时薪:', overview.averageHourlyRate)
  console.log('- 平均日收入:', overview.averageDailyIncome)
  
  console.log('概览统计测试完成\n')
}

/**
 * 测试时间类型分布
 */
function testTimeTypeStatistics() {
  console.log('=== 测试时间类型分布 ===')
  
  const service = new StatisticsService()
  
  // 模拟 getFilteredDailyData 方法
  service.getFilteredDailyData = function() {
    return createTestData()
  }
  
  const timeType = service.calculateTimeTypeStatistics()
  
  console.log('时间类型分布结果:')
  console.log('- 工作时间:', timeType.work.hours, '小时,', timeType.work.percentage, '%')
  console.log('- 加班时间:', timeType.overtime.hours, '小时,', timeType.overtime.percentage, '%')
  console.log('- 休息时间:', timeType.rest.hours, '小时,', timeType.rest.percentage, '%')
  
  console.log('时间类型分布测试完成\n')
}

/**
 * 测试收入来源分析
 */
function testIncomeSourceStatistics() {
  console.log('=== 测试收入来源分析 ===')
  
  const service = new StatisticsService()
  
  // 模拟 getFilteredDailyData 方法
  service.getFilteredDailyData = function() {
    return createTestData()
  }
  
  const incomeSource = service.calculateIncomeSourceStatistics()
  
  console.log('收入来源分析结果:')
  console.log('- 正常工作收入:', incomeSource.work.amount, '元,', incomeSource.work.percentage, '%')
  console.log('- 加班收入:', incomeSource.overtime.amount, '元,', incomeSource.overtime.percentage, '%')
  console.log('- 总收入:', incomeSource.total, '元')
  
  console.log('收入来源分析测试完成\n')
}

/**
 * 测试摸鱼统计
 */
function testFishingStatistics() {
  console.log('=== 测试摸鱼统计 ===')
  
  const service = new StatisticsService()
  
  // 模拟 getFilteredDailyData 方法
  service.getFilteredDailyData = function() {
    return createTestData()
  }
  
  const fishing = service.calculateFishingStatistics()
  
  console.log('摸鱼统计结果:')
  console.log('- 总摸鱼时长（小时）:', fishing.totalFishingHours)
  console.log('- 摸鱼次数:', fishing.fishingCount)
  console.log('- 摸鱼天数:', fishing.fishingDays)
  console.log('- 平均每日摸鱼次数:', fishing.averageFishingPerDay)
  console.log('- 平均摸鱼时长（分钟）:', fishing.averageFishingDuration)
  
  console.log('摸鱼统计测试完成\n')
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行 StatisticsService 测试...\n')
  
  try {
    testTimeCutoff()
    testOverviewStatistics()
    testTimeTypeStatistics()
    testIncomeSourceStatistics()
    testFishingStatistics()
    
    console.log('✅ 所有测试完成！')
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  runAllTests,
  testTimeCutoff,
  testOverviewStatistics,
  testTimeTypeStatistics,
  testIncomeSourceStatistics,
  testFishingStatistics
}
