/**
 * 工作履历UI改进测试
 * 测试新增的字段显示和计算逻辑
 */

// 模拟工作履历数据
const mockWorkHistory = {
  id: '1752916815108',
  company: '测试科技有限公司',
  position: '前端开发工程师',
  startDate: new Date('2024-01-15'),
  probationEndDate: new Date('2024-04-15'),
  formalSalary: 12000,
  probationSalary: 10000,
  endDate: null, // 当前工作
  notes: '这是一份很有挑战性的工作，团队氛围很好，技术栈包括React、Vue、小程序开发等。',
  payDays: [
    { day: 15, name: '月中发薪' },
    { day: 30, name: '月末奖金' }
  ],
  createTime: new Date('2024-01-10'),
  updateTime: new Date('2024-01-20')
}

// 测试工作时长计算
function testWorkDurationCalculation() {
  console.log('=== 测试工作时长计算 ===')
  
  // 模拟页面的计算方法
  function calculateWorkDuration(work) {
    if (!work || !work.startDate) {
      return '未知'
    }

    const startDate = new Date(work.startDate)
    const endDate = work.endDate ? new Date(work.endDate) : new Date()
    
    const diffTime = endDate.getTime() - startDate.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) {
      return `${diffDays}天`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      const remainingDays = diffDays % 30
      return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingDays = diffDays % 365
      const months = Math.floor(remainingDays / 30)
      
      let result = `${years}年`
      if (months > 0) {
        result += `${months}个月`
      }
      return result
    }
  }

  const duration = calculateWorkDuration(mockWorkHistory)
  console.log('工作时长:', duration)
  
  // 测试已离职的情况
  const pastWork = {
    ...mockWorkHistory,
    startDate: new Date('2023-01-01'),
    endDate: new Date('2023-12-31')
  }
  const pastDuration = calculateWorkDuration(pastWork)
  console.log('过去工作时长:', pastDuration)
}

// 测试日期格式化
function testDateFormatting() {
  console.log('=== 测试日期格式化 ===')
  
  function formatDateText(date) {
    if (!date) {
      return ''
    }
    
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  }

  console.log('试用期结束日期:', formatDateText(mockWorkHistory.probationEndDate))
  console.log('创建时间:', formatDateText(mockWorkHistory.createTime))
  console.log('更新时间:', formatDateText(mockWorkHistory.updateTime))
}

// 测试发薪日显示
function testPayDaysDisplay() {
  console.log('=== 测试发薪日显示 ===')
  
  if (mockWorkHistory.payDays && mockWorkHistory.payDays.length > 0) {
    console.log('发薪日设置:')
    mockWorkHistory.payDays.forEach(payDay => {
      console.log(`- ${payDay.day}日: ${payDay.name}`)
    })
  } else {
    console.log('未设置发薪日')
  }
}

// 测试完整的数据处理流程
function testCompleteDataProcessing() {
  console.log('=== 测试完整数据处理 ===')
  
  // 模拟页面的数据处理逻辑
  function processWorkHistoryItem(work) {
    function calculateWorkDuration(work) {
      if (!work || !work.startDate) return '未知'
      const startDate = new Date(work.startDate)
      const endDate = work.endDate ? new Date(work.endDate) : new Date()
      const diffTime = endDate.getTime() - startDate.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays < 30) {
        return `${diffDays}天`
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30)
        const remainingDays = diffDays % 30
        return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`
      } else {
        const years = Math.floor(diffDays / 365)
        const remainingDays = diffDays % 365
        const months = Math.floor(remainingDays / 30)
        let result = `${years}年`
        if (months > 0) result += `${months}个月`
        return result
      }
    }
    
    function formatDateText(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    return {
      ...work,
      workDurationText: calculateWorkDuration(work),
      probationEndDateText: formatDateText(work.probationEndDate),
      createTimeText: formatDateText(work.createTime),
      updateTimeText: formatDateText(work.updateTime),
      status: work.endDate ? 'inactive' : 'active',
      isCurrent: !work.endDate // 简化的当前工作判断
    }
  }

  const processedWork = processWorkHistoryItem(mockWorkHistory)
  
  console.log('处理后的工作履历数据:')
  console.log('公司:', processedWork.company)
  console.log('职位:', processedWork.position)
  console.log('工作时长:', processedWork.workDurationText)
  console.log('试用期结束:', processedWork.probationEndDateText)
  console.log('创建时间:', processedWork.createTimeText)
  console.log('更新时间:', processedWork.updateTimeText)
  console.log('状态:', processedWork.status)
  console.log('是否当前工作:', processedWork.isCurrent)
  console.log('发薪日数量:', processedWork.payDays ? processedWork.payDays.length : 0)
}

// 运行所有测试
function runAllTests() {
  console.log('开始工作履历UI改进测试...\n')
  
  testWorkDurationCalculation()
  console.log('')
  
  testDateFormatting()
  console.log('')
  
  testPayDaysDisplay()
  console.log('')
  
  testCompleteDataProcessing()
  
  console.log('\n测试完成！')
}

// 导出测试函数
module.exports = {
  runAllTests,
  testWorkDurationCalculation,
  testDateFormatting,
  testPayDaysDisplay,
  testCompleteDataProcessing
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
}
