/**
 * 日期格式化工具 WXS 模块
 */

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {string|Date} dateStr - 日期字符串或日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(dateStr) {
  if (!dateStr) {
    return ''
  }
  
  var date = getDate(dateStr)
  if (!date) {
    return ''
  }
  
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  
  return year + '-' + 
         (month < 10 ? '0' + month : month) + '-' + 
         (day < 10 ? '0' + day : day)
}

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm
 * @param {string|Date} dateStr - 日期字符串或日期对象
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateStr) {
  if (!dateStr) {
    return ''
  }
  
  var date = getDate(dateStr)
  if (!date) {
    return ''
  }
  
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var hours = date.getHours()
  var minutes = date.getMinutes()
  
  return year + '-' + 
         (month < 10 ? '0' + month : month) + '-' + 
         (day < 10 ? '0' + day : day) + ' ' +
         (hours < 10 ? '0' + hours : hours) + ':' +
         (minutes < 10 ? '0' + minutes : minutes)
}

/**
 * 格式化VIP到期时间为 YYYY-MM-DD（xx天后）
 * @param {string|Date} dateStr - 日期字符串或日期对象
 * @returns {string} 格式化后的VIP到期时间字符串
 */
function formatVipExpire(dateStr) {
  if (!dateStr) {
    return ''
  }
  
  var expireDate = getDate(dateStr)
  if (!expireDate) {
    return ''
  }
  
  var now = getDate()
  var diffTime = expireDate.getTime() - now.getTime()
  var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  var year = expireDate.getFullYear()
  var month = expireDate.getMonth() + 1
  var day = expireDate.getDate()
  
  var dateStr = year + '-' + 
                (month < 10 ? '0' + month : month) + '-' + 
                (day < 10 ? '0' + day : day)
  
  if (diffDays <= 0) {
    return dateStr + '（已过期）'
  } else if (diffDays === 1) {
    return dateStr + '（明天到期）'
  } else {
    return dateStr + '（' + diffDays + '天后到期）'
  }
}

/**
 * 计算距离现在的天数
 * @param {string|Date} dateStr - 日期字符串或日期对象
 * @returns {number} 天数差
 */
function getDaysFromNow(dateStr) {
  if (!dateStr) {
    return 0
  }
  
  var targetDate = getDate(dateStr)
  if (!targetDate) {
    return 0
  }
  
  var now = getDate()
  var diffTime = targetDate.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

module.exports = {
  formatDate: formatDate,
  formatDateTime: formatDateTime,
  formatVipExpire: formatVipExpire,
  getDaysFromNow: getDaysFromNow
}
