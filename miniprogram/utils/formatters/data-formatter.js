/**
 * 数据格式化工具
 * 提供各种数据格式化方法
 */

class DataFormatter {
  /**
   * 格式化货币显示
   * @param {number} amount - 金额
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的货币字符串
   */
  static formatCurrency(amount, options = {}) {
    const {
      symbol = '¥',
      decimals = 2,
      thousandsSeparator = ',',
      decimalSeparator = '.'
    } = options

    if (isNaN(Number(amount))) {
      return `${symbol}0${decimalSeparator}${'0'.repeat(decimals)}`
    }

    const num = Number(amount)
    const parts = Math.abs(num).toFixed(decimals).split('.')
    
    // 添加千位分隔符
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator)
    
    const formattedAmount = parts.join(decimalSeparator)
    return `${num < 0 ? '-' : ''}${symbol}${formattedAmount}`
  }

  /**
   * 格式化时间显示
   * @param {number} minutes - 分钟数
   * @returns {string} 格式化后的时间字符串
   */
  static formatDuration(minutes) {
    if (isNaN(Number(minutes)) || Number(minutes) < 0) {
      return '0分钟'
    }

    const totalMinutes = Math.floor(Number(minutes))
    const hours = Math.floor(totalMinutes / 60)
    const mins = totalMinutes % 60

    if (hours === 0) {
      return `${mins}分钟`
    } else if (mins === 0) {
      return `${hours}小时`
    } else {
      return `${hours}小时${mins}分钟`
    }
  }

  /**
   * 格式化日期显示
   * @param {Date|string} date - 日期
   * @param {string} format - 格式化模式
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return ''
    
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear()
    const month = (d.getMonth() + 1).toString().padStart(2, '0')
    const day = d.getDate().toString().padStart(2, '0')
    const hour = d.getHours().toString().padStart(2, '0')
    const minute = d.getMinutes().toString().padStart(2, '0')
    const second = d.getSeconds().toString().padStart(2, '0')

    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  }

  /**
   * 格式化相对时间显示
   * @param {Date|string} date - 日期
   * @returns {string} 相对时间字符串
   */
  static formatRelativeTime(date) {
    if (!date) return ''
    
    const target = new Date(date)
    if (isNaN(target.getTime())) return ''

    const now = new Date()
    const diffMs = now.getTime() - target.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays === -1) {
      return '明天'
    } else if (diffDays > 0 && diffDays <= 7) {
      return `${diffDays}天前`
    } else if (diffDays < 0 && diffDays >= -7) {
      return `${Math.abs(diffDays)}天后`
    } else {
      return this.formatDate(date, 'MM-DD')
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的文件大小
   */
  static formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes'
    if (isNaN(Number(bytes))) return '0 Bytes'

    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  /**
   * 格式化手机号显示
   * @param {string} phone - 手机号
   * @param {string} separator - 分隔符
   * @returns {string} 格式化后的手机号
   */
  static formatPhone(phone, separator = ' ') {
    if (!phone || typeof phone !== 'string') return ''
    
    const cleanPhone = phone.replace(/\D/g, '')
    
    if (cleanPhone.length === 11) {
      return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, `$1${separator}$2${separator}$3`)
    }
    
    return phone
  }

  /**
   * 格式化银行卡号显示
   * @param {string} cardNumber - 银行卡号
   * @param {string} separator - 分隔符
   * @returns {string} 格式化后的银行卡号
   */
  static formatBankCard(cardNumber, separator = ' ') {
    if (!cardNumber || typeof cardNumber !== 'string') return ''
    
    const cleanCard = cardNumber.replace(/\D/g, '')
    return cleanCard.replace(/(\d{4})/g, `$1${separator}`).trim()
  }

  /**
   * 格式化身份证号显示
   * @param {string} idCard - 身份证号
   * @param {string} separator - 分隔符
   * @returns {string} 格式化后的身份证号
   */
  static formatIdCard(idCard, separator = ' ') {
    if (!idCard || typeof idCard !== 'string') return ''
    
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})(\d{8})(\d{4})/, `$1${separator}$2${separator}$3`)
    } else if (idCard.length === 15) {
      return idCard.replace(/(\d{6})(\d{6})(\d{3})/, `$1${separator}$2${separator}$3`)
    }
    
    return idCard
  }

  /**
   * 格式化数字显示（添加千位分隔符）
   * @param {number} number - 数字
   * @param {string} separator - 分隔符
   * @returns {string} 格式化后的数字
   */
  static formatNumber(number, separator = ',') {
    if (isNaN(Number(number))) return '0'
    
    return Number(number).toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  }

  /**
   * 格式化百分比显示
   * @param {number} value - 数值
   * @param {number} total - 总数
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的百分比
   */
  static formatPercentage(value, total, decimals = 1) {
    if (isNaN(Number(value)) || isNaN(Number(total)) || Number(total) === 0) {
      return '0%'
    }

    const percentage = (Number(value) / Number(total)) * 100
    return `${percentage.toFixed(decimals)}%`
  }

  /**
   * 格式化中文数字
   * @param {number} number - 数字
   * @returns {string} 中文数字
   */
  static formatChineseNumber(number) {
    if (isNaN(Number(number))) return '零'
    
    const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
    const units = ['', '十', '百', '千', '万']
    
    const num = Math.floor(Number(number))
    if (num === 0) return '零'
    if (num < 10) return chineseNumbers[num]
    
    // 简单实现，仅支持到万位
    let result = ''
    let unitIndex = 0
    let temp = num
    
    while (temp > 0) {
      const digit = temp % 10
      if (digit !== 0) {
        result = chineseNumbers[digit] + units[unitIndex] + result
      }
      temp = Math.floor(temp / 10)
      unitIndex++
    }
    
    return result
  }
}

module.exports = {
  DataFormatter
}
