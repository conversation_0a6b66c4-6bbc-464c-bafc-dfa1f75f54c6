/**
 * WeChat Mini Program 兼容的 Base64 编码/解码工具
 * 由于微信小程序环境不支持 btoa 和 atob 函数，提供兼容实现
 */

class Base64Utils {
  static chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'

  /**
   * Base64 编码
   * @param {string} str - 要编码的字符串
   * @returns {string} Base64 编码后的字符串
   */
  static encode(str) {
    if (!str || typeof str !== 'string') {
      return ''
    }

    let result = ''
    let i = 0
    
    while (i < str.length) {
      const a = str.charCodeAt(i++)
      const b = i < str.length ? str.charCodeAt(i++) : 0
      const c = i < str.length ? str.charCodeAt(i++) : 0
      
      const bitmap = (a << 16) | (b << 8) | c
      
      result += this.chars.charAt((bitmap >> 18) & 63)
      result += this.chars.charAt((bitmap >> 12) & 63)
      result += i - 2 < str.length ? this.chars.charAt((bitmap >> 6) & 63) : '='
      result += i - 1 < str.length ? this.chars.charAt(bitmap & 63) : '='
    }
    
    return result
  }

  /**
   * Base64 解码
   * @param {string} str - Base64 编码的字符串
   * @returns {string} 解码后的字符串
   */
  static decode(str) {
    if (!str || typeof str !== 'string') {
      return ''
    }

    let result = ''
    let i = 0
    
    // 移除非Base64字符
    str = str.replace(/[^A-Za-z0-9+/]/g, '')
    
    while (i < str.length) {
      const encoded1 = this.chars.indexOf(str.charAt(i++))
      const encoded2 = this.chars.indexOf(str.charAt(i++))
      const encoded3 = this.chars.indexOf(str.charAt(i++))
      const encoded4 = this.chars.indexOf(str.charAt(i++))
      
      if (encoded1 === -1 || encoded2 === -1) {
        break
      }
      
      const bitmap = (encoded1 << 18) | (encoded2 << 12) | 
                    ((encoded3 === -1 ? 0 : encoded3) << 6) | 
                    (encoded4 === -1 ? 0 : encoded4)
      
      result += String.fromCharCode((bitmap >> 16) & 255)
      if (encoded3 !== -1 && encoded3 !== 64) {
        result += String.fromCharCode((bitmap >> 8) & 255)
      }
      if (encoded4 !== -1 && encoded4 !== 64) {
        result += String.fromCharCode(bitmap & 255)
      }
    }
    
    return result
  }

  /**
   * 编码 UTF-8 字符串为 Base64
   * @param {string} str - UTF-8 字符串
   * @returns {string} Base64 编码后的字符串
   */
  static encodeUTF8(str) {
    if (!str || typeof str !== 'string') {
      return ''
    }

    try {
      // 将UTF-8字符串转换为字节序列
      const utf8Bytes = []
      for (let i = 0; i < str.length; i++) {
        const code = str.charCodeAt(i)
        if (code < 0x80) {
          utf8Bytes.push(code)
        } else if (code < 0x800) {
          utf8Bytes.push(0xC0 | (code >> 6))
          utf8Bytes.push(0x80 | (code & 0x3F))
        } else if (code < 0xD800 || code >= 0xE000) {
          utf8Bytes.push(0xE0 | (code >> 12))
          utf8Bytes.push(0x80 | ((code >> 6) & 0x3F))
          utf8Bytes.push(0x80 | (code & 0x3F))
        } else {
          // 代理对处理
          i++
          const hi = code
          const lo = str.charCodeAt(i)
          const codePoint = 0x10000 + (((hi & 0x3FF) << 10) | (lo & 0x3FF))
          utf8Bytes.push(0xF0 | (codePoint >> 18))
          utf8Bytes.push(0x80 | ((codePoint >> 12) & 0x3F))
          utf8Bytes.push(0x80 | ((codePoint >> 6) & 0x3F))
          utf8Bytes.push(0x80 | (codePoint & 0x3F))
        }
      }
      
      // 将字节序列转换为字符串
      const byteString = String.fromCharCode.apply(null, utf8Bytes)
      return this.encode(byteString)
    } catch (error) {
      console.error('UTF-8 Base64编码失败:', error)
      return this.encode(str)
    }
  }

  /**
   * 解码 Base64 为 UTF-8 字符串
   * @param {string} base64Str - Base64 编码的字符串
   * @returns {string} UTF-8 字符串
   */
  static decodeUTF8(base64Str) {
    if (!base64Str || typeof base64Str !== 'string') {
      return ''
    }

    try {
      const byteString = this.decode(base64Str)
      const utf8Bytes = []
      
      for (let i = 0; i < byteString.length; i++) {
        utf8Bytes.push(byteString.charCodeAt(i))
      }
      
      // 将UTF-8字节序列转换为字符串
      let result = ''
      let i = 0
      
      while (i < utf8Bytes.length) {
        const byte1 = utf8Bytes[i++]
        
        if (byte1 < 0x80) {
          result += String.fromCharCode(byte1)
        } else if ((byte1 & 0xE0) === 0xC0) {
          const byte2 = utf8Bytes[i++]
          result += String.fromCharCode(((byte1 & 0x1F) << 6) | (byte2 & 0x3F))
        } else if ((byte1 & 0xF0) === 0xE0) {
          const byte2 = utf8Bytes[i++]
          const byte3 = utf8Bytes[i++]
          result += String.fromCharCode(((byte1 & 0x0F) << 12) | ((byte2 & 0x3F) << 6) | (byte3 & 0x3F))
        } else if ((byte1 & 0xF8) === 0xF0) {
          const byte2 = utf8Bytes[i++]
          const byte3 = utf8Bytes[i++]
          const byte4 = utf8Bytes[i++]
          const codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3F) << 12) | ((byte3 & 0x3F) << 6) | (byte4 & 0x3F)
          const hi = 0xD800 + ((codePoint - 0x10000) >> 10)
          const lo = 0xDC00 + ((codePoint - 0x10000) & 0x3FF)
          result += String.fromCharCode(hi, lo)
        }
      }
      
      return result
    } catch (error) {
      console.error('UTF-8 Base64解码失败:', error)
      return this.decode(base64Str)
    }
  }
}

module.exports = {
  Base64Utils
}
