/**
 * 数据安全工具
 * 提供数据脱敏和安全处理方法
 */

const { Base64Utils } = require('./base64-utils.js')

class DataSecurity {
  /**
   * 脱敏处理手机号
   * @param {string} phone - 手机号
   * @returns {string} 脱敏后的手机号
   */
  static maskPhone(phone) {
    if (!phone || typeof phone !== 'string') {
      return ''
    }
    
    const cleanPhone = phone.replace(/\D/g, '')
    
    if (cleanPhone.length === 11) {
      return cleanPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    } else if (cleanPhone.length >= 7) {
      const start = cleanPhone.substring(0, 3)
      const end = cleanPhone.substring(cleanPhone.length - 4)
      return start + '****' + end
    }
    
    return phone
  }

  /**
   * 脱敏处理邮箱
   * @param {string} email - 邮箱
   * @returns {string} 脱敏后的邮箱
   */
  static maskEmail(email) {
    if (!email || typeof email !== 'string') {
      return ''
    }
    
    const atIndex = email.indexOf('@')
    if (atIndex <= 0) {
      return email
    }
    
    const username = email.substring(0, atIndex)
    const domain = email.substring(atIndex)
    
    if (username.length <= 2) {
      return email
    }
    
    const maskedUsername = username.charAt(0) + '*'.repeat(Math.max(1, username.length - 2)) + username.charAt(username.length - 1)
    return maskedUsername + domain
  }

  /**
   * 脱敏处理身份证号
   * @param {string} idCard - 身份证号
   * @returns {string} 脱敏后的身份证号
   */
  static maskIdCard(idCard) {
    if (!idCard || typeof idCard !== 'string') {
      return ''
    }
    
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    } else if (idCard.length === 15) {
      return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
    }
    
    return idCard
  }

  /**
   * 脱敏处理银行卡号
   * @param {string} bankCard - 银行卡号
   * @returns {string} 脱敏后的银行卡号
   */
  static maskBankCard(bankCard) {
    if (!bankCard || typeof bankCard !== 'string') {
      return ''
    }
    
    const cleanCard = bankCard.replace(/\D/g, '')
    
    if (cleanCard.length >= 8) {
      const start = cleanCard.substring(0, 4)
      const end = cleanCard.substring(cleanCard.length - 4)
      const middle = '*'.repeat(cleanCard.length - 8)
      return start + middle + end
    }
    
    return bankCard
  }

  /**
   * 脱敏处理姓名
   * @param {string} name - 姓名
   * @returns {string} 脱敏后的姓名
   */
  static maskName(name) {
    if (!name || typeof name !== 'string') {
      return ''
    }
    
    if (name.length === 1) {
      return name
    } else if (name.length === 2) {
      return name.charAt(0) + '*'
    } else {
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
    }
  }

  /**
   * 脱敏处理地址
   * @param {string} address - 地址
   * @param {number} keepStart - 保留开头字符数
   * @param {number} keepEnd - 保留结尾字符数
   * @returns {string} 脱敏后的地址
   */
  static maskAddress(address, keepStart = 6, keepEnd = 4) {
    if (!address || typeof address !== 'string') {
      return ''
    }
    
    if (address.length <= keepStart + keepEnd) {
      return address
    }
    
    const start = address.substring(0, keepStart)
    const end = address.substring(address.length - keepEnd)
    const middle = '*'.repeat(Math.max(1, address.length - keepStart - keepEnd))
    
    return start + middle + end
  }

  /**
   * 脱敏处理金额
   * @param {number|string} amount - 金额
   * @returns {string} 脱敏后的金额显示
   */
  static maskAmount(amount) {
    // 忽略参数，统一返回脱敏标识
    return '***'
  }

  /**
   * 脱敏处理车牌号
   * @param {string} plateNumber - 车牌号
   * @returns {string} 脱敏后的车牌号
   */
  static maskPlateNumber(plateNumber) {
    if (!plateNumber || typeof plateNumber !== 'string') {
      return ''
    }
    
    if (plateNumber.length >= 7) {
      return plateNumber.substring(0, 2) + '***' + plateNumber.substring(plateNumber.length - 2)
    }
    
    return plateNumber
  }

  /**
   * 脱敏处理IP地址
   * @param {string} ip - IP地址
   * @returns {string} 脱敏后的IP地址
   */
  static maskIP(ip) {
    if (!ip || typeof ip !== 'string') {
      return ''
    }
    
    const parts = ip.split('.')
    if (parts.length === 4) {
      return parts[0] + '.***.***.' + parts[3]
    }
    
    return ip
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   * @param {string} charset - 字符集
   * @returns {string} 随机字符串
   */
  static generateRandomString(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    let result = ''
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    return result
  }

  /**
   * 生成UUID
   * @returns {string} UUID字符串
   */
  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 简单加密（仅用于演示，不适用于生产环境）
   * @param {string} text - 要加密的文本
   * @param {number} shift - 位移量
   * @returns {string} 加密后的文本
   */
  static simpleEncrypt(text, shift = 3) {
    if (!text || typeof text !== 'string') {
      return ''
    }
    
    return text.split('').map(char => {
      const code = char.charCodeAt(0)
      return String.fromCharCode(code + shift)
    }).join('')
  }

  /**
   * 简单解密（仅用于演示，不适用于生产环境）
   * @param {string} encryptedText - 加密的文本
   * @param {number} shift - 位移量
   * @returns {string} 解密后的文本
   */
  static simpleDecrypt(encryptedText, shift = 3) {
    if (!encryptedText || typeof encryptedText !== 'string') {
      return ''
    }
    
    return encryptedText.split('').map(char => {
      const code = char.charCodeAt(0)
      return String.fromCharCode(code - shift)
    }).join('')
  }

  /**
   * Base64编码
   * @param {string} text - 要编码的文本
   * @returns {string} Base64编码后的文本
   */
  static base64Encode(text) {
    if (!text || typeof text !== 'string') {
      return ''
    }

    try {
      // 使用WeChat兼容的Base64编码
      return Base64Utils.encode(text)
    } catch (error) {
      console.error('Base64编码失败:', error)
      return text
    }
  }

  /**
   * Base64解码
   * @param {string} encodedText - Base64编码的文本
   * @returns {string} 解码后的文本
   */
  static base64Decode(encodedText) {
    if (!encodedText || typeof encodedText !== 'string') {
      return ''
    }

    try {
      // 使用WeChat兼容的Base64解码
      return Base64Utils.decode(encodedText)
    } catch (error) {
      console.error('Base64解码失败:', error)
      return encodedText
    }
  }

  /**
   * 检查密码强度
   * @param {string} password - 密码
   * @returns {Object} 强度评估结果
   */
  static checkPasswordStrength(password) {
    if (!password || typeof password !== 'string') {
      return { level: 0, description: '密码为空' }
    }
    
    let score = 0
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    }
    
    Object.values(checks).forEach(check => {
      if (check) score++
    })
    
    const levels = [
      { level: 0, description: '非常弱' },
      { level: 1, description: '弱' },
      { level: 2, description: '一般' },
      { level: 3, description: '较强' },
      { level: 4, description: '强' },
      { level: 5, description: '非常强' }
    ]
    
    return {
      ...levels[score],
      score,
      checks
    }
  }
}

module.exports = {
  DataSecurity
}
