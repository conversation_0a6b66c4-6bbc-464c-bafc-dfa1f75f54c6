/**
 * 工具类统一导出文件 - CommonJS 格式
 * 提供所有工具类的统一入口
 */

// 导入所有模块
const { DataValidator } = require('./validators/data-validator.js')
const { DataFormatter } = require('./formatters/data-formatter.js')
const { DataCalculator } = require('./helpers/data-calculator.js')
const { DataSecurity } = require('./helpers/data-security.js')
const timeUtils = require('./helpers/time-utils.js')
const { getHolidayManager } = require('../core/managers/holiday-manager.js')

// 统一导出
module.exports = {
  // 验证器
  DataValidator,

  // 格式化器
  DataFormatter,

  // 计算器和安全工具
  DataCalculator,
  DataSecurity,

  // 时间工具
  formatDate: timeUtils.formatDate,
  formatTime: timeUtils.formatTime,
  timeUtils,

  // 节假日管理器
  getHolidayManager,
  holidayManager: { getHolidayManager }
}
