/**
 * 会员相关工具函数
 */

/**
 * 检查是否为VIP会员
 * @returns {boolean} 是否为VIP会员
 */
function isVipMember() {
  try {
    const app = getApp()
    const userManager = app.getUserManager()

    if (!userManager) {
      return false
    }

    const userInfo = userManager.getUserInfo()
    return userInfo?.vip?.status === true
  } catch (error) {
    console.error('检查VIP会员状态失败:', error)
    return false
  }
}

/**
 * 检查功能权限并显示提示
 * @param {string} featureName - 功能名称
 * @param {string} description - 功能描述
 * @returns {boolean} 是否有权限
 */
function checkFeaturePermission(featureName, description = '此功能') {
  if (isVipMember()) {
    return true
  }

  // 免费功能列表
  const freeFeatures = ['export_data']
  
  if (freeFeatures.includes(featureName)) {
    return true
  }

  // 显示升级提示
  showUpgradeModal(description)
  return false
}

/**
 * 显示升级提示
 * @param {string} featureDescription - 功能描述
 */
function showUpgradeModal(featureDescription = '此功能') {
  wx.showModal({
    title: '功能限制',
    content: `${featureDescription}仅限VIP会员使用\n\n获取VIP会员即可解锁更多功能！`,
    confirmText: '了解VIP会员',
    cancelText: '暂不获取',
    success: (res) => {
      if (res.confirm) {
        showVipMembershipInfo()
      }
    }
  })
}

/**
 * 显示VIP会员权益信息
 */
function showVipMembershipInfo() {
  const content = `VIP会员专享功能：
• 数据同步功能
• 高级数据分析
• 历史数据管理
• 自定义主题

使用限制提升：
• 工作履历：免费版5个 → VIP版无限制
• 每日时间段：免费版10个 → VIP版无限制
• 数据保留：免费版30天 → VIP版永久保留

获取VIP会员的方式：
🎁 新用户注册送14天VIP
🔑 通过激活码激活续期
📺 观看激励广告视频续期

敬请期待VIP功能上线！`

  wx.showModal({
    title: 'VIP会员权益',
    content: content,
    showCancel: false,
    confirmText: '知道了'
  })
}

/**
 * 检查使用限制
 * @param {string} limitType - 限制类型
 * @param {number} currentValue - 当前值
 * @returns {boolean} 是否在限制内
 */
function checkUsageLimit(limitType, currentValue) {
  if (isVipMember()) {
    return true // VIP会员无限制
  }

  const limits = {
    work_histories: 5,
    time_segments_per_day: 10,
    data_retention_days: 30
  }

  const limit = limits[limitType]
  if (!limit) {
    return true // 未知限制类型，允许使用
  }

  if (currentValue >= limit) {
    const limitNames = {
      work_histories: '工作履历',
      time_segments_per_day: '每日时间段',
      data_retention_days: '数据保留天数'
    }

    const limitName = limitNames[limitType] || limitType
    
    wx.showModal({
      title: '使用限制',
      content: `${limitName}数量已达到限制 (${limit})\n\n获取VIP会员即可解除限制！`,
      confirmText: '了解VIP会员',
      cancelText: '知道了',
      success: (res) => {
        if (res.confirm) {
          showVipMembershipInfo()
        }
      }
    })
    
    return false
  }

  return true
}

/**
 * 获取会员类型文本
 * @returns {string} 会员类型
 */
function getMembershipTypeText() {
  return isVipMember() ? 'VIP会员' : '免费用户'
}

/**
 * 获取会员状态信息
 * @returns {Object} 会员状态
 */
function getMembershipStatus() {
  const isVip = isVipMember()

  return {
    isVip: isVip,
    typeText: getMembershipTypeText(),
    features: {
      data_sync: isVip,
      advanced_analytics: isVip,
      export_data: true, // 免费功能
      history_data: isVip,
      custom_themes: isVip
    },
    limits: isVip ? {
      work_histories: -1,
      time_segments_per_day: -1,
      data_retention_days: -1
    } : {
      work_histories: 5,
      time_segments_per_day: 10,
      data_retention_days: 30
    }
  }
}

module.exports = {
  isVipMember,
  checkFeaturePermission,
  checkUsageLimit,
  showUpgradeModal,
  showVipMembershipInfo,
  getMembershipTypeText,
  getMembershipStatus
}
