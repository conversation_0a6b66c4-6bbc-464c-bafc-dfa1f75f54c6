/**
 * 简化的实时收入计算器
 * 重构版本 - 更简洁、更可靠
 * 支持摸鱼收入计算
 */

class SimpleRealTimeIncome {
  constructor() {
    this.timer = null
    this.decimalPlaces = 2
    this.updateInterval = 1000 // 动态计算的更新间隔
    this.isRunning = false
    this.currentHourlyRate = 0 // 当前时薪，用于计算刷新间隔
    this.lastSegmentIndex = -1 // 上次的时间段索引，用于检测时间段变化
  }

  /**
   * 设置小数位数
   */
  setDecimalPlaces(places) {
    const oldDecimalPlaces = this.decimalPlaces
    this.decimalPlaces = Math.max(0, Math.min(3, places !== undefined ? places : 2))

    // 如果小数位数改变且正在运行，重新计算刷新间隔
    if (oldDecimalPlaces !== this.decimalPlaces && this.isRunning && this.currentHourlyRate > 0) {
      this.updateInterval = this.calculateOptimalUpdateInterval(this.currentHourlyRate)
      console.log('[DEBUG] 重新计算更新间隔:', this.updateInterval)
    }
  }

  /**
   * 计算最优更新间隔
   * @param {number} hourlyRate - 时薪
   * @returns {number} 更新间隔（毫秒）
   */
  calculateOptimalUpdateInterval(hourlyRate) {
    if (!hourlyRate || hourlyRate <= 0) {
      return 1000 // 默认1秒
    }

    // 计算每毫秒的收入增长
    const incomePerMs = hourlyRate / (60 * 60 * 1000) // 时薪 / (60分钟 * 60秒 * 1000毫秒)

    // 计算最小显示单位（根据小数位数）
    const minDisplayUnit = Math.pow(10, -this.decimalPlaces) // 例如：小数位数3 -> 0.001

    // 计算达到最小显示单位需要的时间（毫秒）
    const msForMinUnit = minDisplayUnit / incomePerMs

    // 设置更新间隔为达到最小单位时间的 95%（提前一点更新，确保及时显示）
    let optimalInterval = Math.floor(msForMinUnit * 0.95)

    // 限制在合理范围内：最小8ms，最大1000ms
    optimalInterval = Math.max(8, Math.min(1000, optimalInterval))

    return optimalInterval
  }

  /**
   * 启动实时收入更新
   * @param {Function} callback - 更新回调
   * @param {Object} options - 配置选项
   */
  start(callback, options = {}) {
    this.stop() // 先停止之前的更新

    const {
      workData,           // 今日工作数据
      currentWork,        // 当前工作履历
      baseService,        // 基础服务
      getFishingState     // 获取摸鱼状态的函数
    } = options

    // 验证必要参数
    if (!callback || typeof callback !== 'function') {
      console.error('回调函数无效')
      return
    }

    if (!workData || !currentWork || !baseService) {
      console.error('必要参数缺失:', {
        hasWorkData: !!workData,
        hasCurrentWork: !!currentWork,
        hasBaseService: !!baseService
      })
      return
    }

    this.isRunning = true
    this.lastSegmentIndex = -1 // 重置时间段索引

    // 获取初始时薪并计算初始更新间隔（基于当前时间段）
    const initialWorkTimeInfo = this.calculateRealTimeIncomeFromSegments(workData, new Date())
    const initialHourlyRate = initialWorkTimeInfo.currentHourlyRate || 50 // 如果没有当前时间段，使用默认值
    this.currentHourlyRate = initialHourlyRate
    this.updateInterval = this.calculateOptimalUpdateInterval(initialHourlyRate)

    const updateLoop = () => {
      if (!this.isRunning) return

      try {
        // 获取当前摸鱼状态
        const fishingState = getFishingState ? getFishingState() : null
        const result = this.calculateCurrentIncome(workData, currentWork, baseService, fishingState)

        // 检查时薪是否变化（可能进入了新的时间段）
        if (result.hourlyRate !== this.currentHourlyRate) {
          this.currentHourlyRate = result.hourlyRate
          const newInterval = this.calculateOptimalUpdateInterval(this.currentHourlyRate)

          if (newInterval !== this.updateInterval) {
            this.updateInterval = newInterval
          }
        }

        // 检查是否进入了新的时间段
        if (result.currentSegmentIndex !== undefined && result.currentSegmentIndex !== this.lastSegmentIndex) {
          this.lastSegmentIndex = result.currentSegmentIndex

          // 重新计算刷新间隔（新时间段可能有不同的时薪）
          const newInterval = this.calculateOptimalUpdateInterval(result.hourlyRate)
          if (newInterval !== this.updateInterval) {

            this.updateInterval = newInterval
          }
        }

        callback(result)

        // 在工作时间、摸鱼状态或非工作时间下都继续更新（非工作时间显示当天累计收入）
        if (this.isRunning) {
          // 如果不在活跃状态，使用较长的更新间隔（5秒）
          const updateInterval = result.isActive ? this.updateInterval : 5000
          this.timer = setTimeout(updateLoop, updateInterval)
        }
      } catch (error) {
        console.error('更新循环错误:', error)
        this.isRunning = false
      }
    }

    // 立即执行第一次更新
    updateLoop()
  }

  /**
   * 停止实时更新
   */
  stop() {
    this.isRunning = false
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  }

  /**
   * 计算当前实时收入（包含摸鱼收入）
   * @param {Object} workData - 工作数据
   * @param {Object} currentWork - 当前工作
   * @param {Object} baseService - 基础服务
   * @param {Object} fishingState - 摸鱼状态
   * @param {Date} currentTime - 当前时间（可选，默认使用当前时间）
   */
  calculateCurrentIncome(workData, currentWork, baseService, fishingState = null, currentTime = null) {
    const now = currentTime || new Date()

    // 通知摸鱼管理器检查时间段结束
    if (fishingState && fishingState.isActive) {
      const dataManager = getApp().getDataManager()
      if (dataManager.fishingManager) {
        dataManager.fishingManager.handleTimeSegmentEnd({
          currentTime: now,
          workData: workData,
          fishingState: fishingState
        })
      }
    }

    // 1. 计算摸鱼收入（先计算以获取摸鱼状态）
    const fishingInfo = this.calculateFishingIncome(workData, now, fishingState, 0) // 先用0时薪，后面会更新

    // 2. 计算已工作时间和获取当前时间段信息（传递摸鱼状态）
    const workTimeInfo = this.calculateRealTimeIncomeFromSegments(workData, now, fishingInfo.isFishing, fishingState)

    // 3. 重新计算摸鱼收入（使用正确的时薪）
    const finalFishingInfo = this.calculateFishingIncome(workData, now, fishingState, workTimeInfo.currentHourlyRate)

    // 4. 检查是否在工作时间或摸鱼状态
    const isWorking = workTimeInfo.isWorking
    const isFishing = finalFishingInfo.isFishing

    // 5. 获取当前时间段的时薪（用于高频刷新计算）
    const currentHourlyRate = workTimeInfo.currentHourlyRate || 0

    // 6. 计算总收入
    const totalIncome = workTimeInfo.totalIncome + finalFishingInfo.totalFishingIncome

    return {
      // 总收入相关
      income: totalIncome,
      formattedIncome: this.formatIncome(totalIncome),

      // 工作收入相关
      workIncome: workTimeInfo.totalIncome,
      formattedWorkIncome: this.formatIncome(workTimeInfo.totalIncome),

      // 摸鱼收入相关
      fishingIncome: finalFishingInfo.totalFishingIncome,
      formattedFishingIncome: this.formatIncome(finalFishingInfo.totalFishingIncome),

      // 状态相关
      isWorking,
      isFishing,
      isActive: isWorking || isFishing, // 是否处于活跃状态（工作或摸鱼）

      // 其他信息
      hourlyRate: currentHourlyRate,
      workedHours: workTimeInfo.totalWorkedHours,
      fishingHours: finalFishingInfo.totalFishingHours,
      currentSegmentIndex: workTimeInfo.currentSegmentIndex,

      // 摸鱼状态信息
      fishingState: finalFishingInfo.fishingState,
      currentFishingDuration: finalFishingInfo.currentFishingDuration
    }
  }

  /**
   * 计算摸鱼收入
   * @param {Object} workData - 工作数据
   * @param {Date} currentTime - 当前时间
   * @param {Object} fishingState - 当前摸鱼状态
   * @param {number} currentHourlyRate - 当前时薪
   * @returns {Object} 摸鱼收入信息
   */
  calculateFishingIncome(workData, currentTime, fishingState, currentHourlyRate = 0) {
    let totalFishingIncome = 0
    let totalFishingHours = 0
    let isFishing = false
    let currentFishingDuration = 0

    // 1. 计算已完成的摸鱼记录收入
    if (workData.fishes && workData.fishes.length > 0) {
      workData.fishes.forEach(fish => {
        const fishingMinutes = fish.end - fish.start
        const fishingHours = fishingMinutes / 60

        // 找到摸鱼时间对应的工作时间段，使用该时间段的时薪
        const correspondingSegment = this.findCorrespondingWorkSegment(fish, workData.segments)
        const fishingHourlyRate = correspondingSegment ? this.calculateSegmentHourlyRate(correspondingSegment) : currentHourlyRate

        const fishingIncome = fishingHours * fishingHourlyRate

        totalFishingIncome += fishingIncome
        totalFishingHours += fishingHours
      })
    }

    // 2. 计算当前进行中的摸鱼收入
    if (fishingState && fishingState.isActive) {
      const startTime = new Date(fishingState.startTime)
      const fishingMs = currentTime.getTime() - startTime.getTime()
      const fishingHours = Math.max(0, fishingMs / (1000 * 60 * 60))
      const currentFishingIncome = fishingHours * currentHourlyRate

      totalFishingIncome += currentFishingIncome
      totalFishingHours += fishingHours
      isFishing = true
      currentFishingDuration = fishingMs
    }

    return {
      totalFishingIncome,
      totalFishingHours,
      isFishing,
      fishingState,
      currentFishingDuration
    }
  }

  /**
   * 基于时间段计算实时收入
   * @param {Object} workData - 工作数据
   * @param {Date} currentTime - 当前时间
   * @param {boolean} isFishing - 是否正在摸鱼
   * @param {Object} fishingState - 摸鱼状态
   */
  calculateRealTimeIncomeFromSegments(workData, currentTime, isFishing = false, fishingState = null) {
    let totalIncome = 0
    let totalWorkedHours = 0
    let currentSegmentIndex = -1
    let isWorking = false
    let currentHourlyRate = 0

    if (!workData.segments || workData.segments.length === 0) {
      return {
        totalIncome: 0,
        totalWorkedHours: 0,
        currentSegmentIndex: -1,
        isWorking: false,
        currentHourlyRate: 0
      }
    }

    // 计算当前时间的分钟数
    const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes()

    /**
     * 计算时间段内的摸鱼总时间（分钟）
     * @param {Object} segment - 时间段
     * @param {Date} segmentStartTime - 时间段开始时间
     * @param {Date} segmentEndTime - 时间段结束时间
     * @param {Date} currentTime - 当前时间
     * @param {boolean} isFishing - 是否正在摸鱼
     * @param {Object} fishingState - 当前摸鱼状态
     * @returns {number} 摸鱼总时间（小时）
     */
    const calculateFishingTimeInSegment = (segment, segmentStartTime, segmentEndTime, currentTime, isFishing, fishingState) => {
      let totalFishingHours = 0

      // 1. 计算已完成的摸鱼记录
      if (workData.fishes && workData.fishes.length > 0) {
        workData.fishes.forEach(fish => {
          // 检查摸鱼记录是否在当前时间段内
          if (fish.start >= segment.start && fish.end <= segment.end) {
            const fishingMinutes = fish.end - fish.start
            totalFishingHours += fishingMinutes / 60
          }
        })
      }

      // 2. 计算正在进行的摸鱼时间
      if (isFishing && fishingState && fishingState.startTime) {
        const fishingStartTime = new Date(fishingState.startTime)

        // 检查摸鱼开始时间是否在当前时间段内
        if (fishingStartTime >= segmentStartTime && fishingStartTime <= segmentEndTime) {
          // 计算摸鱼时间：从摸鱼开始到当前时间（或时间段结束时间）
          const fishingEndTime = currentTime < segmentEndTime ? currentTime : segmentEndTime
          const fishingMs = fishingEndTime.getTime() - fishingStartTime.getTime()
          const fishingHours = Math.max(0, fishingMs / (1000 * 60 * 60))
          totalFishingHours += fishingHours
        }
      }

      return totalFishingHours
    }

    workData.segments.forEach((segment, index) => {
      if (segment.type === 'rest') {
        return
      }

      // 分钟数转换为Date对象，支持跨日期时间段
      const baseDate = new Date(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate())

      // 处理跨日期时间段
      let startTime, endTime
      if (segment.start >= 1440) {
        // 开始时间在次日
        const nextDay = new Date(baseDate)
        nextDay.setDate(nextDay.getDate() + 1)
        startTime = new Date(nextDay.getTime() + (segment.start - 1440) * 60 * 1000)
      } else {
        // 开始时间在当天
        startTime = new Date(baseDate.getTime() + segment.start * 60 * 1000)
      }

      if (segment.end >= 1440) {
        // 结束时间在次日
        const nextDay = new Date(baseDate)
        nextDay.setDate(nextDay.getDate() + 1)
        endTime = new Date(nextDay.getTime() + (segment.end - 1440) * 60 * 1000)
      } else {
        // 结束时间在当天
        endTime = new Date(baseDate.getTime() + segment.end * 60 * 1000)
      }

      // 如果是跨日期时间段，需要调整时间比较逻辑
      const isCrossDate = segment.start >= 1440 || segment.end >= 1440

      // 计算时间段的时薪
      const segmentDurationHours = (segment.end - segment.start) / 60
      const segmentHourlyRate = segmentDurationHours > 0 ? (segment.income || 0) / segmentDurationHours : 0

      // 判断当前时间是否在时间段内（考虑跨日期情况）
      let isInSegment = false
      if (isCrossDate && segment.start >= 1440) {
        // 整个时间段都在次日
        isInSegment = currentTime >= startTime && currentTime <= endTime
      } else if (isCrossDate && segment.start < 1440) {
        // 时间段跨越两天（如昨天22:00-今天01:00）
        // 这种情况在跨日期数据中不应该出现，因为我们使用的是昨天的数据
        isInSegment = currentTime >= startTime && currentTime <= endTime
      } else {
        // 普通时间段
        isInSegment = currentTime >= startTime && currentTime <= endTime
      }

      if (isInSegment) {
        // 当前正在进行的时间段
        currentSegmentIndex = index
        isWorking = true
        currentHourlyRate = segmentHourlyRate

        // 计算已工作的时间（从时间段开始到当前时间）
        const workedMs = currentTime.getTime() - startTime.getTime()
        const workedHours = Math.max(0, workedMs / (1000 * 60 * 60))

        // 计算该时间段内的摸鱼总时间
        const fishingHours = calculateFishingTimeInSegment(segment, startTime, endTime, currentTime, isFishing, fishingState)

        // 实际工作时间 = 已工作时间 - 摸鱼时间
        const actualWorkedHours = Math.max(0, workedHours - fishingHours)
        totalWorkedHours += actualWorkedHours

        // 工作收入 = 实际工作时间 × 时薪
        const segmentIncome = actualWorkedHours * segmentHourlyRate
        totalIncome += segmentIncome
      } else if (currentTime > endTime || (isCrossDate && segment.start >= 1440 && currentTime < startTime)) {
        // 已完成的时间段
        const workedMs = endTime.getTime() - startTime.getTime()
        const workedHours = Math.max(0, workedMs / (1000 * 60 * 60))

        // 计算该时间段内的摸鱼总时间
        const fishingHours = calculateFishingTimeInSegment(segment, startTime, endTime, currentTime, isFishing, fishingState)

        // 实际工作时间 = 时间段总时间 - 摸鱼时间
        const actualWorkedHours = Math.max(0, workedHours - fishingHours)
        totalWorkedHours += actualWorkedHours

        // 工作收入 = 实际工作时间 × 时薪
        const segmentIncome = actualWorkedHours * segmentHourlyRate
        totalIncome += segmentIncome
      }
    })

    // 如果不在工作时间，但需要显示时薪信息，使用最近的时间段的时薪
    if (!isWorking && currentHourlyRate === 0) {
      // 查找最近的工作时间段
      let nearestSegment = null
      let minDistance = Infinity

      workData.segments.forEach(segment => {
        if (segment.type === 'rest') return

        const segmentStart = segment.start
        const segmentEnd = segment.end
        let distance

        if (currentMinutes < segmentStart) {
          // 当前时间在时间段之前
          distance = segmentStart - currentMinutes
        } else if (currentMinutes > segmentEnd) {
          // 当前时间在时间段之后
          distance = currentMinutes - segmentEnd
        } else {
          // 当前时间在时间段内（这种情况应该已经在上面处理了）
          distance = 0
        }

        if (distance < minDistance) {
          minDistance = distance
          nearestSegment = segment
        }
      })

      if (nearestSegment) {
        currentHourlyRate = nearestSegment.hourlyRate || 0
      }
    }

    return {
      totalIncome,
      totalWorkedHours,
      currentSegmentIndex,
      isWorking,
      currentHourlyRate
    }
  }

  /**
   * 格式化收入
   */
  formatIncome(income) {
    if (isNaN(income) || income < 0) {
      return '0.' + '0'.repeat(this.decimalPlaces)
    }
    return income.toFixed(this.decimalPlaces)
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      decimalPlaces: this.decimalPlaces,
      updateInterval: this.updateInterval,
      hasTimer: !!this.timer
    }
  }

  /**
   * 找到摸鱼时间对应的工作时间段
   * @param {Object} fish - 摸鱼记录
   * @param {Array} segments - 工作时间段数组
   * @returns {Object|null} 对应的工作时间段
   */
  findCorrespondingWorkSegment(fish, segments) {
    if (!segments || segments.length === 0) return null

    return segments.find(segment => {
      return segment.type !== 'rest' &&
             fish.start >= segment.start &&
             fish.end <= segment.end
    })
  }

  /**
   * 计算时间段的时薪
   * @param {Object} segment - 时间段
   * @returns {number} 时薪
   */
  calculateSegmentHourlyRate(segment) {
    if (!segment || segment.type === 'rest') return 0

    const duration = segment.end - segment.start
    const hours = duration / 60

    return hours > 0 ? (segment.income || 0) / hours : 0
  }
}

/**
 * 跨日期实时收入计算器
 * 专门处理跨日期时间段的实时收入计算
 */
class CrossDateRealTimeIncome {
  constructor() {
    this.timer = null
    this.decimalPlaces = 2
    this.updateInterval = 1000
    this.isRunning = false
    this.currentHourlyRate = 0
  }

  /**
   * 设置小数位数
   */
  setDecimalPlaces(places) {
    const oldDecimalPlaces = this.decimalPlaces
    this.decimalPlaces = Math.max(0, Math.min(3, places !== undefined ? places : 2))

    // 如果小数位数改变且正在运行，重新计算刷新间隔
    if (oldDecimalPlaces !== this.decimalPlaces && this.isRunning && this.currentHourlyRate > 0) {
      this.updateInterval = this.calculateOptimalUpdateInterval(this.currentHourlyRate)
      console.log('[DEBUG] CrossDate 重新计算更新间隔:', this.updateInterval)
    }
  }

  /**
   * 计算最优更新间隔
   * @param {number} hourlyRate - 时薪
   * @returns {number} 更新间隔（毫秒）
   */
  calculateOptimalUpdateInterval(hourlyRate) {
    if (!hourlyRate || hourlyRate <= 0) {
      return 1000 // 默认1秒
    }

    // 计算每毫秒的收入增长
    const incomePerMs = hourlyRate / (60 * 60 * 1000)

    // 计算最小显示单位（根据小数位数）
    const minDisplayUnit = Math.pow(10, -this.decimalPlaces)

    // 计算达到最小显示单位需要的时间（毫秒）
    const msForMinUnit = minDisplayUnit / incomePerMs

    // 设置更新间隔为达到最小单位时间的 95%
    let optimalInterval = Math.floor(msForMinUnit * 0.95)

    // 限制在合理范围内：最小8ms，最大1000ms
    optimalInterval = Math.max(8, Math.min(1000, optimalInterval))

    return optimalInterval
  }

  /**
   * 启动跨日期实时收入更新
   * @param {Function} callback - 更新回调
   * @param {Object} options - 配置选项
   */
  start(callback, options = {}) {
    this.stop() // 先停止之前的更新

    const {
      currentWorkDate,     // 当前工作日期
      currentWorkId,       // 当前工作ID
      baseService,         // 基础服务
      calculateCrossDateIncome // 跨日期收入计算函数
    } = options

    // 验证必要参数
    if (!callback || typeof callback !== 'function') {
      console.error('[CrossDateRealTimeIncome] 回调函数无效')
      return
    }

    if (!currentWorkDate || !currentWorkId || !baseService || !calculateCrossDateIncome) {
      console.error('[CrossDateRealTimeIncome] 必要参数缺失:', {
        hasCurrentWorkDate: !!currentWorkDate,
        hasCurrentWorkId: !!currentWorkId,
        hasBaseService: !!baseService,
        hasCalculateFunction: !!calculateCrossDateIncome
      })
      return
    }

    this.isRunning = true

    // 获取初始时薪并计算初始更新间隔
    const initialHourlyRate = this.getCurrentSegmentHourlyRate(currentWorkDate, currentWorkId, baseService)
    this.currentHourlyRate = initialHourlyRate
    this.updateInterval = this.calculateOptimalUpdateInterval(initialHourlyRate)

    console.log('[CrossDateRealTimeIncome] 启动跨日期实时更新，初始时薪:', initialHourlyRate, '更新间隔:', this.updateInterval + 'ms')

    const updateLoop = () => {
      if (!this.isRunning) return

      try {
        // 重新计算跨日期收入
        const workDayData = calculateCrossDateIncome(currentWorkDate)

        // 计算当前时薪
        const currentHourlyRate = this.getCurrentSegmentHourlyRate(currentWorkDate, currentWorkId, baseService)

        // 检查时薪是否变化（可能进入了新的时间段）
        if (currentHourlyRate !== this.currentHourlyRate) {
          this.currentHourlyRate = currentHourlyRate
          const newInterval = this.calculateOptimalUpdateInterval(this.currentHourlyRate)

          if (newInterval !== this.updateInterval) {
            this.updateInterval = newInterval
            console.log('[CrossDateRealTimeIncome] 时薪变化，更新刷新间隔:', this.updateInterval + 'ms')
          }
        }

        // 构造结果
        const result = {
          dailyIncome: workDayData.dailyIncome,
          workIncome: workDayData.workIncome,
          fishingIncome: workDayData.fishingIncome,
          currentHourlyRate: currentHourlyRate,
          formattedDailyIncome: this.formatIncome(workDayData.dailyIncome),
          formattedWorkIncome: this.formatIncome(workDayData.workIncome),
          formattedFishingIncome: this.formatIncome(workDayData.fishingIncome),
          formattedHourlyRate: currentHourlyRate.toFixed(2)
        }

        callback(result)

        // 继续更新
        if (this.isRunning) {
          this.timer = setTimeout(updateLoop, this.updateInterval)
        }
      } catch (error) {
        console.error('[CrossDateRealTimeIncome] 更新循环错误:', error)
        this.isRunning = false
      }
    }

    // 立即执行第一次更新
    updateLoop()
  }

  /**
   * 停止实时更新
   */
  stop() {
    this.isRunning = false
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
    console.log('[CrossDateRealTimeIncome] 跨日期实时更新已停止')
  }

  /**
   * 获取当前时间段的时薪
   * @param {Date} currentWorkDate - 当前工作日期
   * @param {string} currentWorkId - 当前工作ID
   * @param {Object} baseService - 基础服务
   * @returns {number} 当前时薪
   */
  getCurrentSegmentHourlyRate(currentWorkDate, currentWorkId, baseService) {
    try {
      const now = new Date()
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const currentMs = now.getTime() - todayStart.getTime()
      const yesterdayData = baseService.getTodayData(currentWorkDate, currentWorkId)

      if (!yesterdayData.segments) {
        return 0
      }

      // 找到当前时间所在的跨日期时间段
      for (const segment of yesterdayData.segments) {
        const isCrossDate = segment.start >= 1440 || segment.end >= 1440
        if (isCrossDate && segment.start >= 1440) {
          const startTimeTodayMs = (segment.start - 1440) * 60 * 1000
          const endTimeTodayMs = (segment.end >= 1440 ? segment.end - 1440 : segment.end) * 60 * 1000
          if (currentMs >= startTimeTodayMs && currentMs <= endTimeTodayMs) {
            // 计算当前时间段的时薪
            const segmentDurationHours = (segment.end - segment.start) / 60
            return segmentDurationHours > 0 ? (segment.income || 0) / segmentDurationHours : 0
          }
        }
      }

      return 0
    } catch (error) {
      console.error('[CrossDateRealTimeIncome] 获取当前时薪失败:', error)
      return 0
    }
  }

  /**
   * 格式化收入
   */
  formatIncome(income) {
    if (isNaN(income) || income < 0) {
      return '0.' + '0'.repeat(this.decimalPlaces)
    }
    return income.toFixed(this.decimalPlaces)
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      decimalPlaces: this.decimalPlaces,
      updateInterval: this.updateInterval,
      currentHourlyRate: this.currentHourlyRate,
      hasTimer: !!this.timer
    }
  }
}

module.exports = {
  SimpleRealTimeIncome,
  CrossDateRealTimeIncome
}
