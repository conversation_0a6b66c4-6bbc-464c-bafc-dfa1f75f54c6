/**
 * 时间工具函数
 * 提供时间格式化、计算等功能
 */

/**
 * 格式化时间为 HH:MM 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
  if (!date || !(date instanceof Date)) {
    return '00:00'
  }
  
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * 格式化时间为 HH:MM:SS 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimeHHMMSS(date) {
  if (!date || !(date instanceof Date)) {
    return '00:00:00'
  }
  
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期为 YYYY年MM月DD日 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return `${year}年${month}月${day}日`
}

/**
 * 获取日期对应的星期几
 * @param {Date} date - 日期对象
 * @returns {string} 星期几的中文表示
 */
export function getWeekday(date) {
  if (!date || !(date instanceof Date)) {
    return '未知'
  }

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[date.getDay()]
}

/**
 * 格式化日期为 YYYY年MM月DD日 - 周X 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串，包含星期几
 */
export function formatDateWithWeekday(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }

  const dateStr = formatDate(date)
  const weekday = getWeekday(date)

  return `${dateStr} - ${weekday}`
}

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDateToYYYYMMDD(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化日期为存储键格式 YYYY-MM-DD
 * @param {Date} date - 日期对象
 * @returns {string} 存储键格式的日期字符串
 */
export function formatDateKey(date) {
  if (!date || !(date instanceof Date)) {
    return ''
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化持续时间
 * @param {number} minutes - 分钟数
 * @returns {string} 格式化后的持续时间字符串
 */
export function formatDuration(minutes) {
  if (!minutes || minutes <= 0) {
    return '0分钟'
  }
  
  const hours = Math.floor(minutes / 60)
  const mins = Math.round(minutes % 60)
  
  if (hours === 0) {
    return `${mins}分钟`
  } else if (mins === 0) {
    return `${hours}小时`
  } else {
    return `${hours}小时${mins}分钟`
  }
}

/**
 * 计算时间段的收入
 * @param {Object} segment - 时间段对象
 * @returns {number} 收入金额
 */
export function calculateIncome(segment) {
  if (!segment || segment.type === 'rest') {
    return 0
  }
  
  return segment.income || 0
}

/**
 * 计算时间段的时薪
 * @param {Object} segment - 时间段对象
 * @returns {number} 时薪
 */
export function calculateHourlyRate(segment) {
  if (!segment || segment.type === 'rest' || !segment.income) {
    return 0
  }

  const hours = (segment.end - segment.start) / 60
  return hours > 0 ? segment.income / hours : 0
}

/**
 * 获取日期的开始时间（00:00:00）
 * @param {Date} date - 日期对象
 * @returns {Date} 日期开始时间
 */
export function getDateStart(date) {
  const start = new Date(date)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 获取日期的结束时间（23:59:59.999）
 * @param {Date} date - 日期对象
 * @returns {Date} 日期结束时间
 */
export function getDateEnd(date) {
  const end = new Date(date)
  end.setHours(23, 59, 59, 999)
  return end
}

/**
 * 获取当周的开始日期（周日）
 * @param {Date} date - 基准日期
 * @returns {Date} 周开始日期
 */
export function getWeekStart(date) {
  const start = new Date(date)
  const day = start.getDay()
  start.setDate(start.getDate() - day)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 获取当月的开始日期
 * @param {Date} date - 基准日期
 * @returns {Date} 月开始日期
 */
export function getMonthStart(date) {
  const start = new Date(date)
  start.setDate(1)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 检查两个日期是否是同一天
 * @param {Date} date1 - 第一个日期
 * @param {Date} date2 - 第二个日期
 * @returns {boolean} 是否是同一天
 */
export function isSameDay(date1, date2) {
  if (!date1 || !date2) {
    return false
  }
  
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate()
}

/**
 * 解析时间字符串为分钟数
 * @param {string} timeStr - 时间字符串 "HH:MM"
 * @returns {number} 分钟数
 */
export function parseTimeToMinutes(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0
  }
  
  const [hours, minutes] = timeStr.split(':').map(Number)
  return (hours || 0) * 60 + (minutes || 0)
}

/**
 * 将分钟数转换为时间字符串
 * @param {number} minutes - 分钟数
 * @returns {string} 时间字符串 "HH:MM"
 */
export function minutesToTimeString(minutes) {
  const hours = Math.floor(minutes / 60)
  const mins = Math.round(minutes % 60)
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * 获取今天的日期
 * @returns {Date} 今天的日期
 */
export function getToday() {
  return getDateStart(new Date())
}

/**
 * 格式化文件名用的日期字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 YYYYMMDD
 */
export function formatDateForFilename(date) {
  if (!date || !(date instanceof Date)) {
    return ''
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}${month}${day}`
}

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 - 开始日期
 * @param {Date} date2 - 结束日期
 * @returns {number} 天数差
 */
export function daysBetween(date1, date2) {
  const oneDay = 24 * 60 * 60 * 1000 // 一天的毫秒数
  const firstDate = new Date(date1)
  const secondDate = new Date(date2)
  
  firstDate.setHours(0, 0, 0, 0)
  secondDate.setHours(0, 0, 0, 0)
  
  return Math.round((secondDate - firstDate) / oneDay)
}

/**
 * 获取指定日期所在月份的所有日期
 * @param {Date} date - 基准日期
 * @returns {Array<Date>} 月份中的所有日期
 */
export function getMonthDates(date) {
  const year = date.getFullYear()
  const month = date.getMonth()
  
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  const dates = []
  
  for (let day = 1; day <= lastDay.getDate(); day++) {
    dates.push(new Date(year, month, day))
  }
  
  return dates
}

/**
 * 验证时间字符串格式
 * @param {string} timeStr - 时间字符串
 * @returns {boolean} 是否为有效格式
 */
export function isValidTimeString(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return false
  }
  
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(timeStr)
}

/**
 * 比较两个时间字符串
 * @param {string} time1 - 第一个时间字符串
 * @param {string} time2 - 第二个时间字符串
 * @returns {number} -1, 0, 1 分别表示 time1 < time2, time1 === time2, time1 > time2
 */
export function compareTimeStrings(time1, time2) {
  const minutes1 = parseTimeToMinutes(time1)
  const minutes2 = parseTimeToMinutes(time2)

  if (minutes1 < minutes2) return -1
  if (minutes1 > minutes2) return 1
  return 0
}

/**
 * 创建跨日时间对象
 * @param {Date} baseDate - 基准日期
 * @param {string} timeStr - 时间字符串 "HH:MM"
 * @param {boolean} isNextDay - 是否为次日时间
 * @returns {Date} 时间对象
 */
export function createCrossDayTime(baseDate, timeStr, isNextDay = false) {
  if (!baseDate || !timeStr) {
    return null
  }

  const [hours, minutes] = timeStr.split(':').map(Number)
  const date = new Date(baseDate)

  if (isNextDay) {
    date.setDate(date.getDate() + 1)
  }

  date.setHours(hours || 0, minutes || 0, 0, 0)
  return date
}

/**
 * 格式化跨日时间显示
 * @param {Date} time - 时间对象
 * @param {Date} baseDate - 基准日期
 * @returns {string} 格式化的时间字符串，如 "23:30" 或 "次日 02:30"
 */
export function formatCrossDayTime(time, baseDate) {
  if (!time || !baseDate) {
    return '00:00'
  }

  const timeStr = formatTime(time)
  const isNextDay = !isSameDay(time, baseDate)

  return isNextDay ? `次日 ${timeStr}` : timeStr
}

/**
 * 检查时间是否为次日
 * @param {Date} time - 时间对象
 * @param {Date} baseDate - 基准日期
 * @returns {boolean} 是否为次日
 */
export function isNextDayTime(time, baseDate) {
  if (!time || !baseDate) {
    return false
  }

  return time.getDate() !== baseDate.getDate() ||
         time.getMonth() !== baseDate.getMonth() ||
         time.getFullYear() !== baseDate.getFullYear()
}

/**
 * 计算跨日时间段的持续时间（分钟）
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间
 * @returns {number} 持续时间（分钟）
 */
export function calculateCrossDayDuration(startTime, endTime) {
  if (!startTime || !endTime) {
    return 0
  }

  const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60)
  return Math.max(0, duration)
}

/**
 * 排序时间段（支持跨日）
 * @param {Array} segments - 时间段数组
 * @returns {Array} 排序后的时间段数组
 */
export function sortSegmentsByTime(segments) {
  if (!Array.isArray(segments)) {
    return []
  }

  return segments.slice().sort((a, b) => {
    // 首先按开始时间排序
    const startDiff = a.start.getTime() - b.start.getTime()
    if (startDiff !== 0) {
      return startDiff
    }

    // 如果开始时间相同，按结束时间排序
    return a.end.getTime() - b.end.getTime()
  })
}

/**
 * 验证跨日时间段的有效性
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间
 * @param {Date} baseDate - 基准日期
 * @returns {Object} 验证结果 { isValid: boolean, error: string }
 */
export function validateCrossDayTimeSegment(startTime, endTime, baseDate) {
  if (!startTime || !endTime || !baseDate) {
    return { isValid: false, error: '时间参数不完整' }
  }

  // 检查时间段是否超过48小时
  const duration = calculateCrossDayDuration(startTime, endTime)
  if (duration > 48 * 60) {
    return { isValid: false, error: '时间段不能超过48小时' }
  }

  // 检查开始时间是否早于结束时间
  if (startTime.getTime() >= endTime.getTime()) {
    return { isValid: false, error: '开始时间必须早于结束时间' }
  }

  return { isValid: true, error: null }
}


/**
 * 标准化时间为秒级精度
 * 解决本地毫秒精度与云端秒级精度不一致的问题
 * @param {Date|string} date - 日期对象或ISO字符串
 * @returns {string} 标准化的ISO字符串（秒级精度）
 */
export function normalizeTime(date = new Date()) {
  const dateObj = date instanceof Date ? date : new Date(date)
  // 将毫秒设为0，确保秒级精度
  dateObj.setMilliseconds(0)
  return dateObj.toISOString()
}

/**
 * 标准化时间戳为秒级精度
 * @param {string|Date} time - 时间字符串或Date对象
 * @returns {number} 秒级时间戳
 */
export function normalizeTimestamp(time) {
  if (!time) return 0
  const date = time instanceof Date ? time : new Date(time)
  // 转换为秒级时间戳，忽略毫秒差异
  return Math.floor(date.getTime() / 1000)
}

/**
 * 格式化刷新时间显示
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatRefreshTime(timestamp) {
  if (!timestamp) return '未知'

  try {
    const date = new Date(timestamp)
    const now = new Date()

    // 如果是今天，只显示时间
    if (isSameDay(date, now)) {
      return formatTimeHHMMSS(date)
    } else {
      // 如果不是今天，显示日期和时间
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const time = formatTime(date)
      return `${month}/${day} ${time}`
    }
  } catch (error) {
    console.error('时间格式化失败:', error)
    return '格式错误'
  }
}
