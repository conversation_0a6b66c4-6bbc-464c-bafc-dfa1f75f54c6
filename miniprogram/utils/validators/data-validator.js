/**
 * 数据验证工具
 * 提供各种数据验证方法
 */

class DataValidator {
  /**
   * 验证是否为有效的日期
   * @param {any} date - 要验证的日期
   * @returns {boolean} 是否为有效日期
   */
  static isValidDate(date) {
    if (!date) return false
    const d = new Date(date)
    return d instanceof Date && !isNaN(d.getTime())
  }

  /**
   * 验证是否为正数
   * @param {any} value - 要验证的值
   * @returns {boolean} 是否为正数
   */
  static isPositiveNumber(value) {
    const num = Number(value)
    return !isNaN(num) && num >= 0
  }

  /**
   * 验证字符串是否非空
   * @param {any} str - 要验证的字符串
   * @returns {boolean} 是否为非空字符串
   */
  static isNonEmptyString(str) {
    return typeof str === 'string' && str.trim().length > 0
  }

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱地址
   * @returns {boolean} 是否为有效邮箱
   */
  static isValidEmail(email) {
    if (!email || typeof email !== 'string') return false
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 验证手机号格式（中国大陆）
   * @param {string} phone - 手机号
   * @returns {boolean} 是否为有效手机号
   */
  static isValidPhone(phone) {
    if (!phone || typeof phone !== 'string') return false
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证身份证号格式（中国大陆）
   * @param {string} idCard - 身份证号
   * @returns {boolean} 是否为有效身份证号
   */
  static isValidIdCard(idCard) {
    if (!idCard || typeof idCard !== 'string') return false
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }

  /**
   * 验证银行卡号格式
   * @param {string} bankCard - 银行卡号
   * @returns {boolean} 是否为有效银行卡号
   */
  static isValidBankCard(bankCard) {
    if (!bankCard || typeof bankCard !== 'string') return false
    const bankCardRegex = /^\d{16,19}$/
    return bankCardRegex.test(bankCard)
  }

  /**
   * 验证URL格式
   * @param {string} url - URL地址
   * @returns {boolean} 是否为有效URL
   */
  static isValidUrl(url) {
    if (!url || typeof url !== 'string') return false
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证IP地址格式
   * @param {string} ip - IP地址
   * @returns {boolean} 是否为有效IP地址
   */
  static isValidIP(ip) {
    if (!ip || typeof ip !== 'string') return false
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    return ipRegex.test(ip)
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @param {Object} options - 验证选项
   * @returns {Object} 验证结果
   */
  static validatePassword(password, options = {}) {
    const {
      minLength = 8,
      requireUppercase = true,
      requireLowercase = true,
      requireNumbers = true,
      requireSpecialChars = true
    } = options

    const result = {
      isValid: true,
      errors: [],
      strength: 0
    }

    if (!password || typeof password !== 'string') {
      result.isValid = false
      result.errors.push('密码不能为空')
      return result
    }

    if (password.length < minLength) {
      result.isValid = false
      result.errors.push(`密码长度至少${minLength}位`)
    } else {
      result.strength += 1
    }

    if (requireUppercase && !/[A-Z]/.test(password)) {
      result.isValid = false
      result.errors.push('密码必须包含大写字母')
    } else if (/[A-Z]/.test(password)) {
      result.strength += 1
    }

    if (requireLowercase && !/[a-z]/.test(password)) {
      result.isValid = false
      result.errors.push('密码必须包含小写字母')
    } else if (/[a-z]/.test(password)) {
      result.strength += 1
    }

    if (requireNumbers && !/\d/.test(password)) {
      result.isValid = false
      result.errors.push('密码必须包含数字')
    } else if (/\d/.test(password)) {
      result.strength += 1
    }

    if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.isValid = false
      result.errors.push('密码必须包含特殊字符')
    } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.strength += 1
    }

    return result
  }

  /**
   * 验证中文姓名
   * @param {string} name - 姓名
   * @returns {boolean} 是否为有效中文姓名
   */
  static isValidChineseName(name) {
    if (!name || typeof name !== 'string') return false
    const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/
    return chineseNameRegex.test(name)
  }

  /**
   * 验证年龄范围
   * @param {number} age - 年龄
   * @param {number} min - 最小年龄
   * @param {number} max - 最大年龄
   * @returns {boolean} 是否在有效年龄范围内
   */
  static isValidAge(age, min = 0, max = 150) {
    const ageNum = Number(age)
    return !isNaN(ageNum) && ageNum >= min && ageNum <= max
  }
}

module.exports = {
  DataValidator
}
